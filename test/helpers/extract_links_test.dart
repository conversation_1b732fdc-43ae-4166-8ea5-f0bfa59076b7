import 'package:flutter_test/flutter_test.dart';
import 'package:goldenprizma/helpers/extract_links.dart';

void main() {
  group('extractLinks', () {
    test('should extract single URL from text', () {
      const text = 'Check out this product: https://example.com/product/123';
      final result = extractLinks(text);
      
      expect(result, hasLength(1));
      expect(result.first, equals('https://example.com/product/123'));
    });

    test('should extract multiple URLs from text', () {
      const text = 'Visit https://example.com and also https://another.com/page';
      final result = extractLinks(text);
      
      expect(result, hasLength(2));
      expect(result, contains('https://example.com'));
      expect(result, contains('https://another.com/page'));
    });

    test('should extract URL with www prefix', () {
      const text = 'Go to www.example.com/product';
      final result = extractLinks(text);
      
      expect(result, hasLength(1));
      expect(result.first, equals('www.example.com/product'));
    });

    test('should handle text with no URLs', () {
      const text = 'This is just regular text without any links';
      final result = extractLinks(text);
      
      expect(result, isEmpty);
    });

    test('should extract URL from mixed content', () {
      const text = 'Product name: Amazing Widget\nPrice: \$29.99\nLink: https://shop.example.com/widget/amazing\nDescription: Great product!';
      final result = extractLinks(text);
      
      expect(result, hasLength(1));
      expect(result.first, equals('https://shop.example.com/widget/amazing'));
    });

    test('should handle URL with query parameters', () {
      const text = 'Check this: https://example.com/product?id=123&color=red&size=large';
      final result = extractLinks(text);
      
      expect(result, hasLength(1));
      expect(result.first, equals('https://example.com/product?id=123&color=red&size=large'));
    });
  });
}
