import 'package:flutter_test/flutter_test.dart';
import 'package:goldenprizma/helpers/user_agent_manager.dart';

void main() {
  group('UserAgentManager', () {
    late UserAgentManager userAgentManager;

    setUp(() {
      userAgentManager = UserAgentManager();
      userAgentManager.resetSession(); // Reset for each test
    });

    test('should return a valid user agent', () {
      final userAgent = userAgentManager.getRandomUserAgent();

      expect(userAgent, isNotEmpty);
      expect(userAgent, contains('Mozilla'));
      expect(
          userAgent,
          anyOf([
            contains('iPhone'),
            contains('iPad'),
            contains('Android'),
          ]));
    });

    test('should return consistent session user agent', () {
      final userAgent1 = userAgentManager.getSessionUserAgent();
      final userAgent2 = userAgentManager.getSessionUserAgent();

      expect(userAgent1, equals(userAgent2));
      expect(userAgent1, isNotEmpty);
    });

    test('should return different user agents for different sessions', () {
      final userAgent1 = userAgentManager.getSessionUserAgent();
      userAgentManager.resetSession();
      final userAgent2 = userAgentManager.getSessionUserAgent();

      // Note: There's a small chance they could be the same by random chance
      // but with 20+ user agents, it's very unlikely
      expect(userAgent1, isNotEmpty);
      expect(userAgent2, isNotEmpty);
    });

    test('should return iOS Safari user agent for Shein', () {
      final userAgent = userAgentManager
          .getUserAgentForWebsite('https://m.shein.com/product');

      expect(userAgent, contains('iPhone'));
      expect(userAgent, contains('Safari'));
      expect(userAgent, isNot(contains('CriOS'))); // Not Chrome on iOS
      expect(userAgent, isNot(contains('FxiOS'))); // Not Firefox on iOS
    });

    test('should return Chrome user agent for Amazon', () {
      final userAgent = userAgentManager
          .getUserAgentForWebsite('https://www.amazon.com/product');

      expect(userAgent, contains('Chrome'));
      expect(userAgent, isNot(contains('EdgA'))); // Not Edge
    });

    test('should return Android Chrome user agent for AliExpress', () {
      final userAgent = userAgentManager
          .getUserAgentForWebsite('https://www.aliexpress.com/item');

      expect(userAgent, contains('Android'));
      expect(userAgent, contains('Chrome'));
      expect(userAgent, isNot(contains('Samsung'))); // Not Samsung Browser
      expect(userAgent, isNot(contains('EdgA'))); // Not Edge
    });

    test('should return random user agent for unknown websites', () {
      final userAgent =
          userAgentManager.getUserAgentForWebsite('https://example.com');

      expect(userAgent, isNotEmpty);
      expect(userAgent, contains('Mozilla'));
    });

    test('should handle invalid URLs gracefully', () {
      final userAgent = userAgentManager.getUserAgentForWebsite('not-a-url');

      expect(userAgent, isNotEmpty);
      expect(userAgent, contains('Mozilla'));
    });

    test('should return anti-detection headers', () {
      final headers = userAgentManager.getAntiDetectionHeaders();

      expect(headers, isNotEmpty);
      expect(headers, containsPair('Accept', contains('text/html')));
      expect(headers, containsPair('Accept-Language', contains('en-US')));
      expect(headers, containsPair('DNT', '1'));
      expect(headers, containsPair('Upgrade-Insecure-Requests', '1'));
    });

    test('should track current user agent', () {
      expect(userAgentManager.getCurrentUserAgent(), isNull);

      final userAgent = userAgentManager.getSessionUserAgent();
      expect(userAgentManager.getCurrentUserAgent(), equals(userAgent));

      userAgentManager.resetSession();
      expect(userAgentManager.getCurrentUserAgent(), isNull);
    });

    test('should generate new session user agent', () {
      final userAgent1 = userAgentManager.getSessionUserAgent();
      final userAgent2 = userAgentManager.getNewSessionUserAgent();

      expect(userAgent1, isNotEmpty);
      expect(userAgent2, isNotEmpty);
      expect(userAgentManager.getCurrentUserAgent(), equals(userAgent2));
    });

    test('all user agents should be mobile-friendly', () {
      // Test multiple random user agents to ensure they're all mobile
      for (int i = 0; i < 10; i++) {
        final userAgent = userAgentManager.getRandomUserAgent();

        expect(
            userAgent,
            anyOf([
              contains('Mobile'),
              contains('iPhone'),
              contains('iPad'),
              contains('Android'),
            ]),
            reason: 'User agent should be mobile-friendly: $userAgent');
      }
    });

    test('should randomize user agent versions', () {
      const baseUserAgent =
          'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1';

      final randomized1 =
          userAgentManager.getRandomizedUserAgent(baseUserAgent);
      final randomized2 =
          userAgentManager.getRandomizedUserAgent(baseUserAgent);

      expect(randomized1, isNotEmpty);
      expect(randomized2, isNotEmpty);
      expect(randomized1, contains('Mozilla'));
      expect(randomized2, contains('Mozilla'));

      // They might be different due to randomization
      expect(randomized1, contains('iPhone'));
      expect(randomized2, contains('iPhone'));
    });

    test('should detect blocked user agents', () {
      expect(userAgentManager.isUserAgentLikelyBlocked('HeadlessChrome/1.0'),
          isTrue);
      expect(userAgentManager.isUserAgentLikelyBlocked('Mozilla/5.0 PhantomJS'),
          isTrue);
      expect(userAgentManager.isUserAgentLikelyBlocked('Selenium WebDriver'),
          isTrue);
      expect(
          userAgentManager.isUserAgentLikelyBlocked('GoogleBot/1.0'), isTrue);

      // Normal user agents should not be flagged
      expect(
          userAgentManager.isUserAgentLikelyBlocked(
              'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15'),
          isFalse);
    });

    test('should handle Chrome version randomization', () {
      const chromeUserAgent =
          'Mozilla/5.0 (Linux; Android 13; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36';

      final randomized =
          userAgentManager.getRandomizedUserAgent(chromeUserAgent);

      expect(randomized, contains('Chrome/'));
      expect(randomized, contains('Android'));
      expect(randomized, contains('Mobile'));

      // Should have a different Chrome version
      expect(
          randomized,
          anyOf([
            contains('Chrome/*********'),
            contains('Chrome/*********'),
            contains('Chrome/*********'),
            contains('Chrome/*********'),
          ]));
    });

    test('should handle Safari version randomization', () {
      const safariUserAgent =
          'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1';

      final randomized =
          userAgentManager.getRandomizedUserAgent(safariUserAgent);

      expect(randomized, contains('Version/'));
      expect(randomized, contains('iPhone'));
      expect(randomized, contains('Safari'));

      // Should have a different Safari version
      expect(
          randomized,
          anyOf([
            contains('Version/17.1'),
            contains('Version/17.0'),
            contains('Version/16.6'),
            contains('Version/16.5'),
          ]));
    });
  });
}
