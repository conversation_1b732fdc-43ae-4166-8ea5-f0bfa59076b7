import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/websites/bloc/website_bloc.dart';
import 'package:goldenprizma/presentation/views/screens/website_launcher.dart';
import 'package:goldenprizma/repositories/website_repository.dart';

void main() {
  group('WebsiteLauncher', () {
    late WebsiteRepository mockRepository;

    setUp(() {
      mockRepository = WebsiteRepository();
    });

    testWidgets('should not show floating action button in minimal design mode',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider(
            create: (context) => WebsiteBloc(mockRepository),
            child: WebsiteLauncher(
              url: 'https://example.com',
              websiteName: 'Test Website',
              minimalDesign: true,
            ),
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Verify that no floating action button is present
      expect(find.byType(MaterialButton), findsNothing);
      expect(find.byIcon(Icons.add), findsNothing);
    });

    testWidgets('should show floating action button in normal mode',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider(
            create: (context) => WebsiteBloc(mockRepository),
            child: WebsiteLauncher(
              url: 'https://example.com',
              websiteName: 'Test Website',
              minimalDesign: false,
            ),
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Verify that floating action button is present
      expect(find.byType(MaterialButton), findsOneWidget);
    });

    testWidgets('should not cause semantics errors in minimal design mode',
        (WidgetTester tester) async {
      // This test ensures no semantics exceptions are thrown
      bool hasSemanticError = false;

      FlutterError.onError = (FlutterErrorDetails details) {
        if (details.toString().contains('non-finite rect') ||
            details.toString().contains('SemanticsNode')) {
          hasSemanticError = true;
        }
      };

      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider(
            create: (context) => WebsiteBloc(mockRepository),
            child: WebsiteLauncher(
              url: 'https://example.com',
              websiteName: 'Test Website',
              minimalDesign: true,
            ),
          ),
        ),
      );

      // Wait for the widget to build and settle
      await tester.pumpAndSettle();

      // Verify no semantic errors occurred
      expect(hasSemanticError, false);

      // Reset error handler
      FlutterError.onError = null;
    });

    testWidgets('should handle page action advertisements correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider(
            create: (context) => WebsiteBloc(mockRepository),
            child: WebsiteLauncher(
              url: 'https://golden.test/pages/cerave',
              websiteName: '',
              minimalDesign: true, // This is what page actions use
            ),
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Verify the widget builds without errors
      expect(find.byType(WebsiteLauncher), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);

      // Verify no floating action button in minimal mode
      expect(find.byType(MaterialButton), findsNothing);
    });
  });
}
