import 'package:flutter_test/flutter_test.dart';
import 'package:goldenprizma/domain/store/cubit/store_filter_cubit.dart';
import 'package:goldenprizma/domain/store/models/brand.dart';

void main() {
  group('StoreFilterCubit', () {
    late StoreFilterCubit cubit;

    setUp(() {
      cubit = StoreFilterCubit();
    });

    tearDown(() {
      cubit.close();
    });

    test('initial state is correct', () {
      expect(cubit.state, const StoreFilterState());
      expect(cubit.state.selectedBrandId, isNull);
      expect(cubit.state.selectedBrand, isNull);
      expect(cubit.state.selectedCategoryId, isNull);
      expect(cubit.state.isOutletSelected, false);
      expect(cubit.state.isPromotionSelected, false);
    });

    test('selectBrand updates brand and clears outlet', () {
      final brand = Brand(id: 1, name: 'Test Brand', isTop: true, sortOrder: 1);
      
      // First select outlet
      cubit.toggleOutlet();
      expect(cubit.state.isOutletSelected, true);
      
      // Then select brand - should clear outlet
      cubit.selectBrand(1, brand);
      
      expect(cubit.state.selectedBrandId, 1);
      expect(cubit.state.selectedBrand, brand);
      expect(cubit.state.isOutletSelected, false); // Should be cleared
    });

    test('toggleOutlet clears brand selection', () {
      final brand = Brand(id: 1, name: 'Test Brand', isTop: true, sortOrder: 1);
      
      // First select brand
      cubit.selectBrand(1, brand);
      expect(cubit.state.selectedBrandId, 1);
      expect(cubit.state.selectedBrand, brand);
      
      // Then toggle outlet - should clear brand
      cubit.toggleOutlet();
      
      expect(cubit.state.isOutletSelected, true);
      expect(cubit.state.selectedBrandId, isNull); // Should be cleared
      expect(cubit.state.selectedBrand, isNull); // Should be cleared
    });

    test('selectCategory is independent of other filters', () {
      final brand = Brand(id: 1, name: 'Test Brand', isTop: true, sortOrder: 1);
      
      // Set up initial state with brand, outlet, and promotion
      cubit.selectBrand(1, brand);
      cubit.togglePromotion();
      
      expect(cubit.state.selectedBrandId, 1);
      expect(cubit.state.isPromotionSelected, true);
      
      // Select category - should not affect other filters
      cubit.selectCategory(5);
      
      expect(cubit.state.selectedCategoryId, 5);
      expect(cubit.state.selectedBrandId, 1); // Should remain
      expect(cubit.state.isPromotionSelected, true); // Should remain
    });

    test('togglePromotion is independent of other filters', () {
      final brand = Brand(id: 1, name: 'Test Brand', isTop: true, sortOrder: 1);
      
      // Set up initial state with brand and category
      cubit.selectBrand(1, brand);
      cubit.selectCategory(5);
      
      expect(cubit.state.selectedBrandId, 1);
      expect(cubit.state.selectedCategoryId, 5);
      
      // Toggle promotion - should not affect other filters
      cubit.togglePromotion();
      
      expect(cubit.state.isPromotionSelected, true);
      expect(cubit.state.selectedBrandId, 1); // Should remain
      expect(cubit.state.selectedCategoryId, 5); // Should remain
    });

    test('outlet and promotion can work together', () {
      // Select outlet
      cubit.toggleOutlet();
      expect(cubit.state.isOutletSelected, true);
      
      // Select promotion - should work with outlet
      cubit.togglePromotion();
      expect(cubit.state.isPromotionSelected, true);
      expect(cubit.state.isOutletSelected, true); // Should remain
    });

    test('category and promotion can work together', () {
      // Select category
      cubit.selectCategory(5);
      expect(cubit.state.selectedCategoryId, 5);
      
      // Select promotion - should work with category
      cubit.togglePromotion();
      expect(cubit.state.isPromotionSelected, true);
      expect(cubit.state.selectedCategoryId, 5); // Should remain
    });

    test('clearAllFilters resets everything', () {
      final brand = Brand(id: 1, name: 'Test Brand', isTop: true, sortOrder: 1);
      
      // Set up state with all filters
      cubit.selectBrand(1, brand);
      cubit.selectCategory(5);
      cubit.togglePromotion();
      
      expect(cubit.state.selectedBrandId, 1);
      expect(cubit.state.selectedCategoryId, 5);
      expect(cubit.state.isPromotionSelected, true);
      
      // Clear all filters
      cubit.clearAllFilters();
      
      expect(cubit.state.selectedBrandId, isNull);
      expect(cubit.state.selectedBrand, isNull);
      expect(cubit.state.selectedCategoryId, isNull);
      expect(cubit.state.isOutletSelected, false);
      expect(cubit.state.isPromotionSelected, false);
    });

    test('getOutletFilterValue returns correct values', () {
      // No filters - should return false
      expect(cubit.getOutletFilterValue(), false);
      
      // Outlet selected - should return true
      cubit.toggleOutlet();
      expect(cubit.getOutletFilterValue(), true);
      
      // Brand selected - should return false (brand overrides outlet)
      final brand = Brand(id: 1, name: 'Test Brand', isTop: true, sortOrder: 1);
      cubit.selectBrand(1, brand);
      expect(cubit.getOutletFilterValue(), false);
    });

    test('getPromotionFilterValue returns correct values', () {
      // No promotion - should return null
      expect(cubit.getPromotionFilterValue(), isNull);
      
      // Promotion selected - should return true
      cubit.togglePromotion();
      expect(cubit.getPromotionFilterValue(), true);
    });
  });
}
