import 'package:flutter_test/flutter_test.dart';
import 'package:goldenprizma/domain/ads/models/advertisement.dart';
import 'package:goldenprizma/services/advertisement_action_service.dart';

void main() {
  group('AdvertisementActionService', () {
    late AdvertisementActionService service;

    setUp(() {
      service = AdvertisementActionService();
    });

    test('should identify external_url action correctly', () {
      const advertisement = Advertisement(
        id: 58,
        url: 'https://google.com',
        image: 'https://golden.test/advertisements_image/KRPJMlAVM5.jpg',
        appAction: 'external_url',
        appActionData: 'https://google.com',
      );

      expect(advertisement.isExternalUrl, true);
      expect(advertisement.isBrandStore, false);
      expect(advertisement.isPageAction, false);
      expect(advertisement.externalUrl, 'https://google.com');
    });

    test('should identify brand_store action correctly', () {
      const advertisement = Advertisement(
        id: 59,
        url: 'https://golden.test/store?brand=1',
        image: 'https://golden.test/advertisements_image/O462KLSpG5.jpg',
        appAction: 'brand_store',
        appActionData: '1',
        note: 'Kerastase',
      );

      expect(advertisement.isBrandStore, true);
      expect(advertisement.isExternalUrl, false);
      expect(advertisement.isPageAction, false);
      expect(advertisement.brandId, 1);
      expect(advertisement.note, 'Kerastase');
    });

    test('should identify page action correctly', () {
      const advertisement = Advertisement(
        id: 60,
        url: 'https://golden.test/pages/cerave',
        image: 'https://golden.test/advertisements_image/j11nFY0s3M.jpg',
        appAction: 'page',
        appActionData: 'cerave',
      );

      expect(advertisement.isPageAction, true);
      expect(advertisement.isExternalUrl, false);
      expect(advertisement.isBrandStore, false);
      expect(advertisement.pageSlug, 'cerave');
    });

    test('should generate correct action descriptions', () {
      const externalUrlAd = Advertisement(
        id: 58,
        url: 'https://google.com',
        image: 'test.jpg',
        appAction: 'external_url',
        appActionData: 'https://google.com',
      );

      const brandStoreAd = Advertisement(
        id: 59,
        url: 'https://golden.test/store?brand=1',
        image: 'test.jpg',
        appAction: 'brand_store',
        appActionData: '1',
        note: 'Kerastase',
      );

      const pageAd = Advertisement(
        id: 60,
        url: 'https://golden.test/pages/cerave',
        image: 'test.jpg',
        appAction: 'page',
        appActionData: 'cerave',
      );

      expect(
        service.getActionDescription(externalUrlAd),
        'External URL: https://google.com',
      );

      expect(
        service.getActionDescription(brandStoreAd),
        'Brand Store: Brand ID 1 (Kerastase)',
      );

      expect(
        service.getActionDescription(pageAd),
        'Page: cerave',
      );
    });

    test('should handle unknown actions with fallback', () {
      const unknownAd = Advertisement(
        id: 61,
        url: 'https://example.com',
        image: 'test.jpg',
        appAction: 'unknown_action',
        appActionData: 'some_data',
      );

      expect(
        service.getActionDescription(unknownAd),
        'Unknown action: unknown_action - Fallback to URL: https://example.com',
      );
    });
  });
}
