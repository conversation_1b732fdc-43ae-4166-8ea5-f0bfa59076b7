import 'package:flutter_test/flutter_test.dart';
import 'package:goldenprizma/domain/url_transformation/models/transformation_rule.dart';
import 'package:goldenprizma/services/url_transformation_service.dart';

void main() {
  group('UrlTransformationService', () {
    late UrlTransformationService service;

    setUp(() {
      service = UrlTransformationService();
    });

    tearDown(() {
      service.clearCache();
    });

    test('should transform Zara share URL to Turkish version', () {
      // Arrange
      const rules = TransformationRules(
        rules: [
          TransformationRule(
            domain: 'zara.com',
            sourcePattern: '*/share/*',
            targetPattern: '/tr/tr',
            priority: 10,
          ),
        ],
      );

      const originalUrl = 'https://www.zara.com/share/-p20110709.html';
      const expectedUrl = 'https://www.zara.com/tr/tr/-p20110709.html';

      // Act
      final transformedUrl = service.transformUrl(originalUrl, rules: rules);

      // Assert
      expect(transformedUrl, equals(expectedUrl));
    });

    test('should transform encoded Zara URL to Turkish version', () {
      // Arrange
      const rules = TransformationRules(
        rules: [
          TransformationRule(
            domain: 'zara.com',
            sourcePattern: '*/?go=https%3A//www.zara.com/share/*',
            targetPattern: '/tr/tr/',
            decodeUrl: true,
            priority: 10,
          ),
        ],
      );

      const originalUrl =
          'https://www.zara.com/?go=https%3A//www.zara.com/share/-p20110709.html';
      const expectedUrl = 'https://www.zara.com/tr/tr/-p20110709.html';

      // Act
      final transformedUrl = service.transformUrl(originalUrl, rules: rules);

      // Assert
      expect(transformedUrl, equals(expectedUrl));
    });

    test('should handle Shein subdomain URLs', () {
      // Arrange
      const rules = TransformationRules(
        rules: [
          TransformationRule(
            domain: '*.shein.com',
            sourcePattern: '*/share/*',
            targetPattern: '/tr',
            priority: 8,
          ),
        ],
      );

      const originalUrl = 'https://m.shein.com/share/product123.html';
      const expectedUrl = 'https://m.shein.com/tr/product123.html';

      // Act
      final transformedUrl = service.transformUrl(originalUrl, rules: rules);

      // Assert
      expect(transformedUrl, equals(expectedUrl));
    });

    test('should use highest priority rule when multiple rules match', () {
      // Arrange
      const rules = TransformationRules(
        rules: [
          TransformationRule(
            domain: 'zara.com',
            sourcePattern: '*/share/*',
            targetPattern: '/tr/tr', // Turkish/Turkish
            priority: 5, // Lower priority
          ),
          TransformationRule(
            domain: 'zara.com',
            sourcePattern: '*/share/*',
            targetPattern: '/tr/en', // Turkish/English
            priority: 10, // Higher priority - should win
          ),
          TransformationRule(
            domain: 'zara.com',
            sourcePattern: '*/share/*',
            targetPattern: '/us/en', // US/English
            priority: 3, // Lowest priority
          ),
        ],
      );

      const originalUrl = 'https://www.zara.com/share/-p20110709.html';
      const expectedUrl =
          'https://www.zara.com/tr/en/-p20110709.html'; // Highest priority rule

      // Act
      final transformedUrl = service.transformUrl(originalUrl, rules: rules);

      // Assert
      expect(transformedUrl, equals(expectedUrl));
    });

    test('should return original URL when no rules match', () {
      // Arrange
      const rules = TransformationRules(
        rules: [
          TransformationRule(
            domain: 'zara.com',
            sourcePattern: '*/share/*',
            targetPattern: '/tr/tr',
            priority: 10,
          ),
        ],
      );

      const originalUrl = 'https://www.unsupported-site.com/product/123';

      // Act
      final transformedUrl = service.transformUrl(originalUrl, rules: rules);

      // Assert
      expect(transformedUrl, equals(originalUrl));
    });

    test('should return original URL when no rules are loaded', () {
      // Arrange
      const originalUrl = 'https://www.zara.com/share/-p20110709.html';

      // Act
      final transformedUrl = service.transformUrl(originalUrl);

      // Assert
      expect(transformedUrl, equals(originalUrl));
    });

    test('should handle transformation errors gracefully', () {
      // Arrange
      const rules = TransformationRules(
        rules: [
          TransformationRule(
            domain: 'zara.com',
            sourcePattern: '*/share/*',
            targetPattern: '/tr/tr',
            priority: 10,
          ),
        ],
      );

      const invalidUrl = 'not-a-valid-url';

      // Act
      final transformedUrl = service.transformUrl(invalidUrl, rules: rules);

      // Assert
      expect(transformedUrl, equals(invalidUrl)); // Should fallback to original
    });

    test('should load and cache rules correctly', () {
      // Arrange
      const rules = TransformationRules(
        rules: [
          TransformationRule(
            domain: 'zara.com',
            sourcePattern: '*/share/*',
            targetPattern: '/tr/tr',
            priority: 10,
          ),
        ],
        version: 1,
      );

      // Act
      service.loadRules(rules);

      // Assert
      expect(service.cachedRules, equals(rules));
      expect(service.cachedRules?.version, equals(1));
    });
  });
}
