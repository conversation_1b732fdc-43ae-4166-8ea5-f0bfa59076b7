import 'dart:convert';

import 'package:equatable/equatable.dart';

class NotificationModel extends Equatable {
  final String id;
  final String title;
  final String body;
  final int modelId;
  final String type;
  final String createdAt;
  final String? image;
  final bool isRead;

  const NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    required this.modelId,
    required this.type,
    required this.createdAt,
    required this.isRead,
    this.image,
  });

  NotificationModel copyWith({
    String? id,
    String? title,
    String? body,
    int? modelId,
    String? type,
    String? createdAt,
    bool? isRead,
    String? image,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      modelId: modelId ?? this.modelId,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
      image: image ?? this.image,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'image': image,
      'modelId': modelId,
      'type': type,
      'createdAt': createdAt,
      'isRead': isRead,
    };
  }

  bool isType(String compareType) => type == compareType;

  static List<NotificationModel> fromJsonList(List? list) {
    if (list == null) return [];
    return list
        .map((notification) => NotificationModel.fromJson(notification))
        .toList();
  }

  factory NotificationModel.fromMap(Map<String, dynamic> map) {
    return NotificationModel(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      body: map['body'] ?? '',
      image: map['image'] ?? '',
      type: map['type'] ?? '',
      modelId: map['ordreId']?.toInt() ?? 0,
      createdAt: map['createdAt'] ?? '',
      isRead: map['isRead'] ?? false,
    );
  }

  String toJson() => json.encode(toMap());

  NotificationModel.fromJson(Map<String, dynamic> source)
      : id = source['id'],
        title = source['title'],
        body = source['body'],
        image = source['image'],
        type = source['type'] ?? '',
        modelId = source['model_id'] ?? 0,
        createdAt = source['created_at'],
        isRead = source['is_read'] ?? false;

  @override
  String toString() {
    return 'Notification(id: $id, title: $title, body: $body, image: $image modelId: $modelId, type: $type, createdAt: $createdAt, isRead: $isRead)';
  }

  @override
  List<Object?> get props =>
      [id, title, body, modelId, image, type, createdAt, isRead];
}
