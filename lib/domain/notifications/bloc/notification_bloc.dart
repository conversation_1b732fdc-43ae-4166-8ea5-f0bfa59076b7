import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/notifications/models/notification.dart';
import 'package:goldenprizma/repositories/notification_repository.dart';
import 'package:goldenprizma/helpers/logger.dart';

part 'notification_event.dart';
part 'notification_state.dart';

class NotificationBloc extends Bloc<NotificationEvent, NotificationState> {
  NotificationBloc(this._notificationRepository)
      : super(const NotificationState()) {
    on<NotificationRequestLoad>(_onNotificationRequestLoad);
    on<NotificationRequestRefresh>(_onNotificationRequestRefresh);
    on<NotificationMarkAsRead>(_onNotificationMarkAsRead);
    on<NotificationMarkAllAsRead>(_onNotificationMarkAllAsRead);
  }

  final NotificationRepository _notificationRepository;

  FutureOr<void> _onNotificationRequestLoad(
    NotificationRequestLoad event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      //prevent api call if it's already loading
      // or if it's reached max rows
      if (state.hasReachedMax || state.status == NotificationStatus.loading) {
        return;
      }

      if (state.page > 1) {
        emit(state.copyWith(status: NotificationStatus.loading));
      }

      List<NotificationModel> notifications =
          await _notificationRepository.getNotifications(page: state.page);

      emit(state.copyWith(
        status: NotificationStatus.success,
        notifications: List.of(state.notifications)..addAll(notifications),
        page: state.hasReachedMax ? state.page : state.page + 1,
        hasReachedMax: notifications.isEmpty,
        totalUnreadNotifications: notifications
            .where((NotificationModel notification) =>
                notification.isRead == false)
            .length,
      ));
    } catch (exception) {
      emit(state.copyWith(status: NotificationStatus.failure));
    }
  }

  FutureOr<void> _onNotificationRequestRefresh(
    NotificationRequestRefresh event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      List<NotificationModel> notifications =
          await _notificationRepository.getNotifications(page: 1);

      emit(state.copyWith(
        status: NotificationStatus.success,
        notifications: notifications,
        hasReachedMax: notifications.isEmpty,
        totalUnreadNotifications: notifications
            .where((NotificationModel notification) =>
                notification.isRead == false)
            .length,
      ));
    } catch (exception) {
      emit(state.copyWith(status: NotificationStatus.failure));
    }
  }

  FutureOr<void> _onNotificationMarkAsRead(
      NotificationMarkAsRead event, Emitter<NotificationState> emit) async {
    try {
      NotificationModel notification =
          await _notificationRepository.markAsRead(id: event.id);

      List<NotificationModel> notifications = state.notifications;
      int index =
          notifications.indexWhere((notif) => notif.id == notification.id);
      notifications[index] = notification;

      emit(state.copyWith(
        status: NotificationStatus.success,
        notifications: notifications,
        totalUnreadNotifications: notifications
            .where((NotificationModel notification) =>
                notification.isRead == false)
            .length,
      ));
    } catch (error) {
      logger(error.toString());
    }
  }

  FutureOr<void> _onNotificationMarkAllAsRead(
      NotificationMarkAllAsRead event, Emitter<NotificationState> emit) async {
    try {
      List<NotificationModel> notifications =
          await _notificationRepository.markAllAsRead();

      emit(state.copyWith(
        status: NotificationStatus.success,
        notifications: notifications,
        totalUnreadNotifications: 0,
      ));
    } catch (error) {
      logger(error.toString());
    }
  }
}
