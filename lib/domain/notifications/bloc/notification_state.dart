part of 'notification_bloc.dart';

enum NotificationStatus { initial, loading, refresh, success, failure }

class NotificationState extends Equatable {
  const NotificationState({
    this.page = 1,
    this.hasReachedMax = false,
    this.status = NotificationStatus.initial,
    this.notifications = const [],
    this.totalUnreadNotifications = 0,
  });

  final int page;
  final bool hasReachedMax;
  final NotificationStatus status;
  final List<NotificationModel> notifications;
  final int totalUnreadNotifications;

  @override
  List<Object> get props =>
      [page, hasReachedMax, status, notifications, totalUnreadNotifications];

  NotificationState copyWith({
    int? page,
    bool? hasReachedMax,
    NotificationStatus? status,
    List<NotificationModel>? notifications,
    int? totalUnreadNotifications,
  }) {
    return NotificationState(
      page: page ?? this.page,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      status: status ?? this.status,
      notifications: notifications ?? this.notifications,
      totalUnreadNotifications:
          totalUnreadNotifications ?? this.totalUnreadNotifications,
    );
  }

  String get unreadCounts {
    if (totalUnreadNotifications == 0) {
      return '';
    }
    if (totalUnreadNotifications > 9) {
      return '9+';
    }
    return totalUnreadNotifications.toString();
  }
}
