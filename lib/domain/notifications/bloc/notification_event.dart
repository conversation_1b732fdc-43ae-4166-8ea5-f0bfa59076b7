part of 'notification_bloc.dart';

abstract class NotificationEvent extends Equatable {
  const NotificationEvent();

  @override
  List<Object> get props => [];
}

class NotificationRequestLoad extends NotificationEvent {}

class NotificationRequestRefresh extends NotificationEvent {}

class NotificationMarkAsRead extends NotificationEvent {
  final String id;

  const NotificationMarkAsRead({required this.id});
}

class NotificationMarkAllAsRead extends NotificationEvent {
  const NotificationMarkAllAsRead();
}
