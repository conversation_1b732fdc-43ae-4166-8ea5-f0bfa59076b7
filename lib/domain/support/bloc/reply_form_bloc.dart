import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/domain/support/models/reply.dart';
import 'package:goldenprizma/presentation/views/forms/support/reply_body_field.dart';
import 'package:goldenprizma/repositories/support_repository.dart';

part 'reply_form_event.dart';
part 'reply_form_state.dart';

class ReplyFormBloc extends Bloc<ReplyFormEvent, ReplyFormState> {
  ReplyFormBloc({required this.supportRepository})
      : super(const ReplyFormState()) {
    on<ReplyBodyChanged>(_onReplyBodyChanged);
    on<ReplyFormSubmitted>(_onReplyFormSubmit);
    on<ReplyFormReset>(_onReplyFormReset);
  }
  final SupportRepository supportRepository;

  FutureOr<void> _onReplyBodyChanged(
      ReplyBodyChanged event, Emitter<ReplyFormState> emit) async {
    final body = ReplyBodyField.dirty(event.body);
    emit(state.copyWith(
      status: Formz.validate([body]),
      body: body,
      hasServerError: false,
      errorMessage: '',
    ));
  }

  FutureOr<void> _onReplyFormSubmit(
    ReplyFormSubmitted event,
    Emitter<ReplyFormState> emit,
  ) async {
    if (state.status.isValidated) {
      emit(state.copyWith(status: FormzStatus.submissionInProgress));
      try {
        Map<String, dynamic> response = await supportRepository.createReply(
            ticketId: event.ticketId, body: state.body.value);
        emit(state.copyWith(
          status: FormzStatus.submissionSuccess,
          ticketReply: response['reply'],
          successMessage: response['message'],
        ));
      } catch (error) {
        if (error is DioException) {
          emit(
            state.copyWith(
              hasServerError: true,
              status: FormzStatus.submissionFailure,
              errorMessage: error.response?.data['error'],
            ),
          );
        } else {
          emit(state.copyWith(
            status: FormzStatus.submissionFailure,
            hasServerError: false,
            errorMessage: '',
          ));
        }
      }
    }
  }

  FutureOr<void> _onReplyFormReset(
      ReplyFormReset event, Emitter<ReplyFormState> emit) {
    emit(state.copyWith(
      status: FormzStatus.pure,
      body: const ReplyBodyField.pure(),
      hasServerError: false,
      errorMessage: '',
    ));
  }
}
