part of 'ticket_bloc.dart';

enum TicketStateStatus { initial, loading, refresh, failure, success }

class TicketState extends Equatable {
  const TicketState({
    this.page = 1,
    this.hasReachedMax = false,
    this.status = TicketStateStatus.initial,
    this.tickets = const [],
    this.errorMessage = '',
  });

  final int page;
  final bool hasReachedMax;
  final String errorMessage;
  final TicketStateStatus status;
  final List<Ticket> tickets;

  @override
  List<Object> get props {
    return [
      page,
      hasReachedMax,
      errorMessage,
      status,
      tickets,
    ];
  }

  @override
  String toString() {
    return 'TicketState(page: $page, hasReachedMax: $hasReachedMax, errorMessage: $errorMessage, status: $status, tickets: $tickets)';
  }

  TicketState copyWith({
    int? page,
    bool? hasReachedMax,
    String? errorMessage,
    TicketStateStatus? status,
    List<Ticket>? tickets,
  }) {
    return TicketState(
      page: page ?? this.page,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      errorMessage: errorMessage ?? this.errorMessage,
      status: status ?? this.status,
      tickets: tickets ?? this.tickets,
    );
  }
}
