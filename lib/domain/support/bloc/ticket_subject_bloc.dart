import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/support/models/ticket_subject.dart';
import 'package:goldenprizma/repositories/support_repository.dart';

part 'ticket_subject_event.dart';
part 'ticket_subject_state.dart';

class TicketSubjectBloc extends Bloc<TicketSubjectEvent, TicketSubjectState> {
  TicketSubjectBloc({required this.supportRepository})
      : super(const TicketSubjectState()) {
    on<TicketSubjectRequestLoad>(_onRequestLoad);
  }

  final SupportRepository supportRepository;

  FutureOr<void> _onRequestLoad(
    TicketSubjectRequestLoad event,
    Emitter<TicketSubjectState> emit,
  ) async {
    try {
      List<TicketSubject> subjects = await supportRepository.getSubjects();
      emit(
        state.copyWith(
          status: TicketSubjectStatus.success,
          subjects: subjects,
        ),
      );
    } catch (error) {
      emit(state.copyWith(status: TicketSubjectStatus.failure));
    }
  }
}
