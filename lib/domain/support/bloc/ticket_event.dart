part of 'ticket_bloc.dart';

abstract class TicketEvent extends Equatable {
  const TicketEvent();

  @override
  List<Object> get props => [];
}

class TicketReqeustLoad extends TicketEvent {}

class TicketReload extends TicketEvent {}

class TicketAppend extends TicketEvent {
  final Ticket ticket;

  const TicketAppend({
    required this.ticket,
  });

  @override
  List<Object> get props => [ticket];
}
