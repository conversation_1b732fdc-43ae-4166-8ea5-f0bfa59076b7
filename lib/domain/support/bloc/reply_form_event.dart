part of 'reply_form_bloc.dart';

abstract class ReplyFormEvent extends Equatable {
  const ReplyFormEvent();

  @override
  List<Object> get props => [];
}

class ReplyBodyChanged extends ReplyFormEvent {
  const ReplyBodyChanged({required this.body});

  final String body;

  @override
  List<Object> get props => [body];

  @override
  String toString() => 'ReplyBodyChanged(body: $body)';
}

class ReplyFormSubmitted extends ReplyFormEvent {
  const ReplyFormSubmitted({required this.ticketId});
  final int ticketId;

  @override
  List<Object> get props => [ticketId];
}

class ReplyFormReset extends ReplyFormEvent {
  const ReplyFormReset();
  @override
  List<Object> get props => [];
}
