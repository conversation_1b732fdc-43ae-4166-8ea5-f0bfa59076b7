import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/support/models/reply.dart';
import 'package:goldenprizma/repositories/support_repository.dart';

part 'reply_event.dart';
part 'reply_state.dart';

class ReplyBloc extends Bloc<ReplyEvent, ReplyState> {
  ReplyBloc({required this.supportRepository}) : super(const ReplyState()) {
    on<ReplyRequestLoad>(_onRequestLoad);
    on<ReplyAppend>(_onReplyAppend);
  }

  final SupportRepository supportRepository;

  FutureOr<void> _onRequestLoad(
    ReplyRequestLoad event,
    Emitter<ReplyState> emit,
  ) async {
    try {
      List<TicketReply> replies =
          await supportRepository.getReplies(ticketId: event.ticketId);

      emit(state.copyWith(
        status: ReplyStatus.success,
        replies: replies,
      ));
    } catch (error) {
      emit(state.copyWith(status: ReplyStatus.failure));
    }
  }

  FutureOr<void> _onReplyAppend(
    ReplyAppend event,
    Emitter<ReplyState> emit,
  ) async {
    emit(
      state.copyWith(
        replies: List.of(state.replies)..add(event.reply),
      ),
    );
  }
}
