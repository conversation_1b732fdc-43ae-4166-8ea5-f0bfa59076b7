part of 'ticket_form_bloc.dart';

class TicketFormState extends Equatable {
  const TicketFormState({
    this.status = FormzStatus.pure,
    this.subject = const SubjectField.pure(),
    this.description = const DescriptionField.pure(),
    this.image = const ImageField.pure(),
    this.errorMessage = '',
    this.hasServerError = false,
    this.successMessage = '',
    this.ticket = const Ticket(
      id: 0,
      status: '',
      subject: '',
      description: '',
      createdAt: '',
      canReply: false,
    ),
  });

  final FormzStatus status;
  final SubjectField subject;
  final DescriptionField description;
  final ImageField image;
  final String errorMessage;
  final bool hasServerError;
  final String successMessage;
  final Ticket ticket;

  @override
  List<Object> get props {
    return [
      status,
      subject,
      description,
      errorMessage,
      hasServerError,
      successMessage,
      ticket,
    ];
  }

  @override
  String toString() {
    return 'TicketFormState(status: $status, subject: $subject, description: $description, errorMessage: $errorMessage, hasServerError: $hasServerError, successMessage: $successMessage, ticket: $ticket)';
  }

  TicketFormState copyWith({
    FormzStatus? status,
    SubjectField? subject,
    DescriptionField? description,
    String? errorMessage,
    bool? hasServerError,
    String? successMessage,
    Ticket? ticket,
  }) {
    return TicketFormState(
      status: status ?? this.status,
      subject: subject ?? this.subject,
      description: description ?? this.description,
      errorMessage: errorMessage ?? this.errorMessage,
      hasServerError: hasServerError ?? this.hasServerError,
      successMessage: successMessage ?? this.successMessage,
      ticket: ticket ?? this.ticket,
    );
  }
}
