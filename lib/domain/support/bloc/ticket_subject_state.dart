part of 'ticket_subject_bloc.dart';

enum TicketSubjectStatus { initial, success, failure }

class TicketSubjectState extends Equatable {
  const TicketSubjectState({
    this.subjects = const [],
    this.status = TicketSubjectStatus.initial,
  });

  final List<TicketSubject> subjects;
  final TicketSubjectStatus status;

  @override
  List<Object> get props => [subjects, status];

  TicketSubjectState copyWith({
    List<TicketSubject>? subjects,
    TicketSubjectStatus? status,
  }) {
    return TicketSubjectState(
      subjects: subjects ?? this.subjects,
      status: status ?? this.status,
    );
  }

  @override
  String toString() =>
      'TicketSubjectState(subjects: $subjects, status: $status)';
}
