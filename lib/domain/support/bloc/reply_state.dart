part of 'reply_bloc.dart';

enum ReplyStatus { initial, success, failure }

class ReplyState extends Equatable {
  const ReplyState({
    this.status = ReplyStatus.initial,
    this.replies = const [],
  });

  final ReplyStatus status;
  final List<TicketReply> replies;

  @override
  List<Object> get props => [status, replies];

  ReplyState copyWith({
    ReplyStatus? status,
    List<TicketReply>? replies,
  }) {
    return ReplyState(
      status: status ?? this.status,
      replies: replies ?? this.replies,
    );
  }
}
