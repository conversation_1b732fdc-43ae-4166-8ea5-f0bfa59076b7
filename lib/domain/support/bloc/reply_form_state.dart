part of 'reply_form_bloc.dart';

class ReplyFormState extends Equatable {
  const ReplyFormState({
    this.status = FormzStatus.pure,
    this.body = const ReplyBodyField.pure(),
    this.errorMessage = '',
    this.hasServerError = false,
    this.successMessage = '',
    this.ticketReply = const TicketReply(
      id: 0,
      author: '',
      authorAvatar: '',
      content: '',
      createdAt: '',
    ),
  });

  final FormzStatus status;
  final ReplyBodyField body;
  final String errorMessage;
  final bool hasServerError;
  final String successMessage;
  final TicketReply ticketReply;

  @override
  List<Object> get props {
    return [
      status,
      body,
      errorMessage,
      hasServerError,
      successMessage,
      ticketReply,
    ];
  }

  @override
  String toString() {
    return 'ReplyFormState(status: $status, body: $body, errorMessage: $errorMessage, hasServerError: $hasServerError, successMessage: $successMessage, ticketReply: $ticketReply)';
  }

  ReplyFormState copyWith({
    FormzStatus? status,
    ReplyBodyField? body,
    String? errorMessage,
    bool? hasServerError,
    String? successMessage,
    TicketReply? ticketReply,
  }) {
    return ReplyFormState(
      status: status ?? this.status,
      body: body ?? this.body,
      errorMessage: errorMessage ?? this.errorMessage,
      hasServerError: hasServerError ?? this.hasServerError,
      successMessage: successMessage ?? this.successMessage,
      ticketReply: ticketReply ?? this.ticketReply,
    );
  }
}
