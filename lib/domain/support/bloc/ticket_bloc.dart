import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/support/models/ticket.dart';
import 'package:goldenprizma/repositories/support_repository.dart';

part 'ticket_event.dart';
part 'ticket_state.dart';

class TicketBloc extends Bloc<TicketEvent, TicketState> {
  TicketBloc({required this.supportRepository}) : super(const TicketState()) {
    on<TicketEvent>(_onRequestLoad);
    on<TicketReload>(_onTicketReload);
  }

  final SupportRepository supportRepository;

  FutureOr<void> _onRequestLoad(
    TicketEvent event,
    Emitter<TicketState> emit,
  ) async {
    if (state.status == TicketStateStatus.loading || state.hasReachedMax) {
      return;
    }
    if (state.page > 1) {
      emit(state.copyWith(status: TicketStateStatus.loading));
    }
    try {
      List<Ticket> tickets =
          await supportRepository.getTickets(page: state.page);
      emit(
        state.copyWith(
          status: TicketStateStatus.success,
          tickets: List.of(state.tickets)..addAll(tickets),
          page: state.page + (tickets.isEmpty ? 0 : 1),
          hasReachedMax: tickets.isEmpty,
        ),
      );
    } catch (error) {
      if (error is DioException) {
        emit(
          state.copyWith(
            status: TicketStateStatus.failure,
            errorMessage: error.response!.data['message'],
          ),
        );
      } else {
        emit(state.copyWith(status: TicketStateStatus.failure));
      }
    }
  }

  FutureOr<void> _onTicketReload(
    TicketEvent event,
    Emitter<TicketState> emit,
  ) async {
    emit(state.copyWith(status: TicketStateStatus.loading));
    try {
      List<Ticket> tickets = await supportRepository.getTickets(page: 1);
      emit(
        state.copyWith(
          status: TicketStateStatus.success,
          tickets: tickets,
          page: state.page + (tickets.isEmpty ? 0 : 1),
          hasReachedMax: tickets.isEmpty,
        ),
      );
    } catch (error) {
      if (error is DioException) {
        emit(
          state.copyWith(
            status: TicketStateStatus.failure,
            errorMessage: error.response!.data['message'],
          ),
        );
      } else {
        emit(state.copyWith(status: TicketStateStatus.failure));
      }
    }
  }
}
