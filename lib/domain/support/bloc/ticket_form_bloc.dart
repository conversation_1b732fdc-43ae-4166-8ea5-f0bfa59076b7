import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/domain/support/models/ticket.dart';
import 'package:goldenprizma/main.dart' show getIt;
import 'package:goldenprizma/presentation/views/forms/support/description_field.dart';
import 'package:goldenprizma/presentation/views/forms/support/image_field.dart';
import 'package:goldenprizma/presentation/views/forms/support/subject_field.dart';
import 'package:goldenprizma/repositories/support_repository.dart';

part 'ticket_form_event.dart';
part 'ticket_form_state.dart';

class TicketFormBloc extends Bloc<TicketFormEvent, TicketFormState> {
  final SupportRepository supportRepository = getIt.get<SupportRepository>();
  TicketFormBloc() : super(const TicketFormState()) {
    on<TicketSubjectFieldChanged>(_onSubjectFieldChanged);
    on<TicketDescriptionFieldChanged>(_onDescriptionFieldChanged);
    on<TicketFormSubmitted>(_onTicketFormSubmit);
    on<TicketFormReset>(_onTicketFormReset);
  }

  FutureOr<void> _onSubjectFieldChanged(
    TicketSubjectFieldChanged event,
    Emitter<TicketFormState> emit,
  ) {
    final subject = SubjectField.dirty(event.subject);
    emit(
      state.copyWith(
        subject: subject,
        status: Formz.validate([subject]),
      ),
    );
  }

  FutureOr<void> _onDescriptionFieldChanged(
    TicketDescriptionFieldChanged event,
    Emitter<TicketFormState> emit,
  ) {
    final description = DescriptionField.dirty(event.description);
    emit(
      state.copyWith(
        description: description,
        status: Formz.validate([description, state.subject]),
      ),
    );
  }

  FutureOr<void> _onTicketFormSubmit(
    TicketFormSubmitted event,
    Emitter<TicketFormState> emit,
  ) async {
    if (state.status == FormzStatus.submissionInProgress) {
      return;
    }
    SubjectField subject = SubjectField.dirty(state.subject.value);
    DescriptionField description =
        DescriptionField.dirty(state.description.value);
    emit(
      state.copyWith(
          status: Formz.validate([subject, description]),
          description: description,
          subject: subject),
    );

    try {
      if (state.status.isValidated) {
        emit(
          state.copyWith(
            status: FormzStatus.submissionInProgress,
          ),
        );
        String message = await supportRepository.createTicket({
          'subject': state.subject.value,
          'description': state.description.value,
        });
        emit(state.copyWith(
          status: FormzStatus.submissionSuccess,
          hasServerError: false,
          successMessage: message,
        ));
      }
    } on DioException catch (error) {
      emit(state.copyWith(
        status: FormzStatus.submissionFailure,
        hasServerError: true,
        errorMessage: error.response!.data['error'],
      ));
    } catch (error) {
      emit(state.copyWith(
        status: FormzStatus.submissionFailure,
        hasServerError: false,
        errorMessage: 'An error occured please try again',
      ));
    }
  }

  FutureOr<void> _onTicketFormReset(
      TicketFormReset event, Emitter<TicketFormState> emit) {
    emit(
      state.copyWith(
        status: FormzStatus.pure,
        subject: const SubjectField.pure(),
        description: const DescriptionField.pure(),
        hasServerError: false,
      ),
    );
  }
}
