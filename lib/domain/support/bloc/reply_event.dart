part of 'reply_bloc.dart';

abstract class ReplyEvent extends Equatable {
  const ReplyEvent();

  @override
  List<Object> get props => [];
}

class ReplyRequestLoad extends ReplyEvent {
  const ReplyRequestLoad({required this.ticketId});

  final int ticketId;

  @override
  List<Object> get props => [ticketId];
}

class ReplyAppend extends ReplyEvent {
  const ReplyAppend({required this.reply});

  final TicketReply reply;

  @override
  List<Object> get props => [reply];
}
