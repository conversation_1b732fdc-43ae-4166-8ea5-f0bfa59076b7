part of 'ticket_form_bloc.dart';

abstract class Ticket<PERSON>orm<PERSON>vent extends Equatable {
  const TicketFormEvent();

  @override
  List<Object> get props => [];
}

class TicketSubjectFieldChanged extends Ticket<PERSON>orm<PERSON>vent {
  const TicketSubjectFieldChanged({
    required this.subject,
  });

  final String subject;

  @override
  List<Object> get props => [subject];
}

class TicketDescriptionFieldChanged extends TicketFormEvent {
  const TicketDescriptionFieldChanged({
    required this.description,
  });

  final String description;

  @override
  List<Object> get props => [description];
}

class TicketFormReset extends TicketFormEvent {
  const TicketFormReset();
}

class TicketFormSubmitted extends Ticket<PERSON>orm<PERSON>vent {
  const TicketFormSubmitted();
}
