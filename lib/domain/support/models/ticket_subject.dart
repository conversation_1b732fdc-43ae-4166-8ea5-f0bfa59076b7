import 'dart:convert';

import 'package:equatable/equatable.dart';

class TicketSubject extends Equatable {
  final String title;

  const TicketSubject({
    required this.title,
  });

  TicketSubject copyWith({
    String? title,
  }) {
    return TicketSubject(
      title: title ?? this.title,
    );
  }

  @override
  List<Object> get props {
    return [title];
  }

  Map<String, dynamic> toMap() {
    return {'title': title};
  }

  factory TicketSubject.fromMap(Map<String, dynamic> map) {
    return TicketSubject(title: map['title'] ?? '');
  }

  String toJson() => json.encode(toMap());

  TicketSubject.fromJson(Map<String, dynamic> jsonData)
      : title = jsonData['title'] ?? '';

  factory TicketSubject.empty() {
    return TicketSubject.fromJson(const {});
  }

  @override
  String toString() {
    return 'Dart(title: $title)';
  }
}
