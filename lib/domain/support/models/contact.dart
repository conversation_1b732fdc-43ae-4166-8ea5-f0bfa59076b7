import 'package:equatable/equatable.dart';

class Contact extends Equatable {
  const Contact({
    this.facebookLink,
    this.instagramLink,
    this.phone,
    this.snapchat,
    this.tiktok,
  });

  final String? phone;
  final String? snapchat;
  final String? tiktok;
  final String? facebookLink;
  final String? instagramLink;

  static const String cacheKey = 'contact_details';

  Contact copyWith({
    String? phone,
    String? snapchat,
    String? facebookLink,
    String? instagramLink,
    String? tiktok,
  }) {
    return Contact(
      phone: phone ?? this.phone,
      snapchat: snapchat ?? this.snapchat,
      facebookLink: facebookLink ?? this.facebookLink,
      instagramLink: instagramLink ?? this.instagramLink,
      tiktok: tiktok ?? this.tiktok,
    );
  }

  Contact.fromJson(Map<String, dynamic> jsonData)
      : phone = jsonData['contact_phone'] ?? '',
        snapchat = jsonData['snapchat'] ?? '',
        tiktok = jsonData['tiktok'] ?? '',
        facebookLink = jsonData['facebook_link'] ?? '',
        instagramLink = jsonData['instagram_link'] ?? '';

  @override
  String toString() {
    return 'Contact(phone: $phone, snapchat: $snapchat, tiktok: $tiktok, faccebookLink: $facebookLink, instagramLink: $instagramLink)';
  }

  @override
  List<Object?> get props =>
      [phone, snapchat, tiktok, facebookLink, instagramLink];
}
