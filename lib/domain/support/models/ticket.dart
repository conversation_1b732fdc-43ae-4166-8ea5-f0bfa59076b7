import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class Ticket extends Equatable {
  final int id;
  final String status;
  final String subject;
  final String createdAt;
  final String description;
  final bool canReply;

  const Ticket({
    required this.id,
    required this.status,
    required this.subject,
    required this.createdAt,
    required this.description,
    required this.canReply,
  });

  Ticket copyWith({
    int? id,
    String? status,
    String? subject,
    String? createdAt,
    String? description,
    bool? canReply,
  }) {
    return Ticket(
      id: id ?? this.id,
      status: status ?? this.status,
      subject: subject ?? this.subject,
      createdAt: createdAt ?? this.createdAt,
      description: description ?? this.description,
      canReply: canReply ?? this.canReply,
    );
  }

  String getStatus(BuildContext context) {
    return status == 'created'
        ? AppLocalizations.of(context)!.open
        : AppLocalizations.of(context)!.closed;
  }

  @override
  List<Object> get props {
    return [
      id,
      status,
      subject,
      createdAt,
      description,
      canReply,
    ];
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'status': status,
      'subject': subject,
      'createdAt': createdAt,
      'description': description,
      'canReply': canReply,
    };
  }

  factory Ticket.fromMap(Map<String, dynamic> map) {
    return Ticket(
      id: map['id']?.toInt() ?? 0,
      status: map['status'] ?? '',
      subject: map['subject'] ?? '',
      createdAt: map['createdAt'] ?? '',
      description: map['description'] ?? '',
      canReply: map['canReply'] ?? false,
    );
  }

  String toJson() => json.encode(toMap());

  Ticket.fromJson(Map<String, dynamic> jsonData)
      : id = jsonData['id']?.toInt() ?? 0,
        status = jsonData['status'] ?? '',
        subject = jsonData['subject'] ?? '',
        createdAt = jsonData['created_at'] ?? '',
        description = jsonData['description'] ?? '',
        canReply = jsonData['can_reply'] ?? false;

  factory Ticket.empty() {
    return Ticket.fromJson(const {});
  }

  @override
  String toString() {
    return 'Dart(id: $id, status: $status, subject: $subject, createdAt: $createdAt, description: $description, canReply: $canReply)';
  }

  String get dateFormat {
    if (createdAt.isEmpty) {
      return '';
    }
    List<String> dateTime = createdAt.split(" ");

    return dateTime[0];
  }

  String get timeFormat {
    if (createdAt.isEmpty) {
      return '';
    }
    List<String> dateTime = createdAt.split(" ");
    if (dateTime.length > 1) {
      return dateTime[1];
    }
    return '';
  }
}
