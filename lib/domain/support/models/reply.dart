import 'dart:convert';

import 'package:equatable/equatable.dart';

class TicketReply extends Equatable {
  final int id;
  final String content;
  final String author;
  final String authorAvatar;
  final String createdAt;

  const TicketReply({
    required this.id,
    required this.content,
    required this.author,
    required this.authorAvatar,
    required this.createdAt,
  });

  TicketReply copyWith({
    int? id,
    String? content,
    String? author,
    String? authorAvatar,
    String? createdAt,
  }) {
    return TicketReply(
      id: id ?? this.id,
      content: content ?? this.content,
      author: author ?? this.author,
      authorAvatar: authorAvatar ?? this.authorAvatar,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'content': content,
      'author': author,
      'authorAvatar': authorAvatar,
      'createdAt': createdAt,
    };
  }

  factory TicketReply.fromMap(Map<String, dynamic> map) {
    return TicketReply(
      id: map['id']?.toInt() ?? 0,
      content: map['content'] ?? '',
      author: map['author'] ?? '',
      authorAvatar: map['authorAvatar'] ?? '',
      createdAt: map['createdAt'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  @override
  String toString() {
    return 'TicketReply(id: $id, content: $content, author: $author, authorAvatar: $authorAvatar, createdAt: $createdAt)';
  }

  @override
  List<Object> get props {
    return [
      id,
      content,
      author,
      authorAvatar,
      createdAt,
    ];
  }

  TicketReply.fromJson(Map<String, dynamic> jsonData)
      : id = jsonData['id'].toInt() ?? 0,
        content = jsonData['content'] ?? '',
        author = jsonData['author'] ?? '',
        authorAvatar = jsonData['author_avatar'] ?? '',
        createdAt = jsonData['created_at'] ?? '';
}
