part of 'request_delivery_bloc.dart';

abstract class RequestDeliveryEvent extends Equatable {
  const RequestDeliveryEvent();

  @override
  List<Object> get props => [];
}

class RequestDeliveryReqeustLoad extends RequestDeliveryEvent {}

class RequestDeliveryRefresh extends RequestDeliveryEvent {}

class RequestDeliveryAppend extends RequestDeliveryEvent {
  final RequestDelivery requestDelivery;

  const RequestDeliveryAppend({
    required this.requestDelivery,
  });

  @override
  List<Object> get props => [requestDelivery];
}
