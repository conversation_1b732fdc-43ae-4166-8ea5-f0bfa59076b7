part of 'request_delivery_bloc.dart';

enum RequestDeliveryStatus { initial, loading, refresh, failure, success }

class RequestDeliveryState extends Equatable {
  const RequestDeliveryState({
    this.page = 1,
    this.hasReachedMax = false,
    this.status = RequestDeliveryStatus.initial,
    this.deliveries = const [],
    this.errorMessage = '',
  });

  final int page;
  final bool hasReachedMax;
  final String errorMessage;
  final RequestDeliveryStatus status;
  final List<RequestDelivery> deliveries;

  @override
  List<Object> get props {
    return [
      page,
      hasReachedMax,
      errorMessage,
      status,
      deliveries,
    ];
  }

  @override
  String toString() {
    return 'RequestDeliveryState(page: $page, hasReachedMax: $hasReachedMax, errorMessage: $errorMessage, status: $status, deliveries: $deliveries)';
  }

  RequestDeliveryState copyWith({
    int? page,
    bool? hasReachedMax,
    String? errorMessage,
    RequestDeliveryStatus? status,
    List<RequestDelivery>? deliveries,
  }) {
    return RequestDeliveryState(
      page: page ?? this.page,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      errorMessage: errorMessage ?? this.errorMessage,
      status: status ?? this.status,
      deliveries: deliveries ?? this.deliveries,
    );
  }
}
