import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/domain/request_deliveries/models/request_delivery.dart';
import 'package:goldenprizma/presentation/views/forms/request_delivery/note_field.dart';
import 'package:goldenprizma/presentation/views/forms/request_delivery/city_field.dart';
import 'package:goldenprizma/repositories/request_delivery_repository.dart';

part 'request_delivery_form_event.dart';
part 'request_delivery_form_state.dart';

class RequestDeliveryFormBloc
    extends Bloc<RequestDeliveryFormEvent, RequestDeliveryFormState> {
  final RequestDeliveryRepository repository;
  RequestDeliveryFormBloc({required this.repository})
      : super(const RequestDeliveryFormState()) {
    on<RequestDeliveryFormNoteChanged>(_onNoteChanged);
    on<RequestDeliveryFormCityChanged>(_onCityChanged);
    on<RequestDeliveryFormSubmitted>(_onFormSubmit);
    on<RequestDeliveryFormReset>(_onFormReset);
  }

  FutureOr<void> _onNoteChanged(RequestDeliveryFormNoteChanged event,
      Emitter<RequestDeliveryFormState> emit) {
    final note = NoteField.dirty(event.note);

    emit(state.copyWith(
      note: note,
      status: Formz.validate([note]),
    ));
  }

  FutureOr<void> _onCityChanged(RequestDeliveryFormCityChanged event,
      Emitter<RequestDeliveryFormState> emit) {
    final city = CityField.dirty(event.city);

    emit(state.copyWith(
      city: city,
      status: Formz.validate([city]),
    ));
  }

  FutureOr<void> _onFormSubmit(RequestDeliveryFormSubmitted event,
      Emitter<RequestDeliveryFormState> emit) async {
    if (state.status.isValidated) {
      emit(state.copyWith(status: FormzStatus.submissionInProgress));

      try {
        Map<String, dynamic> response = await repository.createRequestDelivery({
          'note': state.note.value,
          'city': state.city.value,
        });

        emit(
          state.copyWith(
            status: FormzStatus.submissionSuccess,
            hasServerError: false,
            successMessage: response['message'],
            delivery: response['delivery'],
          ),
        );
      } catch (error) {
        if (error is DioException) {
          emit(
            state.copyWith(
              status: FormzStatus.submissionFailure,
              errorMessage: error.response!.data['error'] ?? '',
              hasServerError: true,
            ),
          );
        } else {
          emit(state.copyWith(
            status: FormzStatus.submissionFailure,
            hasServerError: false,
          ));
        }
      }

      emit(state.copyWith(status: FormzStatus.submissionFailure));
    }
  }

  FutureOr<void> _onFormReset(
      RequestDeliveryFormReset event, Emitter<RequestDeliveryFormState> emit) {
    emit(
      state.copyWith(
        note: const NoteField.pure(),
        status: FormzStatus.pure,
      ),
    );
  }
}
