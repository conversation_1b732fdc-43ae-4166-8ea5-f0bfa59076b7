part of 'request_delivery_form_bloc.dart';

class RequestDeliveryFormState extends Equatable {
  const RequestDeliveryFormState({
    this.status = FormzStatus.pure,
    this.note = const NoteField.pure(),
    this.city = const CityField.pure(),
    this.errorMessage = '',
    this.hasServerError = false,
    this.successMessage = '',
    this.delivery = const RequestDelivery(0, '', 'created', ''),
  });

  final FormzStatus status;
  final NoteField note;
  final CityField city;
  final String errorMessage;
  final bool hasServerError;
  final String successMessage;
  final RequestDelivery delivery;

  @override
  List<Object> get props {
    return [
      status,
      note,
      city,
      errorMessage,
      hasServerError,
      successMessage,
      delivery,
    ];
  }

  RequestDeliveryFormState copyWith({
    FormzStatus? status,
    NoteField? note,
    CityField? city,
    String? errorMessage,
    bool? hasServerError,
    String? successMessage,
    RequestDelivery? delivery,
  }) {
    return RequestDeliveryFormState(
      status: status ?? this.status,
      note: note ?? this.note,
      city: city ?? this.city,
      errorMessage: errorMessage ?? this.errorMessage,
      hasServerError: hasServerError ?? this.hasServerError,
      successMessage: successMessage ?? this.successMessage,
      delivery: delivery ?? this.delivery,
    );
  }
}
