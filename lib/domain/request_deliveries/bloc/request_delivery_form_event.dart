part of 'request_delivery_form_bloc.dart';

abstract class RequestDeliveryFormEvent extends Equatable {
  const RequestDeliveryFormEvent();

  @override
  List<Object> get props => [];
}

class RequestDeliveryFormNoteChanged extends RequestDeliveryFormEvent {
  final String note;

  const RequestDeliveryFormNoteChanged({
    required this.note,
  });

  @override
  List<Object> get props => [note];
}

class RequestDeliveryFormCityChanged extends RequestDeliveryFormEvent {
  final String city;

  const RequestDeliveryFormCityChanged({
    required this.city,
  });

  @override
  List<Object> get props => [city];
}

class RequestDeliveryFormSubmitted extends RequestDeliveryFormEvent {
  const RequestDeliveryFormSubmitted();
}

class RequestDeliveryFormReset extends RequestDeliveryFormEvent {
  const RequestDeliveryFormReset();
}
