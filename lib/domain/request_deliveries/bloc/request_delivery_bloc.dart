import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/request_deliveries/models/request_delivery.dart';
import 'package:goldenprizma/repositories/request_delivery_repository.dart';

part 'request_delivery_event.dart';
part 'request_delivery_state.dart';

class RequestDeliveryBloc
    extends Bloc<RequestDeliveryEvent, RequestDeliveryState> {
  RequestDeliveryBloc({required this.repository})
      : super(const RequestDeliveryState()) {
    on<RequestDeliveryReqeustLoad>(_onRequestLoad);
    on<RequestDeliveryRefresh>(_onRefresh);
    on<RequestDeliveryAppend>(_onAppend);
  }

  final RequestDeliveryRepository repository;

  FutureOr<void> _onRequestLoad(RequestDeliveryReqeustLoad event,
      Emitter<RequestDeliveryState> emit) async {
    try {
      if (state.hasReachedMax ||
          state.status == RequestDeliveryStatus.loading) {
        return;
      }

      if (state.page > 1) {
        emit(state.copyWith(status: RequestDeliveryStatus.loading));
      }

      List<RequestDelivery> deliveries =
          await repository.getAll(page: state.page);

      emit(state.copyWith(
        status: RequestDeliveryStatus.success,
        deliveries: List.of(state.deliveries)..addAll(deliveries),
        page: state.page + (deliveries.isEmpty ? 0 : 1),
        hasReachedMax: deliveries.isEmpty,
        errorMessage: '',
      ));
    } catch (error) {
      if (error is DioException) {
        emit(state.copyWith(
          status: RequestDeliveryStatus.failure,
          errorMessage: error.response!.data['error'] ?? '',
        ));
      } else {
        emit(state.copyWith(
          status: RequestDeliveryStatus.failure,
          errorMessage: 'An error occured',
        ));
      }
    }
  }

  FutureOr<void> _onRefresh(
      RequestDeliveryRefresh event, Emitter<RequestDeliveryState> emit) async {
    try {
      emit(state.copyWith(status: RequestDeliveryStatus.refresh));
      if (state.hasReachedMax ||
          state.status == RequestDeliveryStatus.loading) {
        return;
      }

      List<RequestDelivery> deliveries =
          await repository.getAll(page: state.page);

      emit(state.copyWith(
        status: RequestDeliveryStatus.success,
        deliveries: state.deliveries,
        page: state.page + 1,
        hasReachedMax: deliveries.isEmpty,
        errorMessage: '',
      ));
    } catch (error) {
      if (error is DioException) {
        emit(state.copyWith(
          status: RequestDeliveryStatus.failure,
          errorMessage: error.response!.data['error'] ?? '',
        ));
      } else {
        emit(state.copyWith(
          status: RequestDeliveryStatus.failure,
          errorMessage: 'An error occured',
        ));
      }
    }
  }

  FutureOr<void> _onAppend(
      RequestDeliveryAppend event, Emitter<RequestDeliveryState> emit) {
    try {
      emit(state.copyWith(
          deliveries: List.of(state.deliveries)
            ..insert(0, event.requestDelivery)));
      // ignore: empty_catches
    } catch (error) {}
  }
}
