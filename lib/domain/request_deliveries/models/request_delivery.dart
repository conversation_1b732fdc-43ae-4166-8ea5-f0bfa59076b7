import 'dart:convert';

import 'package:equatable/equatable.dart';

class RequestDelivery extends Equatable {
  const RequestDelivery(
    this.id,
    this.note,
    this.status,
    this.createdAt,
  );
  final int id;
  final String note;
  final String status;
  final String createdAt;

  @override
  List<Object> get props => [id, note, status, createdAt];

  RequestDelivery copyWith({
    int? id,
    String? note,
    String? status,
    String? createdAt,
  }) {
    return RequestDelivery(
      id ?? this.id,
      note ?? this.note,
      status ?? this.status,
      createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'RequestDelivery(id: $id, note: $note, status: $status, createdAt: $createdAt)';
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'note': note,
      'status': status,
      'createdAt': createdAt,
    };
  }

  factory RequestDelivery.fromMap(Map<String, dynamic> map) {
    return RequestDelivery(
      map['id'] ?? '',
      map['note'] ?? '',
      map['status'] ?? '',
      map['createdAt'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  RequestDelivery.fromJson(Map<String, dynamic> jsonData)
      : id = jsonData['id'],
        note = jsonData['note'],
        status = jsonData['status'],
        createdAt = jsonData['created_at'];

  factory RequestDelivery.empty() =>
      const RequestDelivery(0, '', 'created', '');

  String get dateOnly {
    if (createdAt.isEmpty) {
      return '';
    }
    List<String> dateTime = createdAt.split(" ");

    return dateTime[0];
  }

  String get timeFormat {
    if (createdAt.isEmpty) {
      return '';
    }
    List<String> dateTime = createdAt.split(" ");

    return dateTime[1];
  }
}
