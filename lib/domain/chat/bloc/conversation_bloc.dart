import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:goldenprizma/domain/chat/models/conversation.dart';
import 'package:goldenprizma/repositories/conversation_repository.dart';

part 'conversation_event.dart';
part 'conversation_state.dart';

class ConversationBloc extends Bloc<ConversationEvent, ConversationState> {
  final ConversationRepostory repository;

  ConversationBloc({required this.repository})
      : super(const ConversationState()) {
    on<GetConversations>((event, emit) async {
      if (state.status == ConversationStatus.loading) {
        return;
      }

      emit(state.copyWith(status: ConversationStatus.loading));

      try {
        final conversations = await repository.getConversations();
        emit(state.copyWith(
          status: ConversationStatus.success,
          conversations: conversations,
        ));
      } catch (e) {
        // trace
        debugPrint(e.toString());

        emit(state.copyWith(
          status: ConversationStatus.failure,
          errorMessage: e.toString(),
        ));
      }
    });
  }
}
