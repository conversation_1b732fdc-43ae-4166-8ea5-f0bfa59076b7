part of 'message_bloc.dart';

enum MessageStatus { initial, loading, success, failure }

class MessageState extends Equatable {
  final MessageStatus status;
  final List<Message> messages;
  final String? errorMessage;

  const MessageState({
    this.status = MessageStatus.initial,
    this.messages = const [],
    this.errorMessage,
  });

  @override
  List<Object> get props => [
        status,
        messages,
      ];

  MessageState copyWith({
    MessageStatus? status,
    List<Message>? messages,
    String? errorMessage,
  }) {
    return MessageState(
      status: status ?? this.status,
      messages: messages ?? this.messages,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}
