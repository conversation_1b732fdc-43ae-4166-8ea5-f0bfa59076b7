part of 'conversation_bloc.dart';

enum ConversationStatus { initial, loading, success, failure }

class ConversationState extends Equatable {
  final List<Conversation> conversations;
  final ConversationStatus status;
  final String? errorMessage;

  const ConversationState({
    this.status = ConversationStatus.initial,
    this.conversations = const [],
    this.errorMessage,
  });

  @override
  List<Object> get props => [
        status,
        conversations,
      ];

  ConversationState copyWith({
    List<Conversation>? conversations,
    ConversationStatus? status,
    String? errorMessage,
  }) {
    return ConversationState(
      conversations: conversations ?? this.conversations,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}
