import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/chat/models/message.dart';
import 'package:goldenprizma/repositories/message_repository.dart';

part 'message_event.dart';
part 'message_state.dart';

class MessageBloc extends Bloc<MessageEvent, MessageState> {
  final MessageRepository repository;

  MessageBloc({
    required this.repository,
  }) : super(const MessageState()) {
    on<GetMessages>((event, emit) async {
      if (state.status == MessageStatus.loading) {
        return;
      }

      emit(state.copyWith(status: MessageStatus.loading));

      // try {
      final response = await repository.getMessages(
        conversationId: event.conversationId,
      );
      emit(state.copyWith(
        status: MessageStatus.success,
        messages: response,
      ));
      // } catch (e) {
      //   emit(state.copyWith(
      //     status: MessageStatus.failure,
      //     errorMessage: e.toString(),
      //   ));
      // }
    });
  }
}
