class Contact {
  final int id;
  final String sourceId;
  final String pubsubToken;
  final String name;
  final String? email;
  final String? phoneNumber;
  final int accountId;

  //  "source_id": "2648e630-acfe-41d6-a7ed-4d04f6097d5b",
  //   "pubsub_token": "xqQvXk9v7m5RAZmLipptK2p7",
  //   "id": 1,
  //   "name": "<PERSON><PERSON>",
  //   "email": "<EMAIL>",
  //   "phone_number": "+*************"

  Contact({
    required this.id,
    required this.sourceId,
    required this.name,
    required this.email,
    required this.phoneNumber,
    required this.accountId,
    required this.pubsubToken,
  });
}
