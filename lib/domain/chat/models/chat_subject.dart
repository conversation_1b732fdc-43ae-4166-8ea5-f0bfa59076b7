import 'dart:convert';

import 'package:flutter/widgets.dart';

class ChatSubject {
  final int id;
  final String nameEn;
  final String? nameAr;
  final String? nameKu;
  final String type;

  ChatSubject({
    required this.id,
    required this.nameEn,
    this.nameAr,
    this.nameKu,
    required this.type,
  });

  String getName(BuildContext context) {
    if (Localizations.localeOf(context).languageCode == 'ar') {
      return nameAr ?? nameEn;
    } else if (Localizations.localeOf(context).languageCode == 'ku') {
      return nameKu ?? nameEn;
    } else {
      return nameEn;
    }
  }

  ChatSubject copyWith({
    int? id,
    String? nameEn,
    String? nameAr,
    String? nameKu,
    String? type,
  }) {
    return ChatSubject(
      id: id ?? this.id,
      nameEn: nameEn ?? this.nameEn,
      nameAr: nameAr ?? this.nameAr,
      nameKu: nameKu ?? this.nameKu,
      type: type ?? this.type,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'nameEn': nameEn,
      'nameAr': nameAr,
      'nameKu': nameKu,
      'type': type,
    };
  }

  factory ChatSubject.fromMap(Map<String, dynamic> map) {
    return ChatSubject(
      id: map['id']?.toInt() ?? 0,
      nameEn: map['nameEn'] ?? '',
      nameAr: map['nameAr'],
      nameKu: map['nameKu'],
      type: map['type'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  ChatSubject.fromJson(Map<String, dynamic> json)
      : id = json['id'],
        nameEn = json['name_en'] ?? '',
        nameAr = json['name_ar'],
        nameKu = json['name_ku'],
        type = json['type'];

  @override
  String toString() {
    return 'ChatSubject(id: $id, nameEn: $nameEn, nameAr: $nameAr, nameKu: $nameKu, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ChatSubject &&
        other.id == id &&
        other.nameEn == nameEn &&
        other.nameAr == nameAr &&
        other.nameKu == nameKu &&
        other.type == type;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        nameEn.hashCode ^
        nameAr.hashCode ^
        nameKu.hashCode ^
        type.hashCode;
  }
}
