import 'package:goldenprizma/domain/chat/models/attachment.dart';
import 'package:goldenprizma/domain/chat/models/sender.dart';

// Origin or purpose of the message
enum MessageType {
  outgoing(0),
  incoming(1),
  system(2),
  activity(3);

  final int value;

  const MessageType(this.value);

  static MessageType fromValue(int value) {
    return MessageType.values.firstWhere((type) => type.value == value,
        orElse: () => MessageType.outgoing);
  }
}

enum ContentType {
  text,
  image,
  video,
  audio,
  file,
  input,
}

extension ContentTypeExtension on ContentType {
  static ContentType fromString(String type) {
    switch (type) {
      case 'text':
        return ContentType.text;
      case 'image':
        return ContentType.image;
      case 'video':
        return ContentType.video;
      case 'audio':
        return ContentType.audio;
      case 'file':
        return ContentType.file;
      case 'input_csat':
        return ContentType.input;
      default:
        return ContentType.text;
    }
  }
}

class Message {
  final int id;
  final MessageType messageType;
  final ContentType contentType;
  final Map<String, dynamic> contentAttributes;
  final DateTime createdAt;
  final int conversationId;
  final String? content;
  final bool? private;
  final Sender sender;
  final List<Attachment> attachments;

  Message({
    required this.id,
    required this.messageType,
    required this.contentType,
    required this.contentAttributes,
    required this.createdAt,
    required this.conversationId,
    required this.sender,
    this.content,
    this.attachments = const [],
    this.private = false,
  });

  Message.fromJson(Map<String, dynamic> json)
      : id = json['id'],
        content = json['content'],
        messageType = MessageType.fromValue(json['message_type']),
        contentType = ContentTypeExtension.fromString(json['content_type']),
        contentAttributes = json['content_attributes'] ?? {},
        createdAt = DateTime.fromMillisecondsSinceEpoch(json['created_at']),
        conversationId = json['conversation_id'],
        attachments = List.of(json['attachments'] ?? [])
            .map((attachment) => Attachment.fromJson(attachment))
            .toList(),
        private = json['private'],
        sender = Sender.fromJson(json['sender'] ??
            {
              'id': 0,
              'name': 'BOT',
              'thumbnail': 'https://i.imgur.com/oBPXx0D.png',
              'type': 'bot',
            });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'message_type': messageType,
      'content_type': contentType,
      'content_attributes': contentAttributes,
      'created_at': createdAt.toIso8601String(),
      'conversation_id': conversationId,
      'sender': sender.toJson(),
      'private': private,
      'attachments':
          attachments.map((attachment) => attachment.toJson()).toList(),
    };
  }
}
