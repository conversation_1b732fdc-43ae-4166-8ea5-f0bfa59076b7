import 'dart:convert';

import 'package:flutter/widgets.dart';

class Attachment {
  final int id;
  final int messageId;
  final String fileType;
  final int accountId;
  final String? extension;
  final String? dataUrl;
  final String? thumbUrl;
  final int? fileSize;
  final int? width;
  final int? height;

  Attachment({
    required this.id,
    required this.messageId,
    required this.fileType,
    required this.accountId,
    this.extension,
    this.dataUrl,
    this.thumbUrl,
    this.fileSize,
    this.width,
    this.height,
  });

  Attachment copyWith({
    int? id,
    int? messageId,
    String? fileType,
    int? accountId,
    ValueGetter<String?>? extension,
    String? dataUrl,
    String? thumbUrl,
    int? fileSize,
    int? width,
    int? height,
  }) {
    return Attachment(
      id: id ?? this.id,
      messageId: messageId ?? this.messageId,
      fileType: fileType ?? this.fileType,
      accountId: accountId ?? this.accountId,
      extension: extension != null ? extension() : this.extension,
      dataUrl: dataUrl ?? this.dataUrl,
      thumbUrl: thumbUrl ?? this.thumbUrl,
      fileSize: fileSize ?? this.fileSize,
      width: width ?? this.width,
      height: height ?? this.height,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'messageId': messageId,
      'fileType': fileType,
      'accountId': accountId,
      'extension': extension,
      'dataUrl': dataUrl,
      'thumbUrl': thumbUrl,
      'fileSize': fileSize,
      'width': width,
      'height': height,
    };
  }

  factory Attachment.fromMap(Map<String, dynamic> map) {
    return Attachment(
      id: map['id']?.toInt() ?? 0,
      messageId: map['message_id']?.toInt() ?? 0,
      fileType: map['file_type'] ?? '',
      accountId: map['account_id']?.toInt() ?? 0,
      extension: map['extension'],
      dataUrl: map['data_url'] ?? '',
      thumbUrl: map['thumb_url'] ?? '',
      fileSize: map['file_size']?.toInt() ?? 0,
      width: map['width']?.toInt() ?? 0,
      height: map['height']?.toInt() ?? 0,
    );
  }

  String toJson() => json.encode(toMap());

  Attachment.fromJson(Map<String, dynamic> json)
      : id = json['id'],
        messageId = json['message_id'],
        fileType = json['file_type'],
        accountId = json['account_id'],
        extension = json['extension'],
        dataUrl = json['data_url'],
        thumbUrl = json['thumb_url'],
        fileSize = json['file_size'],
        width = json['width'],
        height = json['height'];

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Attachment &&
        other.id == id &&
        other.messageId == messageId &&
        other.fileType == fileType &&
        other.accountId == accountId &&
        other.extension == extension &&
        other.dataUrl == dataUrl &&
        other.thumbUrl == thumbUrl &&
        other.fileSize == fileSize &&
        other.width == width &&
        other.height == height;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        messageId.hashCode ^
        fileType.hashCode ^
        accountId.hashCode ^
        extension.hashCode ^
        dataUrl.hashCode ^
        thumbUrl.hashCode ^
        fileSize.hashCode ^
        width.hashCode ^
        height.hashCode;
  }

  @override
  String toString() {
    return 'Attachment(id: $id, messageId: $messageId, fileType: $fileType, accountId: $accountId, extension: $extension, dataUrl: $dataUrl, thumbUrl: $thumbUrl, fileSize: $fileSize, width: $width, height: $height)';
  }
}
