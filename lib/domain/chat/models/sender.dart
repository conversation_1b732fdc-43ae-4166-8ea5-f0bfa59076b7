enum SenderType { agent, bot, contact }

extension SenderTypeExtension on SenderType {
  static SenderType fromString(String type) {
    switch (type) {
      case 'user':
      case 'Agent':
        return SenderType.agent;
      case 'bot':
        return SenderType.bot;
      case 'AgentBot':
        return SenderType.bot;
      case 'contact':
      default:
        return SenderType.contact;
    }
  }
}

class Sender {
  final int id;
  final String name;
  final String thumbnail;
  final SenderType type;
  final String? identifier;
  final String? phoneNumber;
  final String? email;
  final String? availabilityStatus;
  final Map<String, dynamic>? additionalAttributes;
  final Map<String, dynamic>? customAttributes;

  Sender({
    required this.id,
    required this.name,
    required this.thumbnail,
    required this.type,
    this.customAttributes,
    this.additionalAttributes,
    this.phoneNumber,
    this.identifier,
    this.email,
    this.availabilityStatus,
  });

  factory Sender.fromJson(Map<String, dynamic> json) {
    return Sender(
      additionalAttributes: json['additional_attributes'],
      customAttributes: json['custom_attributes'],
      email: json['email'],
      id: json['id'],
      identifier: json['identifier'],
      name: json['name'],
      phoneNumber: json['phone_number'],
      thumbnail: json['thumbnail'],
      type: SenderTypeExtension.fromString(json['type']),
      availabilityStatus: json['availability_status'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'additional_attributes': additionalAttributes,
      'custom_attributes': customAttributes,
      'email': email,
      'id': id,
      'identifier': identifier,
      'name': name,
      'phone_number': phoneNumber,
      'thumbnail': thumbnail,
      'type': type,
    };
  }
}
