import 'dart:convert';

import 'package:flutter/foundation.dart';

import 'package:goldenprizma/domain/chat/models/message.dart';

enum Status {
  open,
  closed,
  resolved;

  @override
  String toString() {
    switch (this) {
      case Status.open:
        return 'Open';
      case Status.closed:
        return 'Closed';
      case Status.resolved:
        return 'Resolved';
    }
  }
}

extension StatusExtension on Status {
  static Status fromString(String status) {
    switch (status) {
      case 'open':
        return Status.open;
      case 'closed':
        return Status.closed;
      case 'resolved':
        return Status.resolved;
      default:
        return Status.open;
    }
  }
}

class Conversation {
  final int id;
  final String uuid;
  final int inboxId;
  final Status status;
  final List<Message> messages;

  Conversation({
    required this.id,
    required this.uuid,
    required this.inboxId,
    required this.status,
    this.messages = const [],
  });

  Conversation copyWith({
    int? id,
    String? uuid,
    int? inboxId,
    Status? status,
    List<Message>? messages,
  }) {
    return Conversation(
      id: id ?? this.id,
      uuid: uuid ?? this.uuid,
      inboxId: inboxId ?? this.inboxId,
      status: status ?? this.status,
      messages: messages ?? this.messages,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'uuid': uuid,
      'inbox_id': inboxId,
      'status': status,
      'messages': messages.map((x) => x).toList(),
    };
  }

  factory Conversation.fromMap(Map<dynamic, dynamic> map) {
    debugPrint('Conversation.fromMap: $map');
    return Conversation(
      id: map['id']?.toInt() ?? 0,
      uuid: map['uuid'] ?? '',
      inboxId: map['inbox_id']?.toInt() ?? 0,
      status: StatusExtension.fromString(map['status']),
      messages: List<Message>.from(map['messages']?.map((x) => x)),
    );
  }

  String toJson() => json.encode(toMap());

  Conversation.fromJson(Map<String, dynamic> json)
      : id = json['id'],
        uuid = json['uuid'],
        inboxId = json['inbox_id'],
        status = StatusExtension.fromString(json['status'] ?? 'open'),
        messages = List.from(json['messages'] ?? [])
            .map((msg) => Message.fromJson(msg))
            .toList();

  @override
  String toString() {
    return 'Conversation(id: $id, uuid: $uuid, inboxId: $inboxId, status: $status, messages: $messages)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Conversation &&
        other.id == id &&
        other.uuid == uuid &&
        other.inboxId == inboxId &&
        other.status == status &&
        listEquals(other.messages, messages);
  }

  @override
  int get hashCode {
    return id.hashCode ^
        uuid.hashCode ^
        inboxId.hashCode ^
        status.hashCode ^
        messages.hashCode;
  }
}
