import 'package:equatable/equatable.dart';

import 'city.dart';

class District extends Equatable {
  final int id;
  final String name;
  final City city;

  const District(
    this.id,
    this.name,
    this.city,
  );

  District.fromJson(Map<String, dynamic> jsonData)
      : id = jsonData['id'],
        city = City.fromJson(jsonData['city']),
        name = jsonData['name'];

  District copyWith({
    int? id,
    City? city,
    String? name,
  }) {
    return District(
      id ?? this.id,
      name ?? this.name,
      city ?? this.city,
    );
  }

  bool filterByName(String? query) {
    return name.contains(query.toString());
  }

  static List<District> fromJsonList(List? list) {
    if (list == null) return [];
    return list.map((district) => District.fromJson(district)).toList();
  }

  String cityAsString() {
    return '${city.name} - $name';
  }

  bool isEqual(City model) {
    return id == model.id;
  }

  @override
  String toString() => name;

  @override
  List<Object?> get props => [id, name, city];
}
