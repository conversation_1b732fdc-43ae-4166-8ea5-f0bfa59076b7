import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/models/district.dart';

class Address extends Equatable {
  final int id;
  final String street;
  final District district;

  const Address(this.district, this.street, this.id);

  Address.fromJson(Map<String, dynamic> jsonData, this.id)
      : street = jsonData['street'],
        district = District.fromJson(jsonData);

  Map<String, dynamic> toJson() => {
        'street': street,
        'district': district,
      };

  @override
  List<Object?> get props => [id, street, district];
}
