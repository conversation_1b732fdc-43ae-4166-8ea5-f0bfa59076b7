import 'dart:convert';

import 'package:equatable/equatable.dart';

class City extends Equatable {
  final int id;
  final String name;

  const City(
    this.id,
    this.name,
  );

  City copyWith({
    int? id,
    String? name,
  }) {
    return City(
      id ?? this.id,
      name ?? this.name,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
    };
  }

  factory City.fromMap(Map<String, dynamic> map) {
    return City(
      map['id']?.toInt() ?? 0,
      map['name'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  City.fromJson(Map<String, dynamic> source)
      : id = source['id'],
        name = source['name'];

  bool filterByName(String? query) {
    return name.startsWith(
        RegExp(query.toString(), caseSensitive: false, multiLine: true));
  }

  static List<City> fromJsonList(List? list) {
    if (list == null) return [];
    return list.map((city) => City.fromJson(city)).toList();
  }

  String cityAsString() {
    return '#$id $name';
  }

  bool isEqual(City model) {
    return id == model.id;
  }

  @override
  String toString() => name;

  @override
  List<Object> get props => [id, name];
}
