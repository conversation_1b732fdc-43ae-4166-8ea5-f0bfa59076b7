import 'dart:convert';

import 'package:equatable/equatable.dart';

class ExchangeRate extends Equatable {
  final String fromCurreny;
  final String toCurreny;
  final double rate;
  final String formattedRate;

  const ExchangeRate({
    required this.fromCurreny,
    required this.toCurreny,
    required this.rate,
    required this.formattedRate,
  });

  @override
  List<Object> get props => [fromCurreny, toCurreny, rate, formattedRate];

  ExchangeRate copyWith({
    String? fromCurreny,
    String? toCurreny,
    double? rate,
    String? formattedRate,
  }) {
    return ExchangeRate(
      fromCurreny: fromCurreny ?? this.fromCurreny,
      toCurreny: toCurreny ?? this.toCurreny,
      rate: rate ?? this.rate,
      formattedRate: formattedRate ?? this.formattedRate,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'fromCurreny': fromCurreny,
      'toCurreny': toCurreny,
      'rate': rate,
      'formattedRate': formattedRate,
    };
  }

  factory ExchangeRate.fromMap(Map<String, dynamic> map) {
    return ExchangeRate(
      fromCurreny: map['fromCurreny'] ?? '',
      toCurreny: map['toCurreny'] ?? '',
      rate: map['rate']?.toDouble() ?? 0.0,
      formattedRate: map['formattedRate'] ?? '',
    );
  }

  ExchangeRate.fromJson(Map<String, dynamic> jsonData)
      : fromCurreny = jsonData['from_currency'],
        toCurreny = jsonData['to_currency'],
        rate = double.parse(jsonData['rate']),
        formattedRate = jsonData['formatted_rate'];

  @override
  String toString() {
    return 'ExchangeRate(fromCurreny: $fromCurreny, toCurreny: $toCurreny, rate: $rate, formattedRate: $formattedRate)';
  }

  String toJson() => json.encode(toMap());
}
