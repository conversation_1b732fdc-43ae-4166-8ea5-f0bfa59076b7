part of 'exchange_rate_bloc.dart';

enum ExchangeRateStatus { initial, loading, success, failure }

class ExchangeRateState extends Equatable {
  const ExchangeRateState({
    this.status = ExchangeRateStatus.initial,
    this.currency = '',
    this.exchangeRate = const ExchangeRate(
        fromCurreny: 'USD', toCurreny: 'TRY', rate: 0, formattedRate: '0'),
  });

  final ExchangeRateStatus status;
  final ExchangeRate exchangeRate;
  final String currency;

  @override
  List<Object> get props => [status, exchangeRate, currency];

  ExchangeRateState copyWith({
    ExchangeRateStatus? status,
    ExchangeRate? exchangeRate,
    String? currency,
  }) {
    return ExchangeRateState(
        status: status ?? this.status,
        exchangeRate: exchangeRate ?? this.exchangeRate,
        currency: currency ?? this.currency);
  }

  @override
  String toString() =>
      'ExchangeRateState(status: $status, exchangeRate: $exchangeRate, currency: $currency)';
}
