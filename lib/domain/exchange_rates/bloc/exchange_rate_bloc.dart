import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/exchange_rates/models/exchange_rate.dart';
import 'package:goldenprizma/main.dart';
import 'package:goldenprizma/repositories/exchange_rate_repository.dart';

part 'exchange_rate_event.dart';
part 'exchange_rate_state.dart';

class ExchangeRateBloc extends Bloc<ExchangeRateEvent, ExchangeRateState> {
  final ExchangeRateRepostory exchangeRateRepostory =
      getIt.get<ExchangeRateRepostory>();

  ExchangeRateBloc() : super(const ExchangeRateState()) {
    on<ExchangeRateRequestLoad>(_onExchangeRateRequestLoad);
  }

  FutureOr<void> _onExchangeRateRequestLoad(
      ExchangeRateRequestLoad event, Emitter<ExchangeRateState> emit) async {
    if (state.status == ExchangeRateStatus.loading ||
        state.currency == event.currency) {
      return;
    }
    emit(state.copyWith(status: ExchangeRateStatus.loading));

    try {
      ExchangeRate? rate =
          await exchangeRateRepostory.getExchangeRate(currency: event.currency);
      if (rate == null) {
        return emit(
            state.copyWith(status: ExchangeRateStatus.initial, currency: ''));
      }
      emit(state.copyWith(
          status: ExchangeRateStatus.success,
          exchangeRate: rate,
          currency: event.currency));
    } catch (exception) {
      emit(state.copyWith(status: ExchangeRateStatus.failure, currency: ''));
    }
  }
}
