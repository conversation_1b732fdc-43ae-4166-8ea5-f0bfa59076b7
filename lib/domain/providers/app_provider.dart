import 'package:flutter/material.dart';

class AppProvider extends ChangeNotifier {
  Locale _locale = const Locale('en');
  ThemeMode _theme = ThemeMode.system;

  AppProvider(String locale, String theme) {
    _locale = Locale(locale);
    _theme = _getThemeMode(theme);
  }

  void setLocale(Locale locale) {
    _locale = locale;
    notifyListeners();
  }

  Locale get locale => _locale;

  Locale getCurrentLocale() => _locale;

  String get getCurrentLocaleCode => _locale.languageCode;

  String getCurrentFlag() => getFlag(locale);

  String getFlag(Locale locale) {
    return 'assets/images/flags/${locale.languageCode}.png';
  }

  void setTheme(String theme) {
    _theme = _getThemeMode(theme);
    notifyListeners();
  }

  ThemeMode _getThemeMode(String theme) {
    return _theme = theme == 'dark' ? ThemeMode.dark : ThemeMode.light;
  }

  ThemeMode get theme => _theme;

  ThemeMode get currentTheme => _theme;

  isDarkMode(BuildContext context) {
    if (_theme == ThemeMode.system) {
      return MediaQuery.of(context).platformBrightness == Brightness.dark;
    }

    return _theme == ThemeMode.dark;
  }
}
