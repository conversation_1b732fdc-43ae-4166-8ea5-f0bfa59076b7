import 'dart:convert';

import 'package:equatable/equatable.dart';

class Website extends Equatable {
  final int id;
  final int countryId;
  final String name;
  final String? logo;
  final bool isPopular;
  final String url;
  final String? nameScript;
  final String? priceScript;
  final String? imageScript;

  const Website({
    required this.id,
    required this.name,
    required this.isPopular,
    required this.url,
    required this.countryId,
    this.logo,
    this.nameScript,
    this.priceScript,
    this.imageScript,
  });

  Website copyWith({
    int? id,
    String? name,
    String? logo,
    bool? isPopular,
    String? url,
    int? countryId,
    String? nameScript,
    String? priceScript,
    String? imageScript,
  }) {
    return Website(
      id: id ?? this.id,
      name: name ?? this.name,
      logo: logo ?? this.logo,
      isPopular: isPopular ?? this.isPopular,
      url: url ?? this.url,
      countryId: countryId ?? this.countryId,
      nameScript: nameScript ?? this.nameScript,
      priceScript: priceScript ?? this.priceScript,
      imageScript: imageScript ?? this.imageScript,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'logo': logo,
      'isPopular': isPopular,
      'url': url,
      'country_id': countryId,
      'name_script': nameScript,
      'price_script': priceScript,
      'image_script': imageScript,
    };
  }

  factory Website.fromMap(Map<String, dynamic> map) {
    return Website(
      id: map['id']?.toInt() ?? 0,
      name: map['name'] ?? '',
      logo: map['logo'],
      isPopular: map['isPopular'] ?? false,
      url: map['url'] ?? '',
      countryId: map['country_id']?.toInt() ?? 0,
      nameScript: map['name_script'],
      priceScript: map['price_script'],
      imageScript: map['image_script'],
    );
  }

  String toJson() => json.encode(toMap());

  factory Website.fromJson(Map<String, dynamic> source) => Website(
        id: source['id']?.toInt(),
        isPopular: source['is_popular'] ?? false,
        name: source['name'],
        url: source['url'],
        logo: source['logo'],
        countryId: source['country_id']?.toInt(),
        nameScript: source['name_script'],
        priceScript: source['price_script'],
        imageScript: source['image_script'],
      );

  @override
  String toString() {
    return 'Website(id: $id, name: $name, logo: $logo, isPopular: $isPopular, url: $url, countryId: $countryId, nameScript: $nameScript, priceScript: $priceScript, imageScript: $imageScript)';
  }

  @override
  List<Object> get props {
    return [
      id,
      name,
      isPopular,
      url,
      countryId,
    ];
  }
}
