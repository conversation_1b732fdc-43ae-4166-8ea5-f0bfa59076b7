import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/websites/models/website.dart';

class CountryWebsites extends Equatable {
  final String name;
  final List<Website> websites;

  const CountryWebsites({
    required this.name,
    required this.websites,
  });

  CountryWebsites copyWith({
    int? id,
    String? name,
    List<Website>? websites,
  }) {
    return CountryWebsites(
      name: name ?? this.name,
      websites: websites ?? this.websites,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'websites': websites,
    };
  }

  factory CountryWebsites.fromMap(Map<String, dynamic> map) {
    return CountryWebsites(
      name: map['name'] ?? '',
      websites: map['websites'],
    );
  }

  String toJson() => json.encode(toMap());

  factory CountryWebsites.fromJson(Map<String, dynamic> source) =>
      CountryWebsites(
        name: source['name'],
        websites: List.of(source['websites'])
            .map((data) => Website.fromJson(data))
            .toList(),
      );

  @override
  String toString() {
    return 'CountryWebsites(name: $name, websites: $websites)';
  }

  @override
  List<Object> get props {
    return [
      name,
      websites,
    ];
  }
}
