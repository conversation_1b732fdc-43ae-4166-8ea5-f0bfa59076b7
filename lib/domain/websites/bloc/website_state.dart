part of 'website_bloc.dart';

enum WebsiteStatus { initial, success, refresh, failure }

class WebsiteState extends Equatable {
  const WebsiteState({
    this.status = WebsiteStatus.initial,
    this.countryWebsites = const [],
    this.reachedMax = false,
    this.isFetching = false,
    this.failMessage = '',
    this.filters = const {},
  });

  final WebsiteStatus status;
  final List<CountryWebsites> countryWebsites;
  final bool reachedMax;
  final bool isFetching;
  final String failMessage;
  final Map<String, dynamic> filters;

  List<Website> get allWebsites => countryWebsites
      .map((country) => country.websites)
      .expand((websites) => websites)
      .toList();

  Website? findWebsiteById(int id) =>
      allWebsites.firstWhere((element) => element.id == id);

  Website? findWebsiteByUrl(String url) {
    Uri uri = Uri.parse(url);
    final matches =
        allWebsites.where((element) => element.url.contains(uri.host));

    if (matches.isEmpty) return null;

    // If one match is found then we will just return it
    if (matches.length == 1) return matches.first;

    // If multiple matches are found then we will check URL path segments
    final pathSegments = uri.pathSegments;

    if (pathSegments.isNotEmpty) {
      // Look for matches based on first path segment (country/region code)
      final firstSegment = pathSegments.first.toLowerCase();

      final pathMatches = matches.where((element) {
        try {
          final websiteUri = Uri.parse(element.url);
          final websiteSegments = websiteUri.pathSegments;

          // Check if website has path segments and first segments match
          if (websiteSegments.isNotEmpty) {
            return websiteSegments.first.toLowerCase() == firstSegment;
          }

          // If website has no path segments, check if the first segment is in the URL
          return element.url.toLowerCase().contains('/$firstSegment/') ||
              element.url.toLowerCase().endsWith('/$firstSegment');
        } catch (e) {
          debugPrint('Error parsing website URL: ${element.url}, error: $e');
          return false;
        }
      });

      if (pathMatches.isNotEmpty) {
        if (pathMatches.length == 1) return pathMatches.first;

        // If multiple path matches, prefer exact host match
        final exactHostMatches = pathMatches.where((element) {
          try {
            final elUri = Uri.parse(element.url);
            return elUri.host == uri.host;
          } catch (e) {
            return false;
          }
        });

        if (exactHostMatches.isNotEmpty) return exactHostMatches.first;
        return pathMatches.first;
      }
    }

    // Fallback to exact host match
    final hostMatches = matches.where((element) {
      try {
        final elUri = Uri.parse(element.url);
        return elUri.host == uri.host;
      } catch (e) {
        return false;
      }
    });

    if (hostMatches.isNotEmpty) return hostMatches.first;

    // Last resort - return first match
    return matches.first;
  }

  @override
  List<Object> get props {
    return [
      status,
      countryWebsites,
      reachedMax,
      isFetching,
      failMessage,
      filters,
    ];
  }

  WebsiteState copyWith({
    WebsiteStatus? status,
    List<CountryWebsites>? countryWebsites,
    bool? reachedMax,
    bool? isFetching,
    String? failMessage,
    Map<String, dynamic>? filters,
  }) {
    return WebsiteState(
      status: status ?? this.status,
      countryWebsites: countryWebsites ?? this.countryWebsites,
      reachedMax: reachedMax ?? this.reachedMax,
      isFetching: isFetching ?? this.isFetching,
      failMessage: failMessage ?? this.failMessage,
      filters: filters ?? this.filters,
    );
  }

  @override
  String toString() {
    return 'WebsiteState(status: $status, countryWebsites: $countryWebsites, reachedMax: $reachedMax, isFetching: $isFetching, failMessage: $failMessage, filters: $filters)';
  }
}
