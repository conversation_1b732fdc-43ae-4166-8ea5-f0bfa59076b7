part of 'website_bloc.dart';

abstract class WebsiteEvent extends Equatable {
  const WebsiteEvent();

  @override
  List<Object> get props => [];
}

class PopularWebsiteFetched extends WebsiteEvent {}

class PopularWebsiteRefreshed extends WebsiteEvent {}

class WebsiteCountryChanged extends WebsiteEvent {
  const WebsiteCountryChanged({required this.country});

  final Map<String, dynamic> country;

  @override
  List<Object> get props => [country];
}

class WebsiteSearchChanged extends WebsiteEvent {
  const WebsiteSearchChanged({required this.search});

  final String search;

  @override
  List<Object> get props => [search];
}
