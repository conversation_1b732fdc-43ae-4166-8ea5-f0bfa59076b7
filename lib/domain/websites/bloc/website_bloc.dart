import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:goldenprizma/domain/websites/country_websites.dart';
import 'package:goldenprizma/domain/websites/models/website.dart';
import 'package:goldenprizma/helpers/event_transformers.dart';
import 'package:goldenprizma/repositories/website_repository.dart';
part 'website_event.dart';
part 'website_state.dart';

class WebsiteBloc extends Bloc<WebsiteEvent, WebsiteState> {
  final WebsiteRepository repository;

  WebsiteBloc(this.repository) : super(const WebsiteState()) {
    on<PopularWebsiteFetched>(_onPopularWebsiteFetched);
    on<PopularWebsiteRefreshed>(_onPopularWebsiteRefresh,
        transformer: debounce(const Duration(milliseconds: 300)));
    on<WebsiteCountryChanged>(_onWebsiteCountryChanged);
    on<WebsiteSearchChanged>(_onWebsiteSearchChanged,
        transformer: debounce(const Duration(milliseconds: 300)));
  }

  Future<void> _onPopularWebsiteFetched(
    PopularWebsiteFetched event,
    Emitter<WebsiteState> emit,
  ) async {
    try {
      if (state.status == WebsiteStatus.initial &&
          state.countryWebsites.isEmpty) {
        List<CountryWebsites> countryWebsites =
            await fetchWebsites(queryParameters: state.filters);
        emit(state.copyWith(
          status: WebsiteStatus.success,
          countryWebsites: List.of(state.countryWebsites)
            ..addAll(countryWebsites),
        ));
        debugPrint(state.allWebsites.toString());
      }
    } catch (error) {
      emit(state.copyWith(
        failMessage: 'An error occured during fetch data',
        status: WebsiteStatus.failure,
      ));
    }
  }

  Future<void> _onPopularWebsiteRefresh(
    PopularWebsiteRefreshed event,
    Emitter<WebsiteState> emit,
  ) async {
    try {
      emit(state.copyWith(
        status: WebsiteStatus.refresh,
      ));
      List<CountryWebsites> countryWebsites =
          await fetchWebsites(queryParameters: state.filters);
      emit(state.copyWith(
        status: WebsiteStatus.success,
        countryWebsites: countryWebsites.toList(),
      ));
    } catch (e) {
      emit(state.copyWith(
        failMessage: 'An error occured during fetch data',
        status: WebsiteStatus.failure,
      ));
    }
  }

  FutureOr<void> _onWebsiteCountryChanged(
      WebsiteCountryChanged event, Emitter<WebsiteState> emit) async {
    emit(state.copyWith(filters: event.country));
  }

  Future<List<CountryWebsites>> fetchWebsites(
      {Map<String, dynamic>? queryParameters}) async {
    return await repository.fetchPopulars(queryParams: queryParameters);
  }

  FutureOr<void> _onWebsiteSearchChanged(
    WebsiteSearchChanged event,
    Emitter<WebsiteState> emit,
  ) async {
    try {
      emit(state.copyWith(status: WebsiteStatus.refresh));
      List<CountryWebsites> countryWebsites = await fetchWebsites(
          queryParameters: {'search': event.search}..addAll(state.filters));
      emit(state.copyWith(
        status: WebsiteStatus.success,
        countryWebsites: countryWebsites.toList(),
      ));
    } catch (e) {
      emit(state.copyWith(
        failMessage: 'An error occured during fetch data',
        status: WebsiteStatus.failure,
      ));
    }
  }
}
