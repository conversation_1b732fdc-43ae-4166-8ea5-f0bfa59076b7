import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/chat/models/chat_subject.dart';
import 'package:goldenprizma/repositories/chat_subjects_repository.dart';

part 'chat_subjects_event.dart';
part 'chat_subjects_state.dart';

class ChatSubjectsBloc extends Bloc<ChatSubjectsEvent, ChatSubjectsState> {
  final ChatSubjectsRepository repository;

  ChatSubjectsBloc({required this.repository})
      : super(const ChatSubjectsState()) {
    on<ChatSubjectsRequested>((event, emit) async {
      if (state.status == ChatSubjectStatus.loading) {
        return;
      }

      emit(state.copyWith(status: ChatSubjectStatus.loading));

      try {
        final subjects = await repository.getAll(type: event.type);

        emit(state.copyWith(
          subjects: subjects,
          status: ChatSubjectStatus.success,
        ));
      } on Exception {
        emit(state.copyWith(status: ChatSubjectStatus.failure));
      }
    });
  }
}
