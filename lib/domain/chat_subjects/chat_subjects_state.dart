part of 'chat_subjects_bloc.dart';

enum ChatSubjectStatus { initial, loading, success, failure }

class ChatSubjectsState extends Equatable {
  final List<ChatSubject> subjects;
  final ChatSubjectStatus status;

  const ChatSubjectsState({
    this.subjects = const [],
    this.status = ChatSubjectStatus.initial,
  });

  @override
  List<Object?> get props => [subjects, status];

  ChatSubjectsState copyWith({
    List<ChatSubject>? subjects,
    ChatSubjectStatus? status,
  }) {
    return ChatSubjectsState(
      subjects: subjects ?? this.subjects,
      status: status ?? this.status,
    );
  }
}
