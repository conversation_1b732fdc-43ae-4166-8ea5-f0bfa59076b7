import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:goldenprizma/repositories/auth_repository.dart';

part 'change_password_event.dart';
part 'change_password_state.dart';

class ChangePasswordBloc
    extends Bloc<ChangePasswordEvent, ChangePasswordState> {
  final AuthRepository _authRepository;

  ChangePasswordBloc(this._authRepository)
      : super(const ChangePasswordState()) {
    on<ChangePasswordSubmitted>((event, emit) async {
      debugPrint('ChangePasswordSubmitted event received');
      try {
        debugPrint('ChangePasswordSubmitted event received');
        if (state.status == ChangePasswordStatus.loading) {
          return;
        }

        emit(state.copyWith(status: ChangePasswordStatus.loading));
        final response = await _authRepository.changePasswordUsingOldPassword(
          oldPassword: event.oldPassword,
          newPassword: event.newPassword,
          confirmPassword: event.confirmPassword,
        );
        emit(
          state.copyWith(
            successMessage: response['message'],
            status: ChangePasswordStatus.success,
          ),
        );
      } catch (e) {
        if (e is DioException) {
          if (e.response?.statusCode != 500) {
            emit(state.copyWith(
                errorMessage: e.response?.data['error'],
                status: ChangePasswordStatus.failure));
            return;
          }
        }
        emit(state.copyWith(
            errorMessage: 'Something went wrong',
            status: ChangePasswordStatus.failure));
      }
    });
  }
}
