part of 'authentication_bloc.dart';

enum AuthenticationStatus { unknown, authenticated, unauthenticated }

class AuthenticationState extends Equatable {
  const AuthenticationState({
    this.status = AuthenticationStatus.unknown,
    this.user = const User(
      id: 0,
      name: '-',
      email: '-',
      phone: '',
      profilePhotoUrl: '-',
      cid: '0',
      formattedBalance: '\$0.00',
      customerType: '-',
      customerTypeColor: Colors.white,
      branchId: 0,
    ),
  });

  final AuthenticationStatus status;
  final User user;

  AuthenticationState copyWith({
    AuthenticationStatus? status,
    User? user,
  }) {
    return AuthenticationState(
      status: status ?? this.status,
      user: user ?? this.user,
    );
  }

  @override
  List<Object> get props => [status, user];

  bool get isAuthenticated => status == AuthenticationStatus.authenticated;
}
