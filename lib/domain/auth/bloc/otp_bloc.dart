import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/helpers/logger.dart';
import 'package:goldenprizma/main.dart';
import 'package:goldenprizma/repositories/auth_repository.dart';

part 'otp_event.dart';
part 'otp_state.dart';

class OtpBloc extends Bloc<OtpEvent, OtpState> {
  final AuthRepository _authRepository = getIt.get<AuthRepository>();

  OtpBloc() : super(const OtpState()) {
    on<OTPRequested>(_onOTPRequested);
    on<OTPVerificationRequested>(_onOTPVerificationRequested);
    on<PasswordOTPVerificationRequested>(_onPasswordOTPVerificationRequested);
  }

  Future<void> _onPasswordOTPVerificationRequested(
      PasswordOTPVerificationRequested event, Emitter<OtpState> emit) async {
    try {
      emit(state.copyWith(status: OtpStatus.request));
      final res = await _authRepository.forgetPasswordOtpVerify(
        phoneNumber: event.phoneNumber,
        code: event.code,
        countryCode: event.dialCode,
      );
      logger(res.toString());
      emit(state.copyWith(
          token: res,
          status: OtpStatus.verify,
          errorMessage: null,
          successMessage: null));
    } catch (error) {
      if (error is DioException) {
        emit(state.copyWith(
          status: OtpStatus.failure,
          errorMessage: error.response?.data['error'] ?? 'Invalid code',
        ));
      } else {
        emit(state.copyWith(
          status: OtpStatus.failure,
          errorMessage: error.toString(),
        ));
      }
    }
  }

  Future<void> _onOTPVerificationRequested(
      OTPVerificationRequested event, Emitter<OtpState> emit) async {
    try {
      emit(state.copyWith(status: OtpStatus.request));
      final res = await _authRepository.verifyOtp(
        phoneNumber: event.phoneNumber,
        code: event.code,
        countryCode: event.dialCode,
      );
      logger(res.toString());
      emit(state.copyWith(
          status: OtpStatus.verify, errorMessage: null, successMessage: null));
    } catch (error) {
      if (error is DioException) {
        emit(state.copyWith(
          status: OtpStatus.failure,
          errorMessage: error.response?.data['error'] ?? '',
        ));
      } else {
        emit(state.copyWith(
          status: OtpStatus.failure,
          errorMessage: error.toString(),
        ));
      }
    }
  }

  Future<void> _onOTPRequested(
      OTPRequested event, Emitter<OtpState> emit) async {
    try {
      emit(state.copyWith(
          countryCode: event.countryCode,
          dialCode: event.dialCode,
          phoneNumber: event.phoneNumber,
          status: OtpStatus.request));
      final res = await _authRepository.sendOtp(
        phoneNumber: event.phoneNumber,
        countryCode: event.dialCode,
        scope: event.scope,
      );
      logger(res.toString());
      emit(state.copyWith(
          status: OtpStatus.success, errorMessage: null, successMessage: null));
    } catch (error) {
      if (error is DioException) {
        emit(state.copyWith(
          status: OtpStatus.failure,
          errorMessage: error.response?.data['error'] ?? '',
        ));
      } else {
        emit(state.copyWith(
          status: OtpStatus.failure,
          errorMessage: error.toString(),
        ));
      }
    }
  }
}
