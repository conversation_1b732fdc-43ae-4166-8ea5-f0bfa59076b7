part of 'login_bloc.dart';

class LoginState extends Equatable {
  const LoginState({
    this.password = const PasswordInput.pure(),
    this.phone = const PhoneNumberInput.pure(),
    this.status = FormzStatus.pure,
    this.hasServerErrror = false,
    this.serverErrorMessage = '',
    this.dialCode = '+964',
  });

  final PasswordInput password;
  final PhoneNumberInput phone;
  final String dialCode;
  final FormzStatus status;
  final bool hasServerErrror;
  final String serverErrorMessage;

  @override
  List<Object> get props {
    return [
      password,
      phone,
      dialCode,
      status,
      hasServerErrror,
      serverErrorMessage,
    ];
  }

  LoginState copyWith({
    PasswordInput? password,
    PhoneNumberInput? phone,
    FormzStatus? status,
    bool? hasServerErrror,
    String? serverErrorMessage,
    String? dialCode,
  }) {
    return LoginState(
      password: password ?? this.password,
      dialCode: dialCode ?? this.dialCode,
      phone: phone ?? this.phone,
      status: status ?? this.status,
      hasServerErrror: hasServerErrror ?? this.hasServerErrror,
      serverErrorMessage: serverErrorMessage ?? this.serverErrorMessage,
    );
  }

  @override
  String toString() {
    return 'LoginState(dialCode:$dialCode, password:$password, phone:$phone status:$status hasServerErrror:$hasServerErrror serverErrorMessage:$serverErrorMessage)';
  }
}
