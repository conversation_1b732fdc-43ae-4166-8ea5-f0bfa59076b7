import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/helpers/logger.dart';
import 'package:goldenprizma/main.dart';
import 'package:goldenprizma/presentation/views/forms/auth/password_input.dart';
import 'package:goldenprizma/presentation/views/forms/auth/phone_number_input.dart';
import 'package:goldenprizma/repositories/auth_repository.dart';
import 'package:goldenprizma/repositories/token_repository.dart';

part 'login_event.dart';
part 'login_state.dart';

class LoginBloc extends Bloc<LoginEvent, LoginState> {
  final TokenRepository tokenRepository = getIt.get<TokenRepository>();
  final AuthRepository authRepository = getIt.get<AuthRepository>();

  LoginBloc() : super(const LoginState()) {
    on<LoginUsernameChanged>(_onUsernameChanged);
    on<LoginPasswordChanged>(_onPasswordChanged);
    on<LoginSubmitted>(_onLoginSubmitted);
  }

  FutureOr<void> _onUsernameChanged(
      LoginUsernameChanged event, Emitter<LoginState> emit) {
    final username = PhoneNumberInput.dirty(event.phone);
    emit(state.copyWith(
      phone: username,
      dialCode: event.duialCode,
      status: Formz.validate([username, state.password]),
    ));
    logger("duialCode:${state.dialCode}  state:  ${state.toString()}");
  }

  FutureOr<void> _onPasswordChanged(
      LoginPasswordChanged event, Emitter<LoginState> emit) {
    final password = PasswordInput.dirty(event.password);
    emit(state.copyWith(
      password: password,
      status: Formz.validate([password, state.phone]),
    ));
  }

  FutureOr<void> _onLoginSubmitted(
      LoginSubmitted event, Emitter<LoginState> emit) async {
    logger("state duialCode:${state.dialCode}  state:  ${state.toString()}");
    if (state.status.isSubmissionInProgress || state.status.isInvalid) {
      return;
    }

    emit(state.copyWith(status: FormzStatus.submissionInProgress));
    try {
      String token = await authRepository.login(
          dialCode: state.dialCode,
          username: state.phone.value,
          password: state.password.value);

      await tokenRepository.set(token);

      emit(state.copyWith(
        status: FormzStatus.submissionSuccess,
        hasServerErrror: false,
      ));
    } catch (error) {
      logger("message: ${error.toString()}");
      if (error is DioException) {
        emit(state.copyWith(
          status: FormzStatus.submissionFailure,
          hasServerErrror: true,
          serverErrorMessage: error.response?.data['message'] ?? '',
        ));
      } else {
        emit(state.copyWith(status: FormzStatus.submissionFailure));
      }
    }
  }
}
