import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/main.dart';
import 'package:goldenprizma/presentation/views/forms/auth/profile_form.dart';
import 'package:goldenprizma/repositories/auth_repository.dart';

part 'profile_event.dart';
part 'profile_state.dart';

class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  final AuthRepository authRepository = getIt.get<AuthRepository>();
  ProfileBloc() : super(const ProfileState()) {
    on<ProfileFormChanged>(_onProfileFormChanged);
    on<ProfileFormSubmit>(_onProfileSubmit);
  }

  FutureOr<void> _onProfileFormChanged(
      ProfileFormChanged event, Emitter<ProfileState> emit) {
    emit(
      state.copyWith(
        form: event.form,
        status: Formz.validate(state.form.inputs),
      ),
    );
  }

  FutureOr<void> _onProfileSubmit(
      ProfileFormSubmit event, Emitter<ProfileState> emit) async {
    emit(
      state.copyWith(
        status: Formz.validate(state.form.inputs),
      ),
    );

    if (state.form.status.isValidated) {
      emit(state.copyWith(status: FormzStatus.submissionInProgress));
      try {
        String successMessage = await authRepository.updateProfile({
          'email': state.form.emailInput.value,
        });
        emit(state.copyWith(
          status: FormzStatus.submissionSuccess,
          hasServerError: false,
          errorMessage: '',
          successMessage: successMessage,
        ));
      } catch (error) {
        if (error is DioException) {
          emit(state.copyWith(
            status: FormzStatus.submissionFailure,
            hasServerError: true,
            errorMessage: error.response?.data['error'] ?? '',
          ));
        } else {
          emit(state.copyWith(
              status: FormzStatus.submissionFailure, hasServerError: false));
        }
      }
    }
  }
}
