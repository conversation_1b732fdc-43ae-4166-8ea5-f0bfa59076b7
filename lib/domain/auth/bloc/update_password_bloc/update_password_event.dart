part of 'update_password_bloc.dart';

abstract class UpdatePasswordEvent extends Equatable {
  const UpdatePasswordEvent();

  @override
  List<Object> get props => [];
}

class UpdatePasswordRequested extends UpdatePasswordEvent {
  const UpdatePasswordRequested(
      {required this.password,
      required this.passwordConfirmation,
      required this.code,
      required this.phoneNumber,
      required this.countryCode,
      required this.token});

  final String password;
  final String token;
  final String code;
  final String phoneNumber;
  final String countryCode;
  final String passwordConfirmation;

  @override
  List<Object> get props =>
      [password, passwordConfirmation, token, code, phoneNumber, countryCode];
}
