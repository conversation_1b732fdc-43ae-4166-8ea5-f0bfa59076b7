part of 'update_password_bloc.dart';

enum UpdatePasswordStatus { initial, failure, request, updated }

class UpdatePasswordState extends Equatable {
  const UpdatePasswordState({
    this.status = UpdatePasswordStatus.initial,
    this.successMessage = '',
    this.errorMessage = '',
  });

  final UpdatePasswordStatus status;
  final String successMessage;
  final String errorMessage;

  @override
  List<Object> get props => [status, successMessage, errorMessage];

  UpdatePasswordState copyWith({
    UpdatePasswordStatus? status,
    String? successMessage,
    String? errorMessage,
  }) {
    return UpdatePasswordState(
      status: status ?? this.status,
      successMessage: successMessage ?? this.successMessage,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  String toString() =>
      'OtpState(status: $status, successMessage: $successMessage, errorMessage: $errorMessage)';
}
