import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/main.dart';
import 'package:goldenprizma/repositories/auth_repository.dart';
import 'package:goldenprizma/helpers/logger.dart';

part 'update_password_event.dart';
part 'update_password_state.dart';

class UpdatePasswordBloc
    extends Bloc<UpdatePasswordEvent, UpdatePasswordState> {
  final AuthRepository _authRepository = getIt.get<AuthRepository>();

  UpdatePasswordBloc() : super(const UpdatePasswordState()) {
    on<UpdatePasswordRequested>(_onOTPRequested);
  }

  Future<void> _onOTPRequested(
      UpdatePasswordRequested event, Emitter<UpdatePasswordState> emit) async {
    try {
      emit(state.copyWith(status: UpdatePasswordStatus.request));
      final res = await _authRepository.changePassword(
          phoneNumber: event.phoneNumber,
          countryCode: event.countryCode,
          code: event.code,
          password: event.password,
          passwordConfirmation: event.passwordConfirmation,
          token: event.token);
      logger(res.toString());
      emit(state.copyWith(
          status: UpdatePasswordStatus.updated,
          errorMessage: null,
          successMessage: res));
    } catch (error) {
      if (error is DioException) {
        emit(state.copyWith(
          status: UpdatePasswordStatus.failure,
          errorMessage: error.response?.data['error'] ?? '',
        ));
      } else {
        emit(state.copyWith(
          status: UpdatePasswordStatus.failure,
          errorMessage: error.toString(),
        ));
      }
    }
  }
}
