part of 'profile_avatar_bloc.dart';

enum ProfileAvatarStatus { initial, updating, success, failure }

class ProfileAvatarState extends Equatable {
  final ProfileAvatarStatus status;
  final bool hasServerError;
  final String serverErrorMessage;
  final String successMessage;

  const ProfileAvatarState({
    this.status = ProfileAvatarStatus.initial,
    this.hasServerError = false,
    this.serverErrorMessage = '',
    this.successMessage = '',
  });

  @override
  List<Object> get props =>
      [status, hasServerError, serverErrorMessage, successMessage];

  ProfileAvatarState copyWith({
    ProfileAvatarStatus? status,
    bool? hasServerError,
    String? serverErrorMessage,
    String? successMessage,
  }) {
    return ProfileAvatarState(
      status: status ?? this.status,
      hasServerError: hasServerError ?? this.hasServerError,
      serverErrorMessage: serverErrorMessage ?? this.serverErrorMessage,
      successMessage: successMessage ?? this.successMessage,
    );
  }
}
