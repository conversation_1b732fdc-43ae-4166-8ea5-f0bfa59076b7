import 'dart:async';
import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/main.dart';
import 'package:goldenprizma/presentation/views/forms/auth/city_input.dart';
import 'package:goldenprizma/presentation/views/forms/auth/fullname_input.dart';
import 'package:goldenprizma/presentation/views/forms/auth/password_input.dart';
import 'package:goldenprizma/presentation/views/forms/auth/phone_number_input.dart';
import 'package:goldenprizma/presentation/views/forms/auth/register_form_model.dart';
import 'package:goldenprizma/repositories/auth_repository.dart';
import 'package:goldenprizma/repositories/token_repository.dart';
import 'package:goldenprizma/services/push_notification_service.dart';
import 'package:goldenprizma/support/api_secret.dart';

part 'register_event.dart';
part 'register_state.dart';

class RegisterBloc extends Bloc<RegisterEvent, RegisterState> {
  final AuthRepository _authRepository = getIt.get<AuthRepository>();
  final TokenRepository tokenRepository = getIt.get<TokenRepository>();
  final PushNotificationService _pushNotificationService =
      getIt.get<PushNotificationService>();
  RegisterBloc() : super(const RegisterState()) {
    on<RegisterFormChanged>(_onRegisterFormChanged);
    on<RegisterFormSubmitted>(_onRegisterFormSubmitted);
    on<RegisterFormReset>(_onRegisterFormReset);
  }

  FutureOr<void> _onRegisterFormChanged(
    RegisterFormChanged event,
    Emitter<RegisterState> emit,
  ) async {
    emit(state.copyWith(
      status: Formz.validate(event.form.inputs),
      form: event.form,
    ));
  }

  FutureOr<void> _onRegisterFormSubmitted(
    RegisterFormSubmitted event,
    Emitter<RegisterState> emit,
  ) async {
    emit(state.copyWith(
      status: Formz.validate(state.form.inputs),
      form: state.form.copyWith(
        fullNameInput: FullNameInput.dirty(state.form.fullNameInput.value),
        phoneNumberInput:
            PhoneNumberInput.dirty(state.form.phoneNumberInput.value),
        cityInput: CityInput.dirty(state.form.cityInput.value),
        passwordInput: PasswordInput.dirty(state.form.passwordInput.value),
        confirmPasswordInput:
            PasswordInput.dirty(state.form.confirmPasswordInput.value),
      ),
    ));

    if (state.form.status.isValidated) {
      emit(state.copyWith(status: FormzStatus.submissionInProgress));
      try {
        Map<String, dynamic> response = await _authRepository.register(
          {
            'name': state.form.fullNameInput.value,
            'city_id': state.form.cityInput.value?.id,
            'phone': state.form.phoneNumberInput.value,
            'phone_code': state.form.phoneCodeInput.value,
            'password': state.form.passwordInput.value,
            'password_confirmation': state.form.confirmPasswordInput.value,
            'device': await getDeviceName(),
            'fcm_token': await _pushNotificationService.getToken(),
            ApiSecret.secretKey: ApiSecret.secret,
          },
        );

        await tokenRepository.set(response['api_token']);

        emit(state.copyWith(
          status: FormzStatus.submissionSuccess,
          apiSuccessMessage: response['message'],
          apiToken: response['api_token'],
          hasServerError: false,
        ));
      } catch (error) {
        if (error is DioException) {
          emit(state.copyWith(
            status: FormzStatus.submissionFailure,
            apiErrorMessage: error.response?.data['error'] ?? '',
            hasServerError: true,
          ));
        } else {
          emit(state.copyWith(
            status: FormzStatus.submissionFailure,
          ));
        }
      }
    }
  }

  Future<String> getDeviceName() async {
    DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
    String deviceName = 'phone';

    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfoPlugin.androidInfo;
      deviceName = androidInfo.model;
    }

    if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfoPlugin.iosInfo;
      deviceName = iosInfo.utsname.machine;
    }
    return deviceName;
  }

  FutureOr<void> _onRegisterFormReset(
      RegisterFormReset event, Emitter<RegisterState> emit) {
    emit(state.copyWith(
      status: FormzStatus.pure,
      form: state.form.copyWith(
        fullNameInput: const FullNameInput.pure(),
        phoneNumberInput: const PhoneNumberInput.pure(),
        cityInput: const CityInput.pure(),
        passwordInput: const PasswordInput.pure(),
        confirmPasswordInput: const PasswordInput.pure(),
      ),
      apiErrorMessage: '',
      hasServerError: false,
      apiSuccessMessage: '',
      apiToken: '',
    ));
  }
}
