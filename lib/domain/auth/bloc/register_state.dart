part of 'register_bloc.dart';

class RegisterState extends Equatable {
  const RegisterState({
    this.form = const RegisterFormModel(),
    this.status = FormzStatus.pure,
    this.hasServerError = false,
    this.apiErrorMessage = '',
    this.apiSuccessMessage = '',
    this.apiToken = '',
  });

  final RegisterFormModel form;
  final FormzStatus status;
  final bool hasServerError;
  final String apiErrorMessage;
  final String apiSuccessMessage;
  final String apiToken;

  @override
  List<Object> get props {
    return [
      form,
      status,
      hasServerError,
      apiErrorMessage,
      apiSuccessMessage,
      apiToken,
    ];
  }

  RegisterState copyWith({
    RegisterFormModel? form,
    FormzStatus? status,
    bool? hasServerError,
    String? apiErrorMessage,
    String? apiSuccessMessage,
    String? apiToken,
  }) {
    return RegisterState(
      form: form ?? this.form,
      status: status ?? this.status,
      hasServerError: hasServerError ?? this.hasServerError,
      apiErrorMessage: apiErrorMessage ?? this.apiErrorMessage,
      apiSuccessMessage: apiSuccessMessage ?? this.apiSuccessMessage,
      apiToken: apiToken ?? this.apiToken,
    );
  }

  @override
  String toString() {
    return 'RegisterState(form: $form, status: $status, hasServerError: $hasServerError, apiErrorMessage: $apiErrorMessage, apiSuccessMessage: $apiSuccessMessage, apiToken: $apiToken)';
  }
}
