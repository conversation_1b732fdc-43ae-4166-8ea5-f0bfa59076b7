part of 'profile_bloc.dart';

class ProfileState extends Equatable {
  const ProfileState({
    this.form = const ProfileForm(),
    this.status = FormzStatus.pure,
    this.hasServerError = false,
    this.errorMessage = '',
    this.successMessage = '',
  });

  final ProfileForm form;
  final FormzStatus status;
  final bool hasServerError;
  final String errorMessage;
  final String successMessage;

  @override
  List<Object> get props {
    return [
      form,
      status,
      hasServerError,
      errorMessage,
      successMessage,
    ];
  }

  ProfileState copyWith({
    ProfileForm? form,
    FormzStatus? status,
    bool? hasServerError,
    String? errorMessage,
    String? successMessage,
  }) {
    return ProfileState(
      form: form ?? this.form,
      status: status ?? this.status,
      hasServerError: hasServerError ?? this.hasServerError,
      errorMessage: errorMessage ?? this.errorMessage,
      successMessage: successMessage ?? this.successMessage,
    );
  }

  @override
  String toString() {
    return 'ProfileState(form: $form, status: $status, hasServerError: $hasServerError, errorMessage: $errorMessage, successMessage: $successMessage)';
  }
}
