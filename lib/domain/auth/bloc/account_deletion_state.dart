part of 'account_deletion_bloc.dart';

enum AccountDeletionStatus { initial, loading, success, failure }

class AccountDeletionState extends Equatable {
  const AccountDeletionState(
      {this.status = AccountDeletionStatus.initial, this.message = ''});

  final AccountDeletionStatus status;
  final String message;

  AccountDeletionState copyWith({
    AccountDeletionStatus? status,
    String? message,
  }) {
    return AccountDeletionState(
      status: status ?? this.status,
      message: message ?? this.message,
    );
  }

  @override
  List<Object> get props => [status, message];
}
