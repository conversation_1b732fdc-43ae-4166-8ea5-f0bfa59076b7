part of 'profile_bloc.dart';

abstract class ProfileEvent extends Equatable {
  const ProfileEvent();

  @override
  List<Object> get props => [];
}

class ProfileFormChanged extends ProfileEvent {
  final ProfileForm form;

  const ProfileFormChanged({required this.form});

  @override
  List<Object> get props => [form];
}

class ProfileFormSubmit extends ProfileEvent {
  const ProfileFormSubmit();
}
