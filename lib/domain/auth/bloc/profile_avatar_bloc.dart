import 'dart:async';
import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/main.dart';
import 'package:goldenprizma/repositories/auth_repository.dart';

part 'profile_avatar_event.dart';
part 'profile_avatar_state.dart';

class ProfileAvatarBloc extends Bloc<ProfileAvatarEvent, ProfileAvatarState> {
  final AuthRepository authRepository = getIt.get<AuthRepository>();

  ProfileAvatarBloc() : super(const ProfileAvatarState()) {
    on<ProfileAvatarChanged>(_onProfileChanged);
  }

  FutureOr<void> _onProfileChanged(
    ProfileAvatarChanged event,
    Emitter<ProfileAvatarState> emit,
  ) async {
    if (state.status == ProfileAvatarStatus.updating) {
      return;
    }
    emit(state.copyWith(status: ProfileAvatarStatus.updating));
    try {
      String successMessage =
          await authRepository.updateProfilePhoto(event.image.path);

      emit(state.copyWith(
        status: ProfileAvatarStatus.success,
        hasServerError: false,
        successMessage: successMessage,
      ));
    } catch (error) {
      if (error is DioException) {
        emit(state.copyWith(
          status: ProfileAvatarStatus.failure,
          hasServerError: true,
          serverErrorMessage: error.response?.data['error'] ?? '',
        ));
      } else {
        emit(state.copyWith(
            status: ProfileAvatarStatus.failure, hasServerError: false));
      }
    }
    //send api call
  }
}
