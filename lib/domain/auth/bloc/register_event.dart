part of 'register_bloc.dart';

abstract class RegisterEvent extends Equatable {
  const RegisterEvent();

  @override
  List<Object> get props => [];
}

class RegisterFormChanged extends RegisterEvent {
  const RegisterFormChanged({required this.form});

  final RegisterFormModel form;

  @override
  List<Object> get props => [form];
}

class RegisterFormSubmitted extends RegisterEvent {
  const RegisterFormSubmitted();
}

class RegisterFormReset extends RegisterEvent {
  const RegisterFormReset();
}
