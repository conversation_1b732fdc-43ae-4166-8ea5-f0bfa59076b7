import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/repositories/auth_repository.dart';

part 'account_deletion_event.dart';
part 'account_deletion_state.dart';

class AccountDeletionBloc
    extends Bloc<AccountDeletionEvent, AccountDeletionState> {
  final AuthRepository authRepository;
  AccountDeletionBloc({required this.authRepository})
      : super(const AccountDeletionState()) {
    on<AccountDeleteRequestConfirmed>(_onAccountDeleteRequest);
  }

  FutureOr<void> _onAccountDeleteRequest(AccountDeleteRequestConfirmed event,
      Emitter<AccountDeletionState> emit) async {
    try {
      if (state.status == AccountDeletionStatus.loading) {
        return;
      }
      emit(state.copyWith(status: AccountDeletionStatus.loading));

      final String message = await authRepository.deleteMyAccount();
      emit(state.copyWith(
          status: AccountDeletionStatus.success, message: message));
    } on DioException catch (error) {
      emit(state.copyWith(
          status: AccountDeletionStatus.failure,
          message: error.response?.data['error'] ?? ''));
    } catch (error) {
      emit(state.copyWith(
          status: AccountDeletionStatus.failure,
          message: 'An error occured please try again'));
    }
  }
}
