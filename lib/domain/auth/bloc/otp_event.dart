part of 'otp_bloc.dart';

abstract class OtpEvent extends Equatable {
  const OtpEvent();

  @override
  List<Object> get props => [];
}

class OTPRequested extends OtpEvent {
  const OTPRequested(
      {required this.phoneNumber,
      required this.countryCode,
      required this.dialCode,
      this.scope = 'register'});

  final String phoneNumber;
  final String countryCode;
  final String dialCode;
  final String scope;

  @override
  List<Object> get props => [phoneNumber, countryCode, dialCode, scope];
}

class OTPVerificationRequested extends OtpEvent {
  const OTPVerificationRequested({
    required this.phoneNumber,
    required this.countryCode,
    required this.code,
    required this.dialCode,
  });

  final String phoneNumber;
  final String countryCode;
  final String dialCode;
  final String code;

  @override
  List<Object> get props => [phoneNumber, countryCode, code, dialCode];
}

class PasswordOTPVerificationRequested extends OtpEvent {
  const PasswordOTPVerificationRequested({
    required this.phoneNumber,
    required this.countryCode,
    required this.code,
    required this.dialCode,
  });

  final String phoneNumber;
  final String countryCode;
  final String dialCode;
  final String code;

  @override
  List<Object> get props => [phoneNumber, countryCode, code, dialCode];
}
