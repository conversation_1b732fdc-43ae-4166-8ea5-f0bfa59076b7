import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:goldenprizma/domain/auth/models/user.dart';
import 'package:goldenprizma/helpers/logger.dart';
import 'package:goldenprizma/main.dart';
import 'package:goldenprizma/repositories/auth_repository.dart';
import 'package:goldenprizma/repositories/firebase_token_repository.dart';
import 'package:goldenprizma/repositories/token_repository.dart';
import 'package:goldenprizma/services/push_notification_service.dart';

part 'authentication_event.dart';
part 'authentication_state.dart';

class AuthenticationBloc
    extends Bloc<AuthenticationEvent, AuthenticationState> {
  final AuthRepository authRepository = getIt.get<AuthRepository>();
  final TokenRepository tokenRepository = getIt.get<TokenRepository>();
  final PushNotificationService pushNotificationService =
      getIt.get<PushNotificationService>();
  final FirebaseTokenRepository firebaseTokenRepository =
      getIt.get<FirebaseTokenRepository>();

  AuthenticationBloc() : super(const AuthenticationState()) {
    on<AuthenticationUserRequested>(_onAuthUserRequested);
    on<AuthenticationLogoutRequested>(_onAuthLogoutRequested);
  }

  FutureOr<void> _onAuthUserRequested(
    AuthenticationUserRequested event,
    Emitter<AuthenticationState> emit,
  ) async {
    try {
      final String? token = await tokenRepository.get();

      if (token == null || token == "") {
        emit(state.copyWith(status: AuthenticationStatus.unauthenticated));
        return;
      }

      User user = await authRepository.tryGetUser();
      emit(state.copyWith(
          status: AuthenticationStatus.authenticated, user: user));
      // Try to update user device token after login
      firebaseTokenRepository.updateTokenForCurrentDevice(
        token: await pushNotificationService.getToken(),
      );
    } catch (error) {
      logger("AuthenticationBloc  try get auth data error $error");
      // await tokenRepository.delete();
      if (error is DioException && error.response?.statusCode != null) {
        emit(state.copyWith(status: AuthenticationStatus.unauthenticated));
      }
    }
  }

  FutureOr<void> _onAuthLogoutRequested(
    AuthenticationLogoutRequested event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(state.copyWith(status: AuthenticationStatus.unknown));

    try {
      await authRepository.logout();
      await tokenRepository.delete();
      emit(state.copyWith(status: AuthenticationStatus.unauthenticated));
    } catch (error) {
      await tokenRepository.delete();
      emit(state.copyWith(status: AuthenticationStatus.unauthenticated));
    }
  }
}
