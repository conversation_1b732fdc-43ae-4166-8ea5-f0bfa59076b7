part of 'otp_bloc.dart';

enum OtpStatus { initial, failure, success, request, verify, passwordVerify }

class OtpState extends Equatable {
  const OtpState({
    this.status = OtpStatus.initial,
    this.successMessage = '',
    this.errorMessage = '',
    this.phoneNumber = '',
    this.countryCode = '',
    this.dialCode = '',
    this.token = '',
  });

  final OtpStatus status;
  final String successMessage;
  final String errorMessage;
  final String phoneNumber;
  final String countryCode;
  final String dialCode;

  /// this token is used ONLY for forget password
  final String token;

  @override
  List<Object> get props => [
        status,
        successMessage,
        errorMessage,
        phoneNumber,
        countryCode,
        dialCode,
        token
      ];

  OtpState copyWith({
    OtpStatus? status,
    String? successMessage,
    String? errorMessage,
    String? phoneNumber,
    String? countryCode,
    String? dialCode,
    String? token,
  }) {
    return OtpState(
      status: status ?? this.status,
      successMessage: successMessage ?? this.successMessage,
      errorMessage: errorMessage ?? this.errorMessage,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      countryCode: countryCode ?? this.countryCode,
      dialCode: dialCode ?? this.dialCode,
      token: token ?? this.token,
    );
  }

  @override
  String toString() {
    return 'OtpState(status: $status, successMessage: $successMessage, errorMessage: $errorMessage, phoneNumber: $phoneNumber, countryCode: $countryCode, dialCode: $dialCode, token: $token)';
  }
}
