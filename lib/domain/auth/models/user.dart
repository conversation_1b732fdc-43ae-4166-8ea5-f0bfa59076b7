import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:goldenprizma/extensions/color_extension.dart';

class User extends Equatable {
  final int id;
  final String name;
  final String email;
  final String phone;
  final String? phoneCode;
  final int branchId;
  final String cid;
  final String formattedBalance;
  final String customerType;
  final Color customerTypeColor;
  final int? numebrOfProcessedOrders;
  final String? chatInboxIdentifier;
  final String? chatValidationKey;

  final String profilePhotoUrl;

  const User({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.profilePhotoUrl,
    required this.cid,
    required this.formattedBalance,
    required this.customerType,
    required this.customerTypeColor,
    required this.branchId,
    this.numebrOfProcessedOrders = 0,
    this.phoneCode,
    this.chatInboxIdentifier,
    this.chatValidationKey,
    //this.address,
  });

  User.fromJson(Map<String, dynamic> json)
      : id = json['id'],
        name = json['name'],
        email = json['email'],
        phone = json['phone'],
        phoneCode = json['phone_code'],
        branchId = json['branch_id'],
        cid = json['customer_code'],
        profilePhotoUrl = json['profile_photo_url'],
        customerType = json['customer_type'],
        customerTypeColor = HexColor.fromHex(json['customer_type_color']),
        formattedBalance = json['formatted_balance'],
        numebrOfProcessedOrders = json['number_of_processed_orders'] ?? 0,
        chatInboxIdentifier = json['chat_inbox_id'] ?? '',
        chatValidationKey = json['chat_user_validation_key'] ?? '';

  Map<String, dynamic> toJson() => {
        'id': id,
        'cid': cid,
        'name': name,
        'email': email,
        'phone': phone,
        'phoneCode': phoneCode,
        'profilePhotoUrl': profilePhotoUrl,
        'formattedBalance': formattedBalance,
        'customerType': customerType,
        'customerTypeColor': customerTypeColor,
        //'address': address,
        'chatInboxIdentifier': chatInboxIdentifier,
        'chatValidationKey': chatValidationKey,
        'numebrOfProcessedOrders': numebrOfProcessedOrders,
      };

  String get phoneWithCode => phoneCode! + phone;

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        phone,
        phoneCode,
        branchId,
        cid,
        profilePhotoUrl,
        customerType,
        customerTypeColor,
        formattedBalance,
        chatInboxIdentifier,
        chatValidationKey,
        numebrOfProcessedOrders,
      ];

  @override
  String toString() {
    return 'User{id: $id, name: $name, email: $email, phone: $phone, phoneCode: $phoneCode, branchId: $branchId, cid: $cid, formattedBalance: $formattedBalance, customerType: $customerType, customerTypeColor: $customerTypeColor, numebrOfProcessedOrders: $numebrOfProcessedOrders, chatInboxIdentifier: $chatInboxIdentifier, chatValidationKey: $chatValidationKey, profilePhotoUrl: $profilePhotoUrl}';
  }
}
