import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:goldenprizma/domain/store/models/home_data.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/store_repository.dart';

class StoreHomeRepository extends StoreRepository {
  /// Fetch home data including banners, top brands, and promotion status
  Future<HomeData> getHomeData() async {
    try {
      final response = await api.get(ApiUrls.storeHome);

      if (response.statusCode == 200) {
        final data = response.data;
        debugPrint('Store home data response: $data');
        return HomeData.fromJson(data);
      } else {
        throw Exception('Failed to load home data: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 500) {
        throw Exception('Internal Server Error');
      }
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to load home data: $e');
    }
  }
}
