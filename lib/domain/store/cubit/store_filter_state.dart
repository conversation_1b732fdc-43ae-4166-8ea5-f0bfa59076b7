part of 'store_filter_cubit.dart';

class StoreFilterState extends Equatable {
  const StoreFilterState({
    this.selectedBrandId,
    this.selectedBrand,
    this.selectedCategoryId,
    this.isOutletSelected = false,
    this.isPromotionSelected = false,
  });

  final int? selectedBrandId;
  final Brand? selectedBrand;
  final int? selectedCategoryId;
  final bool isOutletSelected;
  final bool isPromotionSelected;

  @override
  List<Object?> get props => [
        selectedBrandId,
        selectedBrand,
        selectedCategoryId,
        isOutletSelected,
        isPromotionSelected,
      ];

  StoreFilterState copyWith({
    int? selectedBrandId,
    Brand? selectedBrand,
    int? selectedCategoryId,
    bool? isOutletSelected,
    bool? isPromotionSelected,
    bool clearBrandId = false,
    bool clearBrand = false,
    bool clearCategoryId = false,
  }) {
    return StoreFilterState(
      selectedBrandId:
          clearBrandId ? null : (selectedBrandId ?? this.selectedBrandId),
      selectedBrand: clearBrand ? null : (selectedBrand ?? this.selectedBrand),
      selectedCategoryId: clearCategoryId
          ? null
          : (selectedCategoryId ?? this.selectedCategoryId),
      isOutletSelected: isOutletSelected ?? this.isOutletSelected,
      isPromotionSelected: isPromotionSelected ?? this.isPromotionSelected,
    );
  }

  @override
  String toString() {
    return 'StoreFilterState('
        'selectedBrandId: $selectedBrandId, '
        'selectedBrand: $selectedBrand, '
        'selectedCategoryId: $selectedCategoryId, '
        'isOutletSelected: $isOutletSelected, '
        'isPromotionSelected: $isPromotionSelected'
        ')';
  }
}
