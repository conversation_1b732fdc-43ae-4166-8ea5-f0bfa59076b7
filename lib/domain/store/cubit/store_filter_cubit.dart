import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/store/models/brand.dart';

part 'store_filter_state.dart';

class StoreFilterCubit extends Cubit<StoreFilterState> {
  StoreFilterCubit() : super(const StoreFilterState());

  /// Select a brand (clears outlet selection as they are mutually exclusive)
  void selectBrand(int? brandId, [Brand? brand]) {
    emit(state.copyWith(
      selectedBrandId: brandId,
      selectedBrand: brand,
      isOutletSelected: false, // Outlet and brand are mutually exclusive
      clearBrandId: brandId == null,
      clearBrand: brand == null,
      // Keep category and promotion selections
    ));
  }

  /// Select a category (independent of other filters)
  void selectCategory(int? categoryId) {
    emit(state.copyWith(
      selectedCategoryId: categoryId,
      clearCategoryId: categoryId == null,
      // Keep all other selections
    ));
  }

  /// Toggle outlet selection (clears brand selection as they are mutually exclusive)
  void toggleOutlet() {
    final willSelectOutlet = !state.isOutletSelected;
    emit(state.copyWith(
      isOutletSelected: willSelectOutlet,
      clearBrandId: willSelectOutlet, // Clear brand if outlet is being selected
      clearBrand: willSelectOutlet, // Clear brand if outlet is being selected
      // Keep category and promotion selections - they are independent
    ));
  }

  /// Toggle promotion selection (independent of other filters)
  void togglePromotion() {
    emit(state.copyWith(
      isPromotionSelected: !state.isPromotionSelected,
      // Keep all other selections
    ));
  }

  /// Clear all filters
  void clearAllFilters() {
    emit(const StoreFilterState());
  }

  /// Clear only brand-related filters (brand and outlet)
  void clearBrandFilters() {
    emit(state.copyWith(
      clearBrandId: true,
      clearBrand: true,
      isOutletSelected: false,
      // Keep category and promotion selections
    ));
  }

  /// Clear only category filter
  void clearCategoryFilter() {
    emit(state.copyWith(
      clearCategoryId: true,
      // Keep all other selections
    ));
  }

  /// Clear only promotion filter
  void clearPromotionFilter() {
    emit(state.copyWith(
      isPromotionSelected: false,
      // Keep all other selections
    ));
  }

  /// Get outlet filter value for API calls
  bool? getOutletFilterValue() {
    // If a specific brand is selected, outlet filter is not applicable
    if (state.selectedBrandId != null) return false;
    // Return the actual outlet selection state
    return state.isOutletSelected;
  }

  /// Get promotion filter value for API calls
  bool? getPromotionFilterValue() {
    // Return the actual promotion selection state
    return state.isPromotionSelected ? true : null;
  }

  /// Check if any filters are active
  bool get hasActiveFilters {
    return state.selectedBrandId != null ||
        state.selectedCategoryId != null ||
        state.isOutletSelected ||
        state.isPromotionSelected;
  }

  /// Get a summary of active filters for display
  String getActiveFiltersText() {
    List<String> activeFilters = [];

    if (state.selectedBrand != null) {
      activeFilters.add(state.selectedBrand!.name);
    }

    if (state.isOutletSelected) {
      activeFilters.add('Outlet');
    }

    if (state.isPromotionSelected) {
      activeFilters.add('On Sale');
    }

    return activeFilters.join(', ');
  }
}
