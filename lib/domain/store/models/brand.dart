import 'dart:convert';

import 'package:flutter/widgets.dart';

class Brand {
  final int id;
  final String name;
  final bool isTop;
  final int sortOrder;
  final String? logoUrl;

  Brand({
    required this.id,
    required this.name,
    required this.isTop,
    required this.sortOrder,
    this.logoUrl,
  });

  String get displayLogoUrl =>
      logoUrl ??
      'https://placehold.co/80x80/000000/FFFFFF?text=${name.substring(0, 1)}';

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'is_top': isTop,
      'sort_order': sortOrder,
      'logo_url': logoUrl,
    };
  }

  factory Brand.fromMap(Map<String, dynamic> map) {
    return Brand(
      id: map['id']?.toInt() ?? 0,
      name: map['name'] ?? '',
      isTop: map['is_top'] ?? false,
      sortOrder: map['sort_order']?.toInt() ?? 0,
      logoUrl: map['logo_url'],
    );
  }

  Brand copyWith({
    int? id,
    String? name,
    bool? isTop,
    int? sortOrder,
    ValueGetter<String?>? logoUrl,
  }) {
    return Brand(
      id: id ?? this.id,
      name: name ?? this.name,
      isTop: isTop ?? this.isTop,
      sortOrder: sortOrder ?? this.sortOrder,
      logoUrl: logoUrl != null ? logoUrl() : this.logoUrl,
    );
  }

  String toJson() => json.encode(toMap());

  factory Brand.fromJson(String source) => Brand.fromMap(json.decode(source));

  @override
  String toString() {
    return 'Brand(id: $id, name: $name, isTop: $isTop, sortOrder: $sortOrder, logoUrl: $logoUrl)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Brand &&
        other.id == id &&
        other.name == name &&
        other.isTop == isTop &&
        other.sortOrder == sortOrder &&
        other.logoUrl == logoUrl;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        isTop.hashCode ^
        sortOrder.hashCode ^
        logoUrl.hashCode;
  }
}
