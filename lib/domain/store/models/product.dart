import 'dart:convert';

import 'package:goldenprizma/domain/store/models/brand.dart';
import 'package:goldenprizma/domain/store/models/category.dart';
import 'package:goldenprizma/domain/store/models/price.dart';

class Product {
  int id;
  String name;
  Price currentPrice;
  Price? originalPrice;
  bool hasPromotion;
  String priceType;
  Category? category;
  Brand? brand;
  String image;
  String thumb;
  String description;
  String link;
  int stocks = 0;
  bool isOutlet;
  String? barcode;

  Product({
    required this.id,
    required this.name,
    required this.currentPrice,
    this.originalPrice,
    required this.hasPromotion,
    required this.priceType,
    required this.image,
    required this.thumb,
    required this.description,
    required this.link,
    required this.stocks,
    this.isOutlet = false,
    this.brand,
    this.category,
    this.barcode,
  });

  // Backward compatibility getter
  Price get price => currentPrice;

  Product copyWith({
    int? id,
    String? name,
    Price? currentPrice,
    Price? originalPrice,
    bool? hasPromotion,
    String? priceType,
    Category? category,
    Brand? brand,
    String? image,
    String? thumb,
    String? description,
    String? link,
    int? stocks,
    bool? isOutlet,
    String? barcode,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      currentPrice: currentPrice ?? this.currentPrice,
      originalPrice: originalPrice ?? this.originalPrice,
      hasPromotion: hasPromotion ?? this.hasPromotion,
      priceType: priceType ?? this.priceType,
      category: category ?? this.category,
      brand: brand ?? this.brand,
      image: image ?? this.image,
      thumb: thumb ?? this.thumb,
      description: description ?? this.description,
      link: link ?? this.link,
      stocks: stocks ?? this.stocks,
      isOutlet: isOutlet ?? this.isOutlet,
      barcode: barcode ?? this.barcode,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'current_price': currentPrice.toMap(),
      'original_price': originalPrice?.toMap(),
      'has_promotion': hasPromotion,
      'price_type': priceType,
      'category': category?.toMap(),
      'brand': brand?.toMap(),
      'image': image,
      'thumb': thumb,
      'description': description,
      'link': link,
      'stocks': stocks,
      'is_outlet': isOutlet,
      'barcode': barcode,
    };
  }

  factory Product.fromMap(Map<String, dynamic> map) {
    return Product(
      id: map['id']?.toInt() ?? 0,
      name: map['name'] ?? '',
      currentPrice: Price.fromMap(map['current_price']),
      originalPrice: map['original_price'] != null
          ? Price.fromMap(map['original_price'])
          : null,
      hasPromotion: map['has_promotion'] ?? false,
      priceType: map['price_type'] ?? '',
      category:
          map['category'] != null ? Category.fromMap(map['category']) : null,
      brand: map['brand'] != null ? Brand.fromMap(map['brand']) : null,
      image: map['image'] ?? '',
      thumb: map['thumb'] ?? '',
      description: map['description'] ?? '',
      link: map['link'] ?? '',
      stocks: map['stocks']?.toInt() ?? 0,
      isOutlet: map['is_outlet'] ?? false,
      barcode: map['barcode'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory Product.fromJson(String source) =>
      Product.fromMap(json.decode(source));

  @override
  String toString() {
    return 'Product(id: $id, name: $name, currentPrice: $currentPrice, originalPrice: $originalPrice, hasPromotion: $hasPromotion, priceType: $priceType, category: $category, brand: $brand, image: $image, thumb: $thumb, description: $description, link: $link, stocks: $stocks, isOutlet: $isOutlet, barcode: $barcode)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Product &&
        other.id == id &&
        other.name == name &&
        other.currentPrice == currentPrice &&
        other.originalPrice == originalPrice &&
        other.hasPromotion == hasPromotion &&
        other.priceType == priceType &&
        other.category == category &&
        other.brand == brand &&
        other.image == image &&
        other.thumb == thumb &&
        other.description == description &&
        other.link == link &&
        other.stocks == stocks &&
        other.isOutlet == isOutlet &&
        other.barcode == barcode;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        currentPrice.hashCode ^
        originalPrice.hashCode ^
        hasPromotion.hashCode ^
        priceType.hashCode ^
        category.hashCode ^
        brand.hashCode ^
        image.hashCode ^
        thumb.hashCode ^
        description.hashCode ^
        link.hashCode ^
        stocks.hashCode ^
        isOutlet.hashCode ^
        barcode.hashCode;
  }
}
