import 'package:equatable/equatable.dart';

class StoreOrder extends Equatable {
  final String name;
  final String image;
  final int quantity;
  final String description;
  final double itemPrice;
  final String priceType;
  final String productId;
  final String link;
  final String barcode;

  const StoreOrder({
    required this.name,
    required this.image,
    required this.quantity,
    required this.description,
    required this.itemPrice,
    required this.priceType,
    required this.productId,
    required this.link,
    required this.barcode,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'image': image,
      'quantity': quantity,
      'description': description,
      'item_price': itemPrice,
      'price_type': priceType,
      'product_id': productId,
      'link': link,
      'barcode': barcode,
    };
  }

  factory StoreOrder.fromMap(Map<String, dynamic> map) {
    return StoreOrder(
      name: map['name'] ?? '',
      image: map['image'] ?? '',
      quantity: map['quantity']?.toInt() ?? 0,
      description: map['description'] ?? '',
      itemPrice: map['item_price']?.toDouble() ?? 0.0,
      priceType: map['price_type'] ?? '',
      productId: map['product_id'] ?? '',
      link: map['link'] ?? '',
      barcode: map['barcode'] ?? '',
    );
  }

  StoreOrder copyWith({
    String? name,
    String? image,
    int? quantity,
    String? description,
    double? itemPrice,
    String? priceType,
    String? productId,
    String? link,
    String? barcode,
  }) {
    return StoreOrder(
      name: name ?? this.name,
      image: image ?? this.image,
      quantity: quantity ?? this.quantity,
      description: description ?? this.description,
      itemPrice: itemPrice ?? this.itemPrice,
      priceType: priceType ?? this.priceType,
      productId: productId ?? this.productId,
      link: link ?? this.link,
      barcode: barcode ?? this.barcode,
    );
  }

  @override
  List<Object> get props {
    return [
      name,
      image,
      quantity,
      description,
      itemPrice,
      priceType,
      productId,
      link,
      barcode,
    ];
  }

  @override
  String toString() {
    return 'StoreOrder(name: $name, image: $image, quantity: $quantity, description: $description, itemPrice: $itemPrice, priceType: $priceType, productId: $productId, link: $link, barcode: $barcode)';
  }
}
