import 'dart:convert';

import 'package:flutter/widgets.dart';

class Banner {
  final int id;
  final String? actionUrl;
  final String imageEnUrl;
  final String imagArUrl;
  final int sortOrder;

  Banner({
    required this.id,
    this.actionUrl,
    required this.imageEnUrl,
    required this.imagArUrl,
    required this.sortOrder,
  });

  Banner copyWith({
    int? id,
    ValueGetter<String?>? actionUrl,
    String? imageEnUrl,
    String? imagArUrl,
    int? sortOrder,
  }) {
    return Banner(
      id: id ?? this.id,
      actionUrl: actionUrl != null ? actionUrl() : this.actionUrl,
      imageEnUrl: imageEnUrl ?? this.imageEnUrl,
      imagArUrl: imagArUrl ?? this.imagArUrl,
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'actionUrl': actionUrl,
      'image_en_url': imageEnUrl,
      'image_ar_url': imagArUrl,
      'sortOrder': sortOrder,
    };
  }

  factory Banner.fromMap(Map<String, dynamic> map) {
    return Banner(
      id: map['id'] ?? '',
      actionUrl: map['action_url'],
      imageEnUrl: map['image_en_url'] ?? '',
      imagArUrl: map['image_ar_url'] ?? '',
      sortOrder: map['sort_order']?.toInt() ?? 0,
    );
  }

  String toJson() => json.encode(toMap());

  factory Banner.fromJson(String source) => Banner.fromMap(json.decode(source));

  @override
  String toString() {
    return 'Banner(id: $id, actionUrl: $actionUrl, imageEnUrl: $imageEnUrl, imagArUrl: $imagArUrl, sortOrder: $sortOrder)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Banner &&
        other.id == id &&
        other.actionUrl == actionUrl &&
        other.imageEnUrl == imageEnUrl &&
        other.imagArUrl == imagArUrl &&
        other.sortOrder == sortOrder;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        actionUrl.hashCode ^
        imageEnUrl.hashCode ^
        imagArUrl.hashCode ^
        sortOrder.hashCode;
  }
}
