import 'dart:convert';

class Price {
  String amount;
  String formattedAmount;
  String currency;

  Price({
    required this.amount,
    required this.formattedAmount,
    required this.currency,
  });

  @override
  String toString() =>
      'Price(amount: $amount, formattedAmount: $formattedAmount, currency: $currency)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Price &&
        other.amount == amount &&
        other.formattedAmount == formattedAmount &&
        other.currency == currency;
  }

  @override
  int get hashCode =>
      amount.hashCode ^ formattedAmount.hashCode ^ currency.hashCode;

  Price copyWith({
    String? amount,
    String? formattedAmount,
    String? currency,
  }) {
    return Price(
      amount: amount ?? this.amount,
      formattedAmount: formattedAmount ?? this.formattedAmount,
      currency: currency ?? this.currency,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'amount': amount,
      'formatted_amount': formattedAmount,
      'currency': currency,
    };
  }

  factory Price.fromMap(Map<String, dynamic> map) {
    return Price(
      amount: map['amount'] ?? '',
      formattedAmount: map['formatted_amount'] ?? '',
      currency: map['currency'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory Price.fromJson(String source) => Price.fromMap(json.decode(source));
}
