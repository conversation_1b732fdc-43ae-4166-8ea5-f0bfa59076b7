class HomeData {
  final bool hasPromotions;
  final List<HomeBanner> banners;
  final List<TopBrand> topBrands;

  const HomeData({
    required this.hasPromotions,
    required this.banners,
    required this.topBrands,
  });

  factory HomeData.fromJson(Map<String, dynamic> json) {
    return HomeData(
      hasPromotions: json['has_promotions'] ?? false,
      banners: (json['banners'] as List<dynamic>?)
              ?.map((banner) => HomeBanner.fromJson(banner))
              .toList() ??
          [],
      topBrands: (json['top_brands'] as List<dynamic>?)
              ?.map((brand) => TopBrand.fromJson(brand))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'has_promotions': hasPromotions,
      'banners': banners.map((banner) => banner.toJson()).toList(),
      'top_brands': topBrands.map((brand) => brand.toJson()).toList(),
    };
  }

  HomeData copyWith({
    bool? hasPromotions,
    List<HomeBanner>? banners,
    List<TopBrand>? topBrands,
  }) {
    return HomeData(
      hasPromotions: hasPromotions ?? this.hasPromotions,
      banners: banners ?? this.banners,
      topBrands: topBrands ?? this.topBrands,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is HomeData &&
        other.hasPromotions == hasPromotions &&
        other.banners == banners &&
        other.topBrands == topBrands;
  }

  @override
  int get hashCode {
    return hasPromotions.hashCode ^ banners.hashCode ^ topBrands.hashCode;
  }
}

class HomeBanner {
  final int id;
  final String name;
  final String actionUrl;
  final int sortOrder;
  final String imageEnUrl;
  final String imageArUrl;

  const HomeBanner({
    required this.id,
    required this.name,
    required this.actionUrl,
    required this.sortOrder,
    required this.imageEnUrl,
    required this.imageArUrl,
  });

  factory HomeBanner.fromJson(Map<String, dynamic> json) {
    return HomeBanner(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      actionUrl: json['action_url'] ?? '',
      sortOrder: json['sort_order'] ?? 0,
      imageEnUrl: json['image_en_url'] ?? '',
      imageArUrl: json['image_ar_url'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'action_url': actionUrl,
      'sort_order': sortOrder,
      'image_en_url': imageEnUrl,
      'image_ar_url': imageArUrl,
    };
  }

  // Get localized image URL based on current locale
  String getLocalizedImageUrl(String locale) {
    return locale == 'ar' ? imageArUrl : imageEnUrl;
  }

  HomeBanner copyWith({
    int? id,
    String? name,
    String? actionUrl,
    int? sortOrder,
    String? imageEnUrl,
    String? imageArUrl,
  }) {
    return HomeBanner(
      id: id ?? this.id,
      name: name ?? this.name,
      actionUrl: actionUrl ?? this.actionUrl,
      sortOrder: sortOrder ?? this.sortOrder,
      imageEnUrl: imageEnUrl ?? this.imageEnUrl,
      imageArUrl: imageArUrl ?? this.imageArUrl,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is HomeBanner &&
        other.id == id &&
        other.name == name &&
        other.actionUrl == actionUrl &&
        other.sortOrder == sortOrder &&
        other.imageEnUrl == imageEnUrl &&
        other.imageArUrl == imageArUrl;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        actionUrl.hashCode ^
        sortOrder.hashCode ^
        imageEnUrl.hashCode ^
        imageArUrl.hashCode;
  }
}

class TopBrand {
  final int id;
  final String name;
  final String logoUrl;

  const TopBrand({
    required this.id,
    required this.name,
    required this.logoUrl,
  });

  factory TopBrand.fromJson(Map<String, dynamic> json) {
    return TopBrand(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      logoUrl: json['logo_url'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'logo_url': logoUrl,
    };
  }

  TopBrand copyWith({
    int? id,
    String? name,
    String? logoUrl,
  }) {
    return TopBrand(
      id: id ?? this.id,
      name: name ?? this.name,
      logoUrl: logoUrl ?? this.logoUrl,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TopBrand &&
        other.id == id &&
        other.name == name &&
        other.logoUrl == logoUrl;
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ logoUrl.hashCode;
  }
}
