part of 'products_bloc.dart';

abstract class ProductsEvent extends Equatable {
  const ProductsEvent();

  @override
  List<Object?> get props => [];
}

class ProductsRequestLoad extends ProductsEvent {
  final String? search;
  final int? brandId;
  final int? categoryId;
  final bool? isOutlet;
  final bool? hasPromotion;

  const ProductsRequestLoad({
    this.search,
    this.brandId,
    this.categoryId,
    this.isOutlet,
    this.hasPromotion,
  });

  @override
  List<Object?> get props =>
      [search, brandId, categoryId, isOutlet, hasPromotion];
}

class ProductsLoadMore extends ProductsEvent {
  final String? search;
  final int? brandId;
  final int? categoryId;
  final bool? isOutlet;
  final bool? hasPromotion;

  const ProductsLoadMore({
    this.search,
    this.brandId,
    this.categoryId,
    this.isOutlet,
    this.hasPromotion,
  });

  @override
  List<Object?> get props =>
      [search, brandId, categoryId, isOutlet, hasPromotion];
}

class ProductsRefresh extends ProductsEvent {}

class ProductsFilterChanged extends ProductsEvent {
  final String? search;
  final int? brandId;
  final int? categoryId;
  final bool? isOutlet;
  final bool? hasPromotion;

  const ProductsFilterChanged({
    this.search,
    this.brandId,
    this.categoryId,
    this.isOutlet,
    this.hasPromotion,
  });

  @override
  List<Object?> get props =>
      [search, brandId, categoryId, isOutlet, hasPromotion];
}
