import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/store/models/product.dart';
import 'package:goldenprizma/domain/store/models/pagination.dart';
import 'package:goldenprizma/repositories/products_repository.dart';
import 'package:goldenprizma/helpers/event_transformers.dart';

part 'products_event.dart';
part 'products_state.dart';

class ProductsBloc extends Bloc<ProductsEvent, ProductsState> {
  final ProductsRepository _productsRepository;

  ProductsBloc(this._productsRepository) : super(const ProductsState()) {
    on<ProductsRequestLoad>(_onProductsRequestLoad);
    on<ProductsLoadMore>(
      _onProductsLoadMore,
      transformer: debounce(const Duration(milliseconds: 300)),
    );
    on<ProductsRefresh>(_onProductsRefresh);
    on<ProductsFilterChanged>(_onProductsFilterChanged);
  }

  FutureOr<void> _onProductsRequestLoad(
      ProductsRequestLoad event, Emitter<ProductsState> emit) async {
    emit(state.copyWith(status: ProductsStatus.loading));

    try {
      PaginatedResponse<Product> response =
          await _productsRepository.getProducts(
        page: 1,
        search: event.search,
        brandId: event.brandId,
        categoryId: event.categoryId,
        isOutlet: event.isOutlet,
        hasPromotion: event.hasPromotion,
      );

      emit(state.copyWith(
        products: response.data,
        status: ProductsStatus.success,
        hasReachedMax: !response.meta.hasNextPage,
        currentPage: response.meta.currentPage,
        search: event.search,
        brandId: event.brandId,
        categoryId: event.categoryId,
        isOutlet: event.isOutlet,
        hasPromotion: event.hasPromotion,
      ));
    } catch (error) {
      emit(state.copyWith(status: ProductsStatus.failure));
    }
  }

  FutureOr<void> _onProductsLoadMore(
      ProductsLoadMore event, Emitter<ProductsState> emit) async {
    // Enhanced checks to prevent duplicate requests
    if (state.hasReachedMax ||
        state.status == ProductsStatus.loading ||
        state.status == ProductsStatus.loadingMore) {
      return;
    }

    emit(state.copyWith(status: ProductsStatus.loadingMore));

    try {
      PaginatedResponse<Product> response =
          await _productsRepository.getProducts(
        page: state.currentPage + 1,
        search: event.search,
        brandId: event.brandId,
        categoryId: event.categoryId,
        isOutlet: event.isOutlet,
        hasPromotion: event.hasPromotion,
      );

      emit(state.copyWith(
        products: List.of(state.products)..addAll(response.data),
        status: ProductsStatus.success,
        hasReachedMax: !response.meta.hasNextPage,
        currentPage: response.meta.currentPage,
        // Update the current filter values in state
        search: event.search,
        brandId: event.brandId,
        categoryId: event.categoryId,
        isOutlet: event.isOutlet,
        hasPromotion: event.hasPromotion,
      ));
    } catch (error) {
      emit(state.copyWith(status: ProductsStatus.failure));
    }
  }

  FutureOr<void> _onProductsRefresh(
      ProductsRefresh event, Emitter<ProductsState> emit) async {
    add(ProductsRequestLoad(
      search: state.search,
      brandId: state.brandId,
      categoryId: state.categoryId,
      isOutlet: state.isOutlet,
      hasPromotion: state.hasPromotion,
    ));
  }

  FutureOr<void> _onProductsFilterChanged(
      ProductsFilterChanged event, Emitter<ProductsState> emit) async {
    add(ProductsRequestLoad(
      search: event.search,
      brandId: event.brandId,
      categoryId: event.categoryId,
      isOutlet: event.isOutlet,
      hasPromotion: event.hasPromotion,
    ));
  }
}
