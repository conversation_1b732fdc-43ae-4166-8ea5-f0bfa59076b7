import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/store/models/category.dart';
import 'package:goldenprizma/repositories/categories_repository.dart';

part 'categories_event.dart';
part 'categories_state.dart';

class CategoriesBloc extends Bloc<CategoriesEvent, CategoriesState> {
  final CategoriesRepository _categoriesRepository;

  CategoriesBloc(this._categoriesRepository) : super(const CategoriesState()) {
    on<CategoriesRequestLoad>(_onCategoriesRequestLoad);
    on<CategoriesRefresh>(_onCategoriesRefresh);
  }

  FutureOr<void> _onCategoriesRequestLoad(
      CategoriesRequestLoad event, Emitter<CategoriesState> emit) async {
    emit(state.copyWith(status: CategoriesStatus.loading));

    try {
      List<Category> categories = await _categoriesRepository.getCategories();
      emit(state.copyWith(
          categories: categories, status: CategoriesStatus.success));
    } catch (error) {
      emit(state.copyWith(status: CategoriesStatus.failure));
    }
  }

  FutureOr<void> _onCategoriesRefresh(
      CategoriesRefresh event, Emitter<CategoriesState> emit) async {
    _categoriesRepository.clearCache();
    add(CategoriesRequestLoad());
  }
}
