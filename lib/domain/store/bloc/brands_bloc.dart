import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/store/models/brand.dart';
import 'package:goldenprizma/repositories/brands_repository.dart';

part 'brands_event.dart';
part 'brands_state.dart';

class BrandsBloc extends Bloc<BrandsEvent, BrandsState> {
  final BrandsRepository _brandsRepository;

  BrandsBloc(this._brandsRepository) : super(const BrandsState()) {
    on<BrandsRequestLoad>(_onBrandsRequestLoad);
    on<BrandsRefresh>(_onBrandsRefresh);
  }

  FutureOr<void> _onBrandsRequestLoad(
      BrandsRequestLoad event, Emitter<BrandsState> emit) async {
    emit(state.copyWith(status: BrandsStatus.loading));

    try {
      List<Brand> brands = await _brandsRepository.getBrands();
      emit(state.copyWith(brands: brands, status: BrandsStatus.success));
    } catch (error) {
      emit(state.copyWith(status: BrandsStatus.failure));
    }
  }

  FutureOr<void> _onBrandsRefresh(
      BrandsRefresh event, Emitter<BrandsState> emit) async {
    _brandsRepository.clearCache();
    add(BrandsRequestLoad());
  }
}
