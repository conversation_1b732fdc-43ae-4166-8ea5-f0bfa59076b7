import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/store/models/product.dart';
import 'package:goldenprizma/repositories/products_repository.dart';

// Events
abstract class ProductDetailEvent extends Equatable {
  const ProductDetailEvent();

  @override
  List<Object> get props => [];
}

class ProductDetailRequested extends ProductDetailEvent {
  final int productId;

  const ProductDetailRequested({required this.productId});

  @override
  List<Object> get props => [productId];
}

// States
abstract class ProductDetailState extends Equatable {
  const ProductDetailState();

  @override
  List<Object> get props => [];
}

class ProductDetailInitial extends ProductDetailState {}

class ProductDetailLoading extends ProductDetailState {}

class ProductDetailLoaded extends ProductDetailState {
  final Product product;

  const ProductDetailLoaded({required this.product});

  @override
  List<Object> get props => [product];
}

class ProductDetailError extends ProductDetailState {
  final String message;

  const ProductDetailError({required this.message});

  @override
  List<Object> get props => [message];
}

// Bloc
class ProductDetailBloc extends Bloc<ProductDetailEvent, ProductDetailState> {
  final ProductsRepository productsRepository;

  ProductDetailBloc({required this.productsRepository}) 
      : super(ProductDetailInitial()) {
    on<ProductDetailRequested>(_onProductDetailRequested);
  }

  Future<void> _onProductDetailRequested(
    ProductDetailRequested event,
    Emitter<ProductDetailState> emit,
  ) async {
    emit(ProductDetailLoading());

    try {
      final product = await productsRepository.getProductDetail(
        productId: event.productId,
      );
      emit(ProductDetailLoaded(product: product));
    } catch (e) {
      emit(ProductDetailError(message: e.toString()));
    }
  }
}
