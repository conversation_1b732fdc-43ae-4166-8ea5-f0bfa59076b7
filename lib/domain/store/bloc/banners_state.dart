part of 'banners_bloc.dart';

enum BannersStatus { initial, loading, success, failure }

class BannersState extends Equatable {
  const BannersState({
    this.status = BannersStatus.initial,
    this.banners = const [],
  });

  final BannersStatus status;
  final List<Banner> banners;

  BannersState copyWith({
    BannersStatus? status,
    List<Banner>? banners,
  }) {
    return BannersState(
      status: status ?? this.status,
      banners: banners ?? this.banners,
    );
  }

  @override
  List<Object> get props => [status, banners];
}
