import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/store/models/banner.dart';
import 'package:goldenprizma/repositories/banners_repository.dart';

part 'banners_event.dart';
part 'banners_state.dart';

class BannersBloc extends Bloc<BannersEvent, BannersState> {
  final BannersRepository _bannersRepository;

  BannersBloc(this._bannersRepository) : super(const BannersState()) {
    on<BannersRequestLoad>(_onBannersRequestLoad);
    on<BannersRefresh>(_onBannersRefresh);
  }

  FutureOr<void> _onBannersRequestLoad(
      BannersRequestLoad event, Emitter<BannersState> emit) async {
    emit(state.copyWith(status: BannersStatus.loading));

    try {
      List<Banner> banners = await _bannersRepository.getBanners();
      emit(state.copyWith(banners: banners, status: BannersStatus.success));
    } catch (error) {
      emit(state.copyWith(status: BannersStatus.failure));
    }
  }

  FutureOr<void> _onBannersRefresh(
      BannersRefresh event, Emitter<BannersState> emit) async {
    _bannersRepository.clearCache();
    add(BannersRequestLoad());
  }
}
