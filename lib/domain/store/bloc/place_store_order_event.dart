part of 'place_store_order_bloc.dart';

abstract class PlaceStoreOrderEvent extends Equatable {
  const PlaceStoreOrderEvent();

  @override
  List<Object> get props => [];
}

class PlaceStoreOrderSubmitted extends PlaceStoreOrderEvent {
  final StoreOrder storeOrder;

  const PlaceStoreOrderSubmitted({required this.storeOrder});

  @override
  List<Object> get props => [storeOrder];
}

class PlaceStoreOrderReset extends PlaceStoreOrderEvent {
  const PlaceStoreOrderReset();
}
