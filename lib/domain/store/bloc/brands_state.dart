part of 'brands_bloc.dart';

enum BrandsStatus { initial, loading, success, failure }

class BrandsState extends Equatable {
  const BrandsState({
    this.status = BrandsStatus.initial,
    this.brands = const [],
  });

  final BrandsStatus status;
  final List<Brand> brands;

  BrandsState copyWith({
    BrandsStatus? status,
    List<Brand>? brands,
  }) {
    return BrandsState(
      status: status ?? this.status,
      brands: brands ?? this.brands,
    );
  }

  @override
  List<Object> get props => [status, brands];
}
