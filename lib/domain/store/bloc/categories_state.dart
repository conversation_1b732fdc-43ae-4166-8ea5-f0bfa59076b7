part of 'categories_bloc.dart';

enum CategoriesStatus { initial, loading, success, failure }

class CategoriesState extends Equatable {
  const CategoriesState({
    this.status = CategoriesStatus.initial,
    this.categories = const [],
  });

  final CategoriesStatus status;
  final List<Category> categories;

  CategoriesState copyWith({
    CategoriesStatus? status,
    List<Category>? categories,
  }) {
    return CategoriesState(
      status: status ?? this.status,
      categories: categories ?? this.categories,
    );
  }

  @override
  List<Object> get props => [status, categories];
}
