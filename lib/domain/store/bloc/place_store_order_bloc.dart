import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/store/models/store_order.dart';
import 'package:goldenprizma/repositories/store_order_repository.dart';
import 'package:goldenprizma/helpers/logger.dart';

part 'place_store_order_event.dart';
part 'place_store_order_state.dart';

class PlaceStoreOrderBloc
    extends Bloc<PlaceStoreOrderEvent, PlaceStoreOrderState> {
  final StoreOrderRepository storeOrderRepository;

  PlaceStoreOrderBloc({required this.storeOrderRepository})
      : super(const PlaceStoreOrderState()) {
    on<PlaceStoreOrderSubmitted>(_onPlaceStoreOrderSubmitted);
    on<PlaceStoreOrderReset>(_onPlaceStoreOrderReset);
  }

  FutureOr<void> _onPlaceStoreOrderSubmitted(
    PlaceStoreOrderSubmitted event,
    Emitter<PlaceStoreOrderState> emit,
  ) async {
    if (state.status == PlaceStoreOrderStatus.loading) {
      return;
    }

    emit(state.copyWith(status: PlaceStoreOrderStatus.loading));

    try {
      logger("Placing store order: ${event.storeOrder}");

      final result =
          await storeOrderRepository.placeStoreOrder(event.storeOrder);

      if (result['success'] == true) {
        emit(state.copyWith(
          status: PlaceStoreOrderStatus.success,
          message: result['message'],
          orderData: result['data'],
        ));
        logger("Store order placed successfully: ${result['message']}");
      } else {
        emit(state.copyWith(
          status: PlaceStoreOrderStatus.failure,
          message: result['message'],
        ));
        logger("Store order failed: ${result['message']}");
      }
    } catch (error) {
      logger("Store order error: $error");
      emit(state.copyWith(
        status: PlaceStoreOrderStatus.failure,
        message: 'An unexpected error occurred while placing the order',
      ));
    }
  }

  FutureOr<void> _onPlaceStoreOrderReset(
    PlaceStoreOrderReset event,
    Emitter<PlaceStoreOrderState> emit,
  ) {
    emit(const PlaceStoreOrderState());
  }
}
