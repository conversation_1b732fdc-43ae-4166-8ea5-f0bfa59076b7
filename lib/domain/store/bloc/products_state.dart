part of 'products_bloc.dart';

enum ProductsStatus { initial, loading, loadingMore, success, failure }

class ProductsState extends Equatable {
  const ProductsState({
    this.status = ProductsStatus.initial,
    this.products = const [],
    this.hasReachedMax = false,
    this.currentPage = 1,
    this.search,
    this.brandId,
    this.categoryId,
    this.isOutlet = false,
    this.hasPromotion = false,
  });

  final ProductsStatus status;
  final List<Product> products;
  final bool hasReachedMax;
  final int currentPage;
  final String? search;
  final int? brandId;
  final int? categoryId;
  final bool? isOutlet;
  final bool? hasPromotion;

  ProductsState copyWith({
    ProductsStatus? status,
    List<Product>? products,
    bool? hasReachedMax,
    int? currentPage,
    String? search,
    int? brandId,
    int? categoryId,
    bool? isOutlet,
    bool? hasPromotion,
  }) {
    return ProductsState(
      status: status ?? this.status,
      products: products ?? this.products,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPage: currentPage ?? this.currentPage,
      search: search ?? this.search,
      brandId: brandId ?? this.brandId,
      categoryId: categoryId ?? this.categoryId,
      isOutlet: isOutlet ?? this.isOutlet,
      hasPromotion: hasPromotion ?? this.hasPromotion,
    );
  }

  @override
  List<Object?> get props => [
        status,
        products,
        hasReachedMax,
        currentPage,
        search,
        brandId,
        categoryId,
        isOutlet,
        hasPromotion,
      ];
}
