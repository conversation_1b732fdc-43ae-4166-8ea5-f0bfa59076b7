import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/store/models/home_data.dart';
import 'package:goldenprizma/domain/store/repositories/store_home_repository.dart';

// Events
abstract class StoreHomeEvent extends Equatable {
  const StoreHomeEvent();

  @override
  List<Object> get props => [];
}

class StoreHomeRequestLoad extends StoreHomeEvent {
  const StoreHomeRequestLoad();
}

class StoreHomeRefresh extends StoreHomeEvent {
  const StoreHomeRefresh();
}

// States
enum StoreHomeStatus { initial, loading, success, failure }

class StoreHomeState extends Equatable {
  final StoreHomeStatus status;
  final HomeData? homeData;
  final String? errorMessage;

  const StoreHomeState({
    this.status = StoreHomeStatus.initial,
    this.homeData,
    this.errorMessage,
  });

  StoreHomeState copyWith({
    StoreHomeStatus? status,
    HomeData? homeData,
    String? errorMessage,
  }) {
    return StoreHomeState(
      status: status ?? this.status,
      homeData: homeData ?? this.homeData,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [status, homeData, errorMessage];
}

// Bloc
class StoreHomeBloc extends Bloc<StoreHomeEvent, StoreHomeState> {
  final StoreHomeRepository _homeRepository;

  StoreHomeBloc(this._homeRepository) : super(const StoreHomeState()) {
    on<StoreHomeRequestLoad>(_onStoreHomeRequestLoad);
    on<StoreHomeRefresh>(_onStoreHomeRefresh);
  }

  Future<void> _onStoreHomeRequestLoad(
    StoreHomeRequestLoad event,
    Emitter<StoreHomeState> emit,
  ) async {
    emit(state.copyWith(status: StoreHomeStatus.loading));

    try {
      final homeData = await _homeRepository.getHomeData();
      emit(state.copyWith(
        status: StoreHomeStatus.success,
        homeData: homeData,
        errorMessage: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: StoreHomeStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onStoreHomeRefresh(
    StoreHomeRefresh event,
    Emitter<StoreHomeState> emit,
  ) async {
    // Don't show loading state for refresh
    try {
      final homeData = await _homeRepository.getHomeData();
      emit(state.copyWith(
        status: StoreHomeStatus.success,
        homeData: homeData,
        errorMessage: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: StoreHomeStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }
}
