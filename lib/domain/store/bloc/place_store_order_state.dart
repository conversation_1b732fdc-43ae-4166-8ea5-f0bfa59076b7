part of 'place_store_order_bloc.dart';

enum PlaceStoreOrderStatus {
  initial,
  loading,
  success,
  failure,
}

class PlaceStoreOrderState extends Equatable {
  final PlaceStoreOrderStatus status;
  final String? message;
  final Map<String, dynamic>? orderData;

  const PlaceStoreOrderState({
    this.status = PlaceStoreOrderStatus.initial,
    this.message,
    this.orderData,
  });

  PlaceStoreOrderState copyWith({
    PlaceStoreOrderStatus? status,
    String? message,
    Map<String, dynamic>? orderData,
  }) {
    return PlaceStoreOrderState(
      status: status ?? this.status,
      message: message ?? this.message,
      orderData: orderData ?? this.orderData,
    );
  }

  @override
  List<Object?> get props => [status, message, orderData];
}
