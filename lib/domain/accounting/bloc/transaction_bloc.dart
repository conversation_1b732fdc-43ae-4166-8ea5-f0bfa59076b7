import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/accounting/bloc/transaction_filter_bloc.dart';
import 'package:goldenprizma/domain/accounting/models/transaction.dart';
import 'package:goldenprizma/repositories/accounting_repository.dart';

part 'transaction_event.dart';
part 'transaction_state.dart';

class TransactionBloc extends Bloc<TransactionEvent, TransactionState> {
  final AccountingRepostory accountingRepostory;
  final TransactionFilterBloc transactionFilterBloc;

  TransactionBloc(
      {required this.accountingRepostory, required this.transactionFilterBloc})
      : super(const TransactionState()) {
    on<TransactionRequestLoad>(_onTransactionRequestLoad);
    on<TransactionRequestRefresh>(_onTransactionRequestRefresh);

    transactionFilterBloc.stream.listen((event) {
      add(TransactionRequestRefresh(
        queryParameters: transactionFilterBloc.state.toMap(),
      ));
    });
  }

  FutureOr<void> _onTransactionRequestLoad(
      TransactionEvent event, Emitter<TransactionState> emit) async {
    try {
      if (state.hasReachedMax || state.status == TransactionStatus.loading) {
        return;
      }

      if (state.page > 1) {
        emit(state.copyWith(status: TransactionStatus.loading));
      }

      List<Transaction> transactions =
          await accountingRepostory.getTransactions(page: state.page);

      emit(
        state.copyWith(
          status: TransactionStatus.success,
          transactions: List.of(state.transactions)..addAll(transactions),
          page: state.hasReachedMax ? state.page : state.page + 1,
          hasReachedMax: transactions.isEmpty,
        ),
      );
    } catch (error) {
      emit(state.copyWith(
        status: TransactionStatus.failure,
      ));
    }
  }

  FutureOr<void> _onTransactionRequestRefresh(
      TransactionRequestRefresh event, Emitter<TransactionState> emit) async {
    try {
      emit(
        state.copyWith(
          status: TransactionStatus.initial,
          transactions: const [],
        ),
      );
      List<Transaction> transactions = await accountingRepostory
          .getTransactions(queryParameters: event.queryParameters);

      emit(
        state.copyWith(
          status: TransactionStatus.success,
          transactions: transactions,
        ),
      );
    } catch (error) {
      emit(state.copyWith(
        status: TransactionStatus.failure,
      ));
    }
  }
}
