import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/accounting/models/pending_deposit.dart';
import 'package:goldenprizma/repositories/accounting_repository.dart';

part 'pending_deposit_event.dart';
part 'pending_deposit_state.dart';

class PendingDepositBloc
    extends Bloc<PendingDepositEvent, PendingDepositState> {
  final AccountingRepostory accountingRepository;

  PendingDepositBloc({required this.accountingRepository})
      : super(const PendingDepositState()) {
    on<PendingDepositRequestLoad>(_onPendingDepositRequestLoad);
    on<PendingDepositRefresh>(_onPendingDepositRefresh);
  }

  FutureOr<void> _onPendingDepositRequestLoad(PendingDepositRequestLoad event,
      Emitter<PendingDepositState> emit) async {
    try {
      if (state.status == PendingDepositStatus.loading) {
        return;
      }

      emit(state.copyWith(status: PendingDepositStatus.loading));

      List<PendingDeposit> pendingDeposits =
          await accountingRepository.getPendingDeposits();

      emit(state.copyWith(
        status: PendingDepositStatus.success,
        pendingDeposits: pendingDeposits,
      ));
    } catch (error) {
      emit(state.copyWith(status: PendingDepositStatus.failure));
    }
  }

  FutureOr<void> _onPendingDepositRefresh(
      PendingDepositRefresh event, Emitter<PendingDepositState> emit) async {
    try {
      emit(state.copyWith(status: PendingDepositStatus.initial));

      List<PendingDeposit> pendingDeposits =
          await accountingRepository.getPendingDeposits();

      emit(state.copyWith(
        status: PendingDepositStatus.success,
        pendingDeposits: pendingDeposits,
      ));
    } catch (error) {
      emit(state.copyWith(status: PendingDepositStatus.failure));
    }
  }
}
