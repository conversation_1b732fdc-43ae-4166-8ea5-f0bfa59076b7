import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/accounting/models/account.dart';
import 'package:goldenprizma/repositories/accounting_repository.dart';

part 'account_event.dart';
part 'account_state.dart';

class AccountBloc extends Bloc<AccountEvent, AccountState> {
  AccountBloc({required this.accountingRepostory})
      : super(const AccountState()) {
    on<AccountRequestLoad>(_onAccountRequestLoad);
    on<AccountRefresh>(_onAccountRefresh);
  }
  final AccountingRepostory accountingRepostory;

  FutureOr<void> _onAccountRequestLoad(
      AccountRequestLoad event, Emitter<AccountState> emit) async {
    try {
      Account account = await accountingRepostory.getAccount();
      emit(state.copyWith(
        status: AccountStatus.success,
        account: account,
      ));
    } catch (error) {
      emit(state.copyWith(status: AccountStatus.failure));
    }
  }

  FutureOr<void> _onAccountRefresh(
      AccountRefresh event, Emitter<AccountState> emit) async {
    try {
      emit(state.copyWith(status: AccountStatus.initial));

      Account account = await accountingRepostory.getAccount();
      emit(state.copyWith(
        status: AccountStatus.success,
        account: account,
      ));
    } catch (error) {
      emit(state.copyWith(status: AccountStatus.failure));
    }
  }
}
