part of 'pending_deposit_bloc.dart';

enum PendingDepositStatus { initial, loading, success, failure }

class PendingDepositState extends Equatable {
  const PendingDepositState({
    this.pendingDeposits = const [],
    this.status = PendingDepositStatus.initial,
  });

  final List<PendingDeposit> pendingDeposits;
  final PendingDepositStatus status;

  @override
  List<Object> get props => [pendingDeposits, status];

  @override
  String toString() {
    return 'PendingDepositState(pendingDeposits: $pendingDeposits, status: $status)';
  }

  PendingDepositState copyWith({
    List<PendingDeposit>? pendingDeposits,
    PendingDepositStatus? status,
  }) {
    return PendingDepositState(
      pendingDeposits: pendingDeposits ?? this.pendingDeposits,
      status: status ?? this.status,
    );
  }
}
