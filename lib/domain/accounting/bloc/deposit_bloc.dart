import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/main.dart';
import 'package:goldenprizma/repositories/accounting_repository.dart';

part 'deposit_event.dart';
part 'deposit_state.dart';

class DepositBloc extends Bloc<DepositEvent, DepositState> {
  final AccountingRepostory accountingRepository =
      getIt.get<AccountingRepostory>();

  DepositBloc() : super(const DepositState()) {
    on<DepositSubmitted>(_onDepositSubmitted);
    on<DepositReset>(_onDepositReset);
  }

  FutureOr<void> _onDepositSubmitted(
    DepositSubmitted event,
    Emitter<DepositState> emit,
  ) async {
    if (state.status == DepositStatus.loading) {
      return;
    }

    try {
      emit(state.copyWith(status: DepositStatus.loading));

      final Map<String, dynamic> result =
          await accountingRepository.createDeposit(
        amount: event.amount,
        gateway: event.gateway,
      );

      emit(
        state.copyWith(
          status: DepositStatus.success,
          successMessage:
              result['message'] ?? 'Deposit request submitted successfully',
          hasApiError: false,
          errorMessage: '',
          depositData: result,
        ),
      );
    } on DioException catch (error) {
      String errorMessage = 'An error occurred';

      // Handle API validation errors
      if (error.response?.data != null) {
        final responseData = error.response!.data;
        if (responseData['error'] != null) {
          if (responseData['error'] is List) {
            // Join multiple error messages
            final errors = responseData['error'] as List;
            errorMessage = errors.join(', ');
          } else if (responseData['error'] is String) {
            errorMessage = responseData['error'];
          }
        } else if (responseData['message'] != null) {
          errorMessage = responseData['message'];
        }
      }

      emit(
        state.copyWith(
          status: DepositStatus.failure,
          hasApiError: true,
          errorMessage: errorMessage,
        ),
      );
    } catch (error) {
      emit(
        state.copyWith(
          status: DepositStatus.failure,
          errorMessage: 'An unexpected error occurred',
        ),
      );
    }
  }

  FutureOr<void> _onDepositReset(
    DepositReset event,
    Emitter<DepositState> emit,
  ) async {
    emit(const DepositState());
  }
}
