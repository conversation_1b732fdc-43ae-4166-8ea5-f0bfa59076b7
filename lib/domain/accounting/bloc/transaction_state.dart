part of 'transaction_bloc.dart';

enum TransactionStatus { initial, loading, success, failure }

class TransactionState extends Equatable {
  const TransactionState({
    this.transactions = const [],
    this.status = TransactionStatus.initial,
    this.hasReachedMax = false,
    this.page = 1,
  });

  final List<Transaction> transactions;
  final TransactionStatus status;
  final int page;
  final bool hasReachedMax;

  @override
  List<Object> get props => [transactions, status, page, hasReachedMax];

  @override
  String toString() {
    return 'TransactionState(transactions: $transactions, status: $status, page: $page, hasReachedMax: $hasReachedMax)';
  }

  TransactionState copyWith({
    List<Transaction>? transactions,
    TransactionStatus? status,
    int? page,
    bool? hasReachedMax,
  }) {
    return TransactionState(
      transactions: transactions ?? this.transactions,
      status: status ?? this.status,
      page: page ?? this.page,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
    );
  }
}
