part of 'transaction_filter_bloc.dart';

class TransactionFilterState extends Equatable {
  const TransactionFilterState({
    this.transactionType = 'All',
    this.createdAt,
  });

  final String transactionType;
  final DateTimeRange? createdAt;

  @override
  List<Object?> get props => [transactionType, createdAt];

  TransactionFilterState copyWith({
    String? transactionType,
    DateTimeRange? createdAt,
  }) {
    return TransactionFilterState(
      transactionType: transactionType ?? this.transactionType,
      createdAt: createdAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'transactionType': transactionType,
      'createdAt': (createdAt?.start != null && createdAt?.end != null)
          ? DateTimeRange(
              start: createdAt?.start ?? DateTime(2021),
              end: createdAt?.end ?? DateTime.now(),
            )
          : [],
      'createdAtRange': (createdAt?.start != null && createdAt?.end != null)
          ? "${createdAt?.start}, ${createdAt?.end}"
          : null,
    };
  }

  @override
  String toString() =>
      'TransactionFilterState(transactionType: $transactionType, createdAt: $createdAt)';
}
