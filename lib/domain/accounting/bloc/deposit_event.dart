part of 'deposit_bloc.dart';

abstract class DepositEvent extends Equatable {
  const DepositEvent();

  @override
  List<Object> get props => [];
}

class DepositSubmitted extends DepositEvent {
  const DepositSubmitted({
    required this.amount,
    required this.gateway,
  });

  final double amount;
  final String gateway;

  @override
  List<Object> get props => [amount, gateway];
}

class DepositReset extends DepositEvent {
  const DepositReset();
}
