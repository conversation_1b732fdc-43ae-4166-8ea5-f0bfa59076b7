part of 'deposit_bloc.dart';

enum DepositStatus { initial, loading, success, failure }

class DepositState extends Equatable {
  const DepositState({
    this.status = DepositStatus.initial,
    this.hasApiError = false,
    this.errorMessage = '',
    this.successMessage = '',
    this.depositData,
  });

  final DepositStatus status;
  final bool hasApiError;
  final String errorMessage;
  final String successMessage;
  final Map<String, dynamic>? depositData;

  @override
  List<Object?> get props {
    return [
      status,
      hasApiError,
      errorMessage,
      successMessage,
      depositData,
    ];
  }

  DepositState copyWith({
    DepositStatus? status,
    bool? hasApiError,
    String? errorMessage,
    String? successMessage,
    Map<String, dynamic>? depositData,
  }) {
    return DepositState(
      status: status ?? this.status,
      hasApiError: hasApiError ?? this.hasApiError,
      errorMessage: errorMessage ?? this.errorMessage,
      successMessage: successMessage ?? this.successMessage,
      depositData: depositData ?? this.depositData,
    );
  }
}
