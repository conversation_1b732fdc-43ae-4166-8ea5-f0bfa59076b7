part of 'transaction_bloc.dart';

abstract class TransactionEvent extends Equatable {
  const TransactionEvent();

  @override
  List<Object?> get props => [];
}

class TransactionRequestLoad extends TransactionEvent {}

class TransactionRequestRefresh extends TransactionEvent {
  final Map<String, dynamic>? queryParameters;

  const TransactionRequestRefresh({this.queryParameters});

  @override
  List<Object?> get props => [queryParameters];
}
