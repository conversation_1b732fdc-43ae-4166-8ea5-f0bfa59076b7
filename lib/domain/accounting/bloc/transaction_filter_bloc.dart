import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

part 'transaction_filter_event.dart';
part 'transaction_filter_state.dart';

class TransactionFilterBloc
    extends Bloc<TransactionFilterEvent, TransactionFilterState> {
  TransactionFilterBloc() : super(const TransactionFilterState()) {
    on<TransactionFilterTypeChanged>((event, emit) {
      emit(state.copyWith(transactionType: event.transactionType));
    });

    on<TransactionFilterDateChanged>((event, emit) {
      emit(state.copyWith(createdAt: event.createdAt));
    });
  }
}
