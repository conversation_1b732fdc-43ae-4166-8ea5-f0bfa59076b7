part of 'account_bloc.dart';

enum AccountStatus { initial, success, failure }

class AccountState extends Equatable {
  const AccountState({
    this.account = const Account.empty(),
    this.status = AccountStatus.initial,
  });

  final Account account;
  final AccountStatus status;

  @override
  List<Object> get props => [account, status];

  @override
  String toString() => 'AccountState(account: $account, status: $status)';

  AccountState copyWith({
    Account? account,
    AccountStatus? status,
  }) {
    return AccountState(
      account: account ?? this.account,
      status: status ?? this.status,
    );
  }
}
