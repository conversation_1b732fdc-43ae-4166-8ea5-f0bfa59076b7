part of 'transaction_filter_bloc.dart';

abstract class TransactionFilterEvent extends Equatable {
  const TransactionFilterEvent();

  @override
  List<Object?> get props => [];
}

class TransactionFilterTypeChanged extends TransactionFilterEvent {
  const TransactionFilterTypeChanged({required this.transactionType});
  final String transactionType;
  @override
  List<Object> get props => [transactionType];
}

class TransactionFilterDateChanged extends TransactionFilterEvent {
  const TransactionFilterDateChanged({required this.createdAt});
  final DateTimeRange? createdAt;
  @override
  List<Object?> get props => [createdAt];
}
