import 'package:equatable/equatable.dart';

class Account extends Equatable {
  final String balance;
  final String accountType;
  final String totalPurchase;
  final String totalDelivered;

  const Account({
    required this.balance,
    required this.accountType,
    required this.totalPurchase,
    required this.totalDelivered,
  });

  Account copyWith({
    String? balance,
    String? accountType,
    String? totalPurchase,
  }) {
    return Account(
      balance: balance ?? this.balance,
      accountType: accountType ?? this.accountType,
      totalPurchase: totalPurchase ?? this.totalPurchase,
      totalDelivered: totalDelivered,
    );
  }

  Account.fromJson(Map<String, dynamic> jsonData)
      : balance = jsonData['balance'],
        accountType = jsonData['type_name'],
        totalPurchase = jsonData['total_purchase'],
        totalDelivered = jsonData['total_delivered'];

  const Account.empty()
      : balance = '\$0',
        accountType = 'Normal',
        totalPurchase = '\$0',
        totalDelivered = '\$0';

  @override
  String toString() =>
      'Account(balance: $balance, accountType: $accountType, totalPurchase: $totalPurchase, totalDelivered: $totalDelivered)';

  @override
  List<Object> get props =>
      [balance, accountType, totalPurchase, totalDelivered];
}
