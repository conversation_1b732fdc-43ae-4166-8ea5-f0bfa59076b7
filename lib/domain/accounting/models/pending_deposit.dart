import 'dart:convert';

import 'package:equatable/equatable.dart';

class PendingDeposit extends Equatable {
  const PendingDeposit({
    required this.id,
    required this.userId,
    required this.gatewayName,
    required this.description,
    required this.amount,
    required this.currency,
    required this.exchangeRate,
    required this.amountUsd,
    required this.status,
    required this.createdAt,
    this.processedAt,
  });

  final int id;
  final int userId;
  final String gatewayName;
  final String description;
  final String amount;
  final String currency;
  final String exchangeRate;
  final String amountUsd;
  final String status;
  final String createdAt;
  final String? processedAt;

  @override
  List<Object> get props {
    return [
      id,
      userId,
      gatewayName,
      description,
      amount,
      currency,
      exchangeRate,
      amountUsd,
      status,
      createdAt,
      processedAt ?? '',
    ];
  }

  PendingDeposit copyWith({
    int? id,
    int? userId,
    String? gatewayName,
    String? description,
    String? amount,
    String? currency,
    String? exchangeRate,
    String? amountUsd,
    String? status,
    String? createdAt,
    String? processedAt,
  }) {
    return PendingDeposit(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      gatewayName: gatewayName ?? this.gatewayName,
      description: description ?? this.description,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      exchangeRate: exchangeRate ?? this.exchangeRate,
      amountUsd: amountUsd ?? this.amountUsd,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      processedAt: processedAt ?? this.processedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'gateway_name': gatewayName,
      'description': description,
      'amount': amount,
      'currency': currency,
      'exchange_rate': exchangeRate,
      'amount_usd': amountUsd,
      'status': status,
      'created_at': createdAt,
      'processed_at': processedAt,
    };
  }

  factory PendingDeposit.fromMap(Map<String, dynamic> map) {
    return PendingDeposit(
      id: map['id']?.toInt() ?? 0,
      userId: map['user_id']?.toInt() ?? 0,
      gatewayName: map['gateway_name'] ?? '',
      description: map['description'] ?? '',
      amount: map['amount'] ?? '',
      currency: map['currency'] ?? '',
      exchangeRate: map['exchange_rate'] ?? '',
      amountUsd: map['amount_usd'] ?? '',
      status: map['status'] ?? '',
      createdAt: map['created_at'] ?? '',
      processedAt: map['processed_at'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  PendingDeposit.fromJson(Map<String, dynamic> jsonData)
      : id = jsonData['id']?.toInt() ?? 0,
        userId = jsonData['user_id']?.toInt() ?? 0,
        gatewayName = jsonData['gateway_name'] ?? '',
        description = jsonData['description'] ?? '',
        amount = jsonData['amount'] ?? '',
        currency = jsonData['currency'] ?? '',
        exchangeRate = jsonData['exchange_rate'] ?? '',
        amountUsd = jsonData['amount_usd'] ?? '',
        status = jsonData['status'] ?? '',
        createdAt = jsonData['created_at'] ?? '',
        processedAt = jsonData['processed_at'];

  @override
  String toString() {
    return 'PendingDeposit(id: $id, userId: $userId, gatewayName: $gatewayName, description: $description, amount: $amount, currency: $currency, exchangeRate: $exchangeRate, amountUsd: $amountUsd, status: $status, createdAt: $createdAt, processedAt: $processedAt)';
  }

  String get dateOnly {
    if (createdAt.isEmpty) {
      return '';
    }
    List<String> dateTime = createdAt.split(" ");
    return dateTime[0];
  }

  String get timeOnly {
    if (createdAt.isEmpty) {
      return '';
    }
    List<String> dateTime = createdAt.split(" ");
    return dateTime.length > 1 ? dateTime[1] : '';
  }

  String get formattedAmount {
    return '\$$amountUsd';
  }

  String get capitalizedStatus {
    return status.isNotEmpty
        ? status[0].toUpperCase() + status.substring(1).toLowerCase()
        : '';
  }
}
