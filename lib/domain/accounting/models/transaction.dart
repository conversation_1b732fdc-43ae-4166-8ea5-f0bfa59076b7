import 'dart:convert';

import 'package:equatable/equatable.dart';

class Transaction extends Equatable {
  const Transaction({
    required this.id,
    required this.type,
    required this.amount,
    required this.createdAt,
    required this.day,
    required this.month,
    required this.year,
    this.note = '',
    this.paymentType = '',
  });

  final int id;
  final String type;
  final String amount;
  final String createdAt;
  final String day;
  final String month;
  final String year;
  final String note;
  final String paymentType;

  @override
  List<Object> get props {
    return [
      id,
      type,
      amount,
      createdAt,
      day,
      month,
      year,
      note,
      paymentType,
    ];
  }

  Transaction copyWith({
    int? id,
    String? type,
    String? amount,
    String? createdAt,
    String? day,
    String? month,
    String? year,
    String? note,
    String? paymentType,
  }) {
    return Transaction(
      id: id ?? this.id,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      createdAt: createdAt ?? this.createdAt,
      day: day ?? this.day,
      month: month ?? this.month,
      year: year ?? this.year,
      note: note ?? this.note,
      paymentType: paymentType ?? this.paymentType,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type,
      'amount': amount,
      'createdAt': createdAt,
      'day': day,
      'month': month,
      'year': year,
      'note': note,
      'paymentType': paymentType,
    };
  }

  factory Transaction.fromMap(Map<String, dynamic> map) {
    return Transaction(
      id: map['id']?.toInt() ?? 0,
      type: map['type'],
      amount: map['amount'] ?? '',
      createdAt: map['createdAt'] ?? '',
      day: map['day'] ?? '',
      month: map['month'] ?? '',
      year: map['year'] ?? '',
      note: map['note'] ?? '',
      paymentType: map['paymentType'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  Transaction.fromJson(Map<String, dynamic> jsonData)
      : id = jsonData['id']?.toInt() ?? 0,
        type = jsonData['type'],
        amount = jsonData['amount'] ?? '',
        createdAt = jsonData['created_at'] ?? '',
        day = jsonData['day'] ?? '',
        month = jsonData['month'] ?? '',
        year = jsonData['year'] ?? '',
        note = jsonData['note'] ?? '',
        paymentType = jsonData['paymentType'] ?? '';

  @override
  String toString() {
    return 'Transaction(id: $id, type: $type, amount: $amount, createdAt: $createdAt, day: $day, month: $month, year: $year, note: $note, paymentType: $paymentType)';
  }

  String get dateOnly {
    if (createdAt.isEmpty) {
      return '';
    }
    List<String> dateTime = createdAt.split(" ");

    return dateTime[0];
  }

  String get timeOnly {
    if (createdAt.isEmpty) {
      return '';
    }
    List<String> dateTime = createdAt.split(" ");

    return dateTime[1];
  }
}
