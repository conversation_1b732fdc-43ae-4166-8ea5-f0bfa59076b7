import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:goldenprizma/domain/exchange_rates/models/exchange_rate.dart';
import 'package:goldenprizma/domain/home/<USER>/slide.dart';

class HomeData extends Equatable {
  final List<ExchangeRate> exchangeRates;
  final List<Slide> slides;

  const HomeData({
    required this.exchangeRates,
    required this.slides,
  });

  @override
  List<Object> get props => [exchangeRates, slides];

  factory HomeData.fromJson(Map<String, dynamic> json) {
    try {
      final data = json['data'] ?? {};

      // Parse exchange rates with error handling
      List<ExchangeRate> exchangeRates = [];
      try {
        exchangeRates = (data['exchange_rates'] as List<dynamic>?)
                ?.map((item) => ExchangeRate.fromJson(item))
                .toList() ??
            [];
      } catch (e) {
        debugPrint('Error parsing exchange rates: $e');
        exchangeRates = [];
      }

      // Parse slides with error handling
      List<Slide> slides = [];
      try {
        slides = (data['slides'] as List<dynamic>?)
                ?.map((item) => Slide.fromJson(item))
                .toList() ??
            [];
      } catch (e) {
        debugPrint('Error parsing slides: $e');
        slides = [];
      }

      return HomeData(
        exchangeRates: exchangeRates,
        slides: slides,
      );
    } catch (e) {
      throw Exception('Failed to parse HomeData from JSON: $e. JSON: $json');
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'data': {
        'exchange_rates': exchangeRates.map((rate) => rate.toMap()).toList(),
        'slides': slides.map((slide) => slide.toJson()).toList(),
      }
    };
  }

  HomeData copyWith({
    List<ExchangeRate>? exchangeRates,
    List<Slide>? slides,
  }) {
    return HomeData(
      exchangeRates: exchangeRates ?? this.exchangeRates,
      slides: slides ?? this.slides,
    );
  }

  @override
  String toString() {
    return 'HomeData(exchangeRates: $exchangeRates, slides: $slides)';
  }
}
