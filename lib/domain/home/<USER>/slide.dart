import 'package:equatable/equatable.dart';

class Slide extends Equatable {
  final int id;
  final String? url;
  final String? actionUrl;
  final String image;
  final String imageUrl;
  final String? isPage;
  final bool isActive;
  final bool isSlider;
  final int sortNumber;
  final String createdAt;
  final bool inApp;
  final String appAction;
  final String? appActionData;

  const Slide({
    required this.id,
    this.url,
    this.actionUrl,
    required this.image,
    required this.imageUrl,
    this.isPage,
    required this.isActive,
    required this.isSlider,
    required this.sortNumber,
    required this.createdAt,
    this.inApp = true,
    this.appAction = 'external_url',
    this.appActionData,
  });

  @override
  List<Object?> get props => [
        id,
        url,
        actionUrl,
        image,
        imageUrl,
        isPage,
        isActive,
        isSlider,
        sortNumber,
        createdAt,
        inApp,
        appAction,
        appActionData,
      ];

  factory Slide.fromJson(Map<String, dynamic> json) {
    try {
      return Slide(
        id: json['id'] ?? 0,
        url: json['url'],
        actionUrl: json['action_url'],
        image: json['image'] ?? '',
        imageUrl: json['image_url'] ?? '',
        isPage: json['is_page'],
        isActive: json['is_active'] ?? false,
        isSlider: json['is_slider'] ?? false,
        sortNumber: json['sort_number'] ?? 0,
        createdAt: json['created_at'] ?? '',
        inApp: json['in_app'] ?? true,
        appAction: json['app_action'] ?? 'external_url',
        appActionData: json['app_action_data'],
      );
    } catch (e) {
      throw Exception('Failed to parse Slide from JSON: $e. JSON: $json');
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'url': url,
      'action_url': actionUrl,
      'image': image,
      'image_url': imageUrl,
      'is_page': isPage,
      'is_active': isActive,
      'is_slider': isSlider,
      'sort_number': sortNumber,
      'created_at': createdAt,
      'in_app': inApp,
      'app_action': appAction,
      'app_action_data': appActionData,
    };
  }

  Slide copyWith({
    int? id,
    String? url,
    String? actionUrl,
    String? image,
    String? imageUrl,
    String? isPage,
    bool? isActive,
    bool? isSlider,
    int? sortNumber,
    String? createdAt,
    bool? inApp,
    String? appAction,
    String? appActionData,
  }) {
    return Slide(
      id: id ?? this.id,
      url: url ?? this.url,
      actionUrl: actionUrl ?? this.actionUrl,
      image: image ?? this.image,
      imageUrl: imageUrl ?? this.imageUrl,
      isPage: isPage ?? this.isPage,
      isActive: isActive ?? this.isActive,
      isSlider: isSlider ?? this.isSlider,
      sortNumber: sortNumber ?? this.sortNumber,
      createdAt: createdAt ?? this.createdAt,
      inApp: inApp ?? this.inApp,
      appAction: appAction ?? this.appAction,
      appActionData: appActionData ?? this.appActionData,
    );
  }

  // Helper methods for different app actions
  bool get isExternalUrl => appAction == 'external_url';
  bool get isBrandStore => appAction == 'brand_store';
  bool get isPageAction => appAction == 'page';

  // Get brand ID for brand store action
  int? get brandId => isBrandStore && appActionData != null
      ? int.tryParse(appActionData!)
      : null;

  // Get page slug for page action
  String? get pageSlug => isPageAction ? appActionData : null;

  // Get external URL
  String? get externalUrl => isExternalUrl ? appActionData : null;

  @override
  String toString() {
    return 'Slide(id: $id, url: $url, actionUrl: $actionUrl, image: $image, imageUrl: $imageUrl, isPage: $isPage, isActive: $isActive, isSlider: $isSlider, sortNumber: $sortNumber, createdAt: $createdAt, inApp: $inApp, appAction: $appAction, appActionData: $appActionData)';
  }
}
