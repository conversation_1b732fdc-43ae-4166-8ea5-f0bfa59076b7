import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/home/<USER>/home_data.dart';
import 'package:goldenprizma/repositories/home_repository.dart';

part 'home_event.dart';
part 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  final HomeRepository _homeRepository;

  HomeBloc({required HomeRepository homeRepository})
      : _homeRepository = homeRepository,
        super(const HomeState()) {
    on<HomeDataRequested>(_onHomeDataRequested);
    on<HomeDataRefreshed>(_onHomeDataRefreshed);
  }

  Future<void> _onHomeDataRequested(
    HomeDataRequested event,
    Emitter<HomeState> emit,
  ) async {
    emit(state.copyWith(status: HomeStatus.loading));
    try {
      final homeData = await _homeRepository.getHomeData();
      emit(state.copyWith(
        status: HomeStatus.success,
        homeData: homeData,
        errorMessage: '',
      ));
    } catch (error) {
      emit(state.copyWith(
        status: HomeStatus.failure,
        errorMessage: error.toString(),
      ));
    }
  }

  Future<void> _onHomeDataRefreshed(
    HomeDataRefreshed event,
    Emitter<HomeState> emit,
  ) async {
    try {
      final homeData = await _homeRepository.getHomeData();
      emit(state.copyWith(
        status: HomeStatus.success,
        homeData: homeData,
        errorMessage: '',
      ));
    } catch (error) {
      emit(state.copyWith(
        status: HomeStatus.failure,
        errorMessage: error.toString(),
      ));
    }
  }
}
