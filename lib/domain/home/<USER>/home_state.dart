part of 'home_bloc.dart';

enum HomeStatus { initial, loading, success, failure }

class HomeState extends Equatable {
  const HomeState({
    this.status = HomeStatus.initial,
    this.homeData = const HomeData(exchangeRates: [], slides: []),
    this.errorMessage = '',
  });

  final HomeStatus status;
  final HomeData homeData;
  final String errorMessage;

  @override
  List<Object> get props => [status, homeData, errorMessage];

  HomeState copyWith({
    HomeStatus? status,
    HomeData? homeData,
    String? errorMessage,
  }) {
    return HomeState(
      status: status ?? this.status,
      homeData: homeData ?? this.homeData,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  String toString() =>
      'HomeState(status: $status, homeData: $homeData, errorMessage: $errorMessage)';
}
