import 'package:equatable/equatable.dart';

class SizeModel extends Equatable {
  const SizeModel(
    this.id,
    this.name,
    this.isFrequentlyUsed,
    this.isPinned,
  );

  final int id;
  final String name;
  final bool isFrequentlyUsed;
  final bool isPinned;

  @override
  List<Object> get props => [id, name, isFrequentlyUsed, isPinned];

  SizeModel copyWith({
    int? id,
    String? name,
    bool? isFrequentlyUsed,
    bool? isPinned,
  }) {
    return SizeModel(
      id ?? this.id,
      name ?? this.name,
      isFrequentlyUsed ?? this.isFrequentlyUsed,
      isPinned ?? this.isPinned,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'isFrequentlyUsed': isFrequentlyUsed,
      'isPinned': isPinned,
    };
  }

  factory SizeModel.fromMap(Map<String, dynamic> map) {
    return SizeModel(
      map['id']?.toInt() ?? 0,
      map['name'] ?? '',
      map['isFrequentlyUsed'] ?? false,
      map['isPinned'] ?? false,
    );
  }

  SizeModel.fromJson(Map<String, dynamic> jsonData)
      : id = jsonData['id'],
        name = jsonData['name'],
        isFrequentlyUsed = jsonData['isFrequentlyUsed'] ?? false,
        isPinned = jsonData['isPinned'] ?? false;

  @override
  String toString() => name;

  bool filterByName(String? query) {
    if (query == null || isPinned || isFrequentlyUsed) {
      return true;
    }

    return name
        .startsWith(RegExp(query, caseSensitive: false, multiLine: false));
  }

  static List<SizeModel> fromJsonList(List<dynamic> dataList) {
    return List.of(dataList)
        .map((jsonData) => SizeModel.fromJson(jsonData))
        .toList();
  }
}
