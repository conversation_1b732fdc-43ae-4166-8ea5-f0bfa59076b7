import 'package:equatable/equatable.dart';

class ScrapResponse extends Equatable {
  const ScrapResponse({
    this.itemPrice,
    this.itemCode,
    this.fromCurrency,
    this.color,
    this.websiteId,
    this.brandId,
    this.parentCategoryId,
    this.image,
    this.countryId,
  });

  final String? itemPrice;
  final String? itemCode;
  final String? fromCurrency;
  final String? color;
  final int? websiteId;
  final int? brandId;
  final int? parentCategoryId;
  final String? image;
  final int? countryId;

  ScrapResponse copyWith({
    String? itemPrice,
    String? itemCode,
    String? fromCurrency,
    String? color,
    int? websiteId,
    int? brandId,
    int? parentCategoryId,
    String? image,
    int? countryId,
  }) {
    return ScrapResponse(
      itemPrice: itemPrice ?? this.itemPrice,
      itemCode: itemCode ?? this.itemCode,
      fromCurrency: fromCurrency ?? this.fromCurrency,
      color: color ?? this.color,
      websiteId: websiteId ?? this.websiteId,
      brandId: brandId ?? this.brandId,
      parentCategoryId: parentCategoryId ?? this.parentCategoryId,
      image: image ?? this.image,
      countryId: countryId ?? this.countryId,
    );
  }

  ScrapResponse.fromJson(Map<String, dynamic> jsonData)
      : itemPrice = jsonData['item_price'].toString(),
        itemCode = jsonData['item_code'].toString(),
        fromCurrency = jsonData['from_currency'],
        color = jsonData['color'],
        websiteId = jsonData['website_id'],
        brandId = jsonData['brand_id'],
        parentCategoryId = jsonData['parentCategory'],
        countryId = jsonData['country_id'],
        image = jsonData['image'];

  @override
  String toString() {
    return 'ScrapResponse(itemPrice: $itemPrice, itemCode: $itemCode, fromCurrency: $fromCurrency, color: $color, websiteId: $websiteId, brandId: $brandId, parentCategoryId: $parentCategoryId, image: $image, countryId: $countryId)';
  }

  @override
  List<Object?> get props {
    return [
      itemPrice,
      itemCode,
      fromCurrency,
      color,
      websiteId,
      brandId,
      parentCategoryId,
      image,
      countryId,
    ];
  }
}
