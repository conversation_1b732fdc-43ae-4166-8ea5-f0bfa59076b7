import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/orders/models/rejection_reason.dart';
import 'package:validators/sanitizers.dart';

class Order extends Equatable {
  final int id;
  final String status;
  final String image;
  final String link;
  final String? description;
  final String? size;
  final String? brand;
  final int quantity;
  final String itemPrice;
  final dynamic itemUsdPrice;
  final String internalShipping;
  final String shippingPrice;
  final double shippingPriceRaw;
  final String? commission;
  final String? tax;
  final String totalPrice;
  final String createdAt;
  final bool showAcceptReject;
  final bool showPrice;
  final bool canReorder; // Default to false, can be set later
  final bool isStoreOrder; // Default to false, can be set later
  final int? storeProductId;
  final List<RejectionReason> rejectionReasons;

  const Order({
    required this.id,
    required this.status,
    required this.image,
    required this.link,
    required this.quantity,
    required this.itemPrice,
    required this.itemUsdPrice,
    required this.shippingPrice,
    required this.shippingPriceRaw,
    required this.totalPrice,
    required this.internalShipping,
    required this.createdAt,
    this.showPrice = true,
    this.rejectionReasons = const [],
    this.size = '',
    this.brand = '',
    this.commission,
    this.tax = '',
    this.description = '',
    this.showAcceptReject = false,
    this.canReorder = false,
    this.isStoreOrder = false,
    this.storeProductId,
  });

  Order.fromJson(Map<String, dynamic> jsonData)
      : id = jsonData['id'] as int,
        status = jsonData['status_name'],
        image = jsonData['image'],
        link = jsonData['link'],
        rejectionReasons = ((jsonData['rejection_reasons'] ?? []) as List)
            .map<RejectionReason>((e) => RejectionReason.fromMap(e))
            .toList(),
        description = jsonData['description'],
        size = jsonData['size'],
        brand = jsonData['brand'],
        quantity = jsonData['quantity'] as int,
        internalShipping = jsonData['internal_shipping'],
        itemPrice = jsonData['item_price'],
        itemUsdPrice = jsonData['item_price_usd'],
        shippingPrice = jsonData['shipping_price'],
        shippingPriceRaw = toDouble(jsonData['shipping_price_raw']),
        totalPrice = jsonData['total_price'],
        commission = jsonData['commission'],
        tax = jsonData['tax'],
        showAcceptReject = jsonData['processed'] ?? false,
        showPrice = jsonData['show_price'] ?? true,
        canReorder = jsonData['can_reorder'] ?? false,
        isStoreOrder = jsonData['is_store_order'] ?? false,
        storeProductId = jsonData['store_product_id'],
        createdAt = jsonData['created_at'];

  Order copyWith({
    int? id,
    String? status,
    String? image,
    String? link,
    String? description,
    String? size,
    String? brand,
    int? quantity,
    String? itemPrice,
    dynamic itemUsdPrice,
    String? internalShipping,
    String? shippingPrice,
    double? shippingPriceRaw,
    String? commission,
    String? tax,
    String? totalPrice,
    String? createdAt,
    bool? showAcceptReject,
    bool? showPrice,
    bool? canReorder,
    bool? isStoreOrder,
    int? storeProductId,
    List<RejectionReason>? rejectionReasons,
  }) {
    return Order(
      id: id ?? this.id,
      status: status ?? this.status,
      image: image ?? this.image,
      link: link ?? this.link,
      description: description ?? this.description,
      size: size ?? this.size,
      brand: brand ?? this.brand,
      quantity: quantity ?? this.quantity,
      itemPrice: itemPrice ?? this.itemPrice,
      itemUsdPrice: itemUsdPrice ?? this.itemUsdPrice,
      internalShipping: internalShipping ?? this.internalShipping,
      shippingPrice: shippingPrice ?? this.shippingPrice,
      shippingPriceRaw: shippingPriceRaw ?? this.shippingPriceRaw,
      commission: commission ?? this.commission,
      tax: tax ?? this.tax,
      totalPrice: totalPrice ?? this.totalPrice,
      rejectionReasons: rejectionReasons ?? this.rejectionReasons,
      createdAt: createdAt ?? this.createdAt,
      showPrice: showPrice ?? this.showPrice,
      showAcceptReject: showAcceptReject ?? this.showAcceptReject,
      canReorder: canReorder ?? this.canReorder,
      isStoreOrder: isStoreOrder ?? this.isStoreOrder,
      storeProductId: storeProductId ?? this.storeProductId,
    );
  }

  @override
  String toString() {
    return 'Order(id: $id, status: $status, image: $image, link: $link, description: $description, size: $size, brand: $brand, quantity: $quantity, itemPrice: $itemPrice, itemUsdPrice: $itemUsdPrice, internalShipping: $internalShipping, shippingPrice: $shippingPrice, shippingPriceRaw: $shippingPriceRaw, commission: $commission, tax: $tax, totalPrice: $totalPrice, createdAt: $createdAt, showAcceptReject: $showAcceptReject, showPrice $showPrice, rejectionReasons:$rejectionReasons, canReorder: $canReorder, isStoreOrder: $isStoreOrder, storeProductId: $storeProductId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Order && other.id == id;
  }

  bool canViewReason() {
    return rejectionReasons.isNotEmpty;
  }

  bool canConfirmAgain() {
    return rejectionReasons.isNotEmpty &&
        status.toString().toLowerCase() == 'processed';
  }

  @override
  int get hashCode {
    return id.hashCode ^
        status.hashCode ^
        image.hashCode ^
        link.hashCode ^
        description.hashCode ^
        size.hashCode ^
        brand.hashCode ^
        quantity.hashCode ^
        itemPrice.hashCode ^
        itemUsdPrice.hashCode ^
        internalShipping.hashCode ^
        shippingPrice.hashCode ^
        shippingPriceRaw.hashCode ^
        commission.hashCode ^
        rejectionReasons.hashCode ^
        tax.hashCode ^
        totalPrice.hashCode ^
        createdAt.hashCode ^
        showPrice.hashCode ^
        canReorder.hashCode ^
        isStoreOrder.hashCode ^
        storeProductId.hashCode ^
        showAcceptReject.hashCode;
  }

  @override
  List<Object?> get props {
    return [
      id,
      status,
      image,
      link,
      description,
      size,
      brand,
      quantity,
      itemPrice,
      itemUsdPrice,
      internalShipping,
      shippingPrice,
      rejectionReasons,
      shippingPriceRaw,
      commission,
      tax,
      totalPrice,
      createdAt,
      showPrice,
      canReorder,
      isStoreOrder,
      storeProductId,
      showAcceptReject,
    ];
  }
}
