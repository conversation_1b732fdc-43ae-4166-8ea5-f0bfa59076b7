import 'package:equatable/equatable.dart';

class CountryModel extends Equatable {
  const CountryModel({
    this.id = 0,
    this.name = '',
    this.currency = '',
  });

  final int id;
  final String name;
  final String currency;

  @override
  List<Object> get props => [id, name, currency];

  CountryModel copyWith({
    int? id,
    String? name,
    String? currency,
  }) {
    return CountryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      currency: currency ?? this.currency,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'currency': currency,
    };
  }

  factory CountryModel.fromMap(Map<String, dynamic> map) {
    return CountryModel(
      id: map['id']?.toInt() ?? 0,
      name: map['name'] ?? '',
      currency: map['currency'] ?? '',
    );
  }

  CountryModel.fromJson(Map<String, dynamic> jsonData)
      : id = jsonData['id'],
        name = jsonData['name'],
        currency = jsonData['main_currency'] ?? '';

  @override
  String toString() => name;

  bool filterByName(String? query) {
    if (query == null) {
      return true;
    }
    return name.contains(
        RegExp(query.toString(), caseSensitive: false, multiLine: true));
  }

  static List<CountryModel> fromJsonList(List<dynamic> dataList) {
    return List.of(dataList)
        .map((jsonData) => CountryModel.fromJson(jsonData))
        .toList();
  }
}
