import 'package:equatable/equatable.dart';

class FilterWebsiteModel extends Equatable {
  const FilterWebsiteModel(
    this.id,
    this.name,
  );

  final int id;
  final String name;

  @override
  List<Object> get props => [id, name];

  FilterWebsiteModel copyWith({
    int? id,
    String? name,
  }) {
    return FilterWebsiteModel(
      id ?? this.id,
      name ?? this.name,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
    };
  }

  factory FilterWebsiteModel.fromMap(Map<String, dynamic> map) {
    return FilterWebsiteModel(
      map['id']?.toInt() ?? 0,
      map['name'] ?? '',
    );
  }

  FilterWebsiteModel.fromJson(Map<String, dynamic> jsonData)
      : id = jsonData['id'],
        name = jsonData['name'];

  @override
  String toString() => name;

  bool filterByName(String? query) {
    if (query == null) {
      return true;
    }

    return name
        .startsWith(RegExp(query, caseSensitive: false, multiLine: false));
  }

  static List<FilterWebsiteModel> fromJsonList(List<dynamic> dataList) {
    return List.of(dataList)
        .map((jsonData) => FilterWebsiteModel.fromJson(jsonData))
        .toList();
  }
}
