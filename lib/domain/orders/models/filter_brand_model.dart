import 'package:equatable/equatable.dart';

class FilterBrandModel extends Equatable {
  const FilterBrandModel(
    this.id,
    this.name,
  );

  final int id;
  final String name;

  @override
  List<Object> get props => [id, name];

  FilterBrandModel copyWith({
    int? id,
    String? name,
  }) {
    return FilterBrandModel(
      id ?? this.id,
      name ?? this.name,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
    };
  }

  factory FilterBrandModel.fromMap(Map<String, dynamic> map) {
    return FilterBrandModel(
      map['id']?.toInt() ?? 0,
      map['name'] ?? '',
    );
  }

  FilterBrandModel.fromJson(Map<String, dynamic> jsonData)
      : id = jsonData['id'],
        name = jsonData['name'];

  @override
  String toString() => name;

  bool filterByName(String? query) {
    if (query == null) {
      return true;
    }

    return name
        .startsWith(RegExp(query, caseSensitive: false, multiLine: false));
  }

  static List<FilterBrandModel> fromJsonList(List<dynamic> dataList) {
    return List.of(dataList)
        .map((jsonData) => FilterBrandModel.fromJson(jsonData))
        .toList();
  }
}
