import 'package:equatable/equatable.dart';

class RejectionReason extends Equatable {
  final String date;
  final String reason;
  final String? status;
  final bool canReply;

  const RejectionReason({
    required this.date,
    required this.reason,
    this.canReply = false,
    this.status,
  });

  RejectionReason copyWith({
    String? date,
    String? reason,
    bool? canReply,
    String? status,
  }) {
    return RejectionReason(
      date: date ?? this.date,
      reason: reason ?? this.reason,
      canReply: canReply ?? this.canReply,
      status: status ?? this.status,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'date': date,
      'reason': reason,
      'canReply': canReply,
      'status': status,
    };
  }

  factory RejectionReason.fromMap(Map<String, dynamic> map) {
    return RejectionReason(
      date: map['date'] ?? '',
      reason: map['reason'] ?? '',
      status: map['status'] ?? '',
      canReply: map['customer_can_reply'] ?? false,
    );
  }

  @override
  String toString() =>
      'RejectionReason(date: $date, reason: $reason, canReply: $canReply, status $status)';

  @override
  List<Object> get props => [date, reason, canReply];
}
