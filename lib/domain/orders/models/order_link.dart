import 'package:formz/formz.dart';

enum OrderLinkError { empty, invalid }

class OrderLink extends FormzInput<String, OrderLinkError> {
  const OrderLink.pure() : super.pure('');

  const OrderLink.dirty([super.value = '']) : super.dirty();

  @override
  OrderLinkError? validator(String? value) {
    if (value!.isEmpty || value.trim().isEmpty) {
      return OrderLinkError.empty;
    }
    if (value.contains('@')) {
      return OrderLinkError.invalid;
    }
    return null;
  }
}
