import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';

class OrderQuery extends Equatable {
  final String search;
  final List<String> statuses;
  final List<int> websiteIds;
  final List<int> sizeIds;
  final List<int> brandIds;
  final int page;

  const OrderQuery({
    this.search = '',
    this.statuses = const [],
    this.websiteIds = const [],
    this.sizeIds = const [],
    this.brandIds = const [],
    this.page = 1,
  });

  OrderQuery copyWith({
    String? search,
    List<String>? statuses,
    List<int>? websiteIds,
    List<int>? sizeIds,
    List<int>? brandIds,
    int? page,
  }) {
    return OrderQuery(
      search: search ?? this.search,
      statuses: statuses ?? this.statuses,
      websiteIds: websiteIds ?? this.websiteIds,
      sizeIds: sizeIds ?? this.sizeIds,
      brandIds: brandIds ?? this.brandIds,
      page: page ?? this.page,
    );
  }

  bool containsStatus(String status) {
    return statuses.isNotEmpty && statuses.contains(status);
  }

  OrderQuery toggleStatus(String status) {
    if (containsStatus(status)) {
      return copyWith(
        statuses:
            List.of(statuses).where((element) => status != element).toList(),
      );
    }
    return copyWith(
      statuses: List.of(statuses)..add(status),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'search': search,
      'statuses': statuses,
      'websiteIds': websiteIds,
      'sizeIds': sizeIds,
      'brandIds': brandIds,
      'page': page,
    };
  }

  factory OrderQuery.fromMap(Map<String, dynamic> map) {
    return OrderQuery(
      search: map['search'] ?? '',
      statuses: List<String>.from(map['statuses']),
      websiteIds: List<int>.from(map['websiteIds']),
      sizeIds: List<int>.from(map['sizeIds']),
      brandIds: List<int>.from(map['brandIds']),
      page: map['page']?.toInt() ?? 0,
    );
  }

  String toJson() => json.encode(toMap());

  factory OrderQuery.fromJson(String source) =>
      OrderQuery.fromMap(json.decode(source));

  @override
  String toString() {
    return 'OrderQuery(search: $search, statuses: $statuses, websiteIds: $websiteIds, sizeIds: $sizeIds, brandIds: $brandIds, page: $page, )';
  }

  @override
  List<Object> get props {
    return [
      search,
      statuses,
      websiteIds,
      sizeIds,
      brandIds,
      page,
    ];
  }

  static List<Map<String, String?>> statusList(BuildContext context) => [
        {'label': context.loc.allOrders, 'value': null},
        {'label': context.loc.processed, 'value': 'Processed'},
        {'label': context.loc.waiting, 'value': 'Waiting'},
        {'label': context.loc.delivered, 'value': 'Delivered'},
        {'label': context.loc.inTransit, 'value': 'In Transit'},
        {'label': context.loc.cancelled, 'value': 'Cancelled'},
        {'label': context.loc.completed, 'value': 'Completed'},
      ];
}
