part of 'order_bloc.dart';

abstract class OrderEvent extends Equatable {
  const OrderEvent();

  @override
  List<Object?> get props => [];
}

class OrderRequestLoad extends OrderEvent {
  const OrderRequestLoad({this.status});

  final String? status;

  @override
  List<Object?> get props => [status];
}

class OrderQueryChanged extends OrderEvent {
  const OrderQueryChanged({required this.query});

  final OrderQuery query;

  @override
  List<Object> get props => [query];
}

class OrderSearchQueryChanged extends OrderEvent {
  final String search;

  const OrderSearchQueryChanged({required this.search});

  @override
  List<Object> get props => [search];
}

class OrderReload extends OrderEvent {
  const OrderReload({required this.query});

  final OrderQuery query;

  @override
  List<Object> get props => [query];
}
