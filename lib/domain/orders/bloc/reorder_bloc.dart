import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/main.dart';
import 'package:goldenprizma/repositories/order_repository.dart';

part 'reorder_event.dart';
part 'reorder_state.dart';

class ReorderBloc extends Bloc<ReorderEvent, ReorderState> {
  final OrderRepository orderRepository = getIt.get<OrderRepository>();

  ReorderBloc() : super(const ReorderState()) {
    on<ReorderSubmitted>(_onReorderSubmitted);
    on<ReorderReset>(_onReorderReset);
  }

  FutureOr<void> _onReorderSubmitted(
    ReorderSubmitted event,
    Emitter<ReorderState> emit,
  ) async {
    if (state.status == ReorderStatus.loading) {
      return;
    }
    try {
      emit(state.copyWith(status: ReorderStatus.loading));

      // Call the reorder method from the repository
      Map<String, dynamic> result = await orderRepository.reorder(
        orderId: event.orderId,
      );

      emit(
        state.copyWith(
          status: ReorderStatus.success,
          successMessage: result['message'],
          hasApiError: false,
          errorMessage: '',
        ),
      );
    } on DioException catch (error) {
      emit(
        state.copyWith(
          status: ReorderStatus.failure,
          hasApiError: true,
          errorMessage: error.response?.data['error'] ?? 'An error occurred',
        ),
      );
    } catch (error) {
      emit(
        state.copyWith(
          status: ReorderStatus.failure,
          errorMessage: 'An error occurred',
        ),
      );
    }
  }

  FutureOr<void> _onReorderReset(
    ReorderReset event,
    Emitter<ReorderState> emit,
  ) async {
    emit(const ReorderState());
  }
}
