part of 'order_form_bloc.dart';

enum ScrapStatus { initial, loading, failure, success }

class OrderFormState extends Equatable {
  const OrderFormState({
    this.form = const OrderFormModel(),
    this.status = FormzStatus.pure,
    this.scrapStatus = ScrapStatus.initial,
    this.scrapResponse = const ScrapResponse(),
    this.hasServerError = false,
    this.apirErrorMessage = '',
    this.apiSuccessMessage = '',
  });

  final OrderFormModel form;
  final FormzStatus status;
  final ScrapStatus scrapStatus;

  // We will use it when sending post request to create order
  // we don't use it in any part of ui
  final ScrapResponse scrapResponse;

  final bool hasServerError;
  final String apirErrorMessage;
  final String apiSuccessMessage;

  @override
  List<Object> get props {
    return [
      form,
      status,
      scrapStatus,
      scrapResponse,
      hasServerError,
      apirErrorMessage,
      apiSuccessMessage,
    ];
  }

  OrderFormState copyWith({
    OrderFormModel? form,
    FormzStatus? status,
    ScrapStatus? scrapStatus,
    ScrapResponse? scrapResponse,
    bool? hasServerError,
    String? apirErrorMessage,
    String? apiSuccessMessage,
  }) {
    return OrderFormState(
      form: form ?? this.form,
      status: status ?? this.status,
      scrapStatus: scrapStatus ?? this.scrapStatus,
      scrapResponse: scrapResponse ?? this.scrapResponse,
      hasServerError: hasServerError ?? this.hasServerError,
      apirErrorMessage: apirErrorMessage ?? this.apirErrorMessage,
      apiSuccessMessage: apiSuccessMessage ?? this.apiSuccessMessage,
    );
  }

  @override
  String toString() {
    return 'OrderFormState(form: $form, status: $status, scrapStatus: $scrapStatus, scrapResponse: $scrapResponse, hasServerError: $hasServerError, apirErrorMessage: $apirErrorMessage, apiSuccessMessage: $apiSuccessMessage)';
  }
}
