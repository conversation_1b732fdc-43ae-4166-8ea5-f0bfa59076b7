part of 'order_form_bloc.dart';

abstract class OrderForm<PERSON>vent extends Equatable {
  const OrderFormEvent();

  @override
  List<Object> get props => [];
}

class OrderFormChanged extends OrderFormEvent {
  const OrderFormChanged({required this.form});

  final OrderFormModel form;

  @override
  List<Object> get props => [form];
}

class ScrapLink extends OrderFormEvent {
  const ScrapLink({required this.link});

  final String link;

  @override
  List<Object> get props => [link];
}

class ClientScraped extends OrderFormEvent {
  const ClientScraped({this.websiteId, this.itemCode, this.itemPrice});

  final int? websiteId;
  final String? itemCode;
  final double? itemPrice;

  @override
  List<Object> get props => [websiteId ?? 0, itemCode ?? 0, itemPrice ?? 0];
}

class OrderFormSubmitted extends OrderFormEvent {
  const OrderFormSubmitted();
}

class OrderFormReset extends OrderFormEvent {
  const OrderFormReset();
}
