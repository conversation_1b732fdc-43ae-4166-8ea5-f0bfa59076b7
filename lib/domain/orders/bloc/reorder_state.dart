part of 'reorder_bloc.dart';

enum ReorderStatus { initial, loading, success, failure }

class ReorderState extends Equatable {
  const ReorderState({
    this.status = ReorderStatus.initial,
    this.successMessage = '',
    this.errorMessage = '',
    this.hasApiError = false,
  });

  final ReorderStatus status;
  final String successMessage;
  final String errorMessage;
  final bool hasApiError;

  ReorderState copyWith({
    ReorderStatus? status,
    String? successMessage,
    String? errorMessage,
    bool? hasApiError,
  }) {
    return ReorderState(
      status: status ?? this.status,
      successMessage: successMessage ?? this.successMessage,
      errorMessage: errorMessage ?? this.errorMessage,
      hasApiError: hasApiError ?? this.hasApiError,
    );
  }

  @override
  List<Object> get props => [status, successMessage, errorMessage, hasApiError];
}
