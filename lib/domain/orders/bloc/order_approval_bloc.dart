import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/main.dart';
import 'package:goldenprizma/repositories/order_repository.dart';

part 'order_approval_event.dart';
part 'order_approval_state.dart';

class OrderApprovalBloc extends Bloc<OrderApprovalEvent, OrderApprovalState> {
  final OrderRepository orderRepository = getIt.get<OrderRepository>();

  OrderApprovalBloc() : super(const OrderApprovalState()) {
    on<AcceptOrder>(_onAcceptOrder);
    on<RejectOrder>(_onRejectOrder);
  }

  FutureOr<void> _onAcceptOrder(
    AcceptOrder event,
    Emitter<OrderApprovalState> emit,
  ) async {
    if (state.status == OrderApprovalStatus.loading) {
      return;
    }
    try {
      emit(state.copyWith(status: OrderApprovalStatus.loading));
      String message = await orderRepository.acceptOrRejectOrder(
        orderId: event.orderId,
        action: 'accept',
        reason: event.reason,
      );
      emit(
        state.copyWith(
          status: OrderApprovalStatus.success,
          successMessage: message,
          hasApiError: false,
          errorMessage: '',
        ),
      );
    } on DioException catch (error) {
      emit(
        state.copyWith(
            status: OrderApprovalStatus.failure,
            hasApiError: true,
            errorMessage: error.response?.data['error']),
      );
    } catch (error) {
      emit(
        state.copyWith(
          status: OrderApprovalStatus.failure,
          errorMessage: 'An error occured',
        ),
      );
    }
  }

  FutureOr<void> _onRejectOrder(
    RejectOrder event,
    Emitter<OrderApprovalState> emit,
  ) async {
    if (state.status == OrderApprovalStatus.loading) {
      return;
    }
    try {
      emit(state.copyWith(status: OrderApprovalStatus.loading));
      String message = await orderRepository.acceptOrRejectOrder(
        orderId: event.orderId,
        action: 'reject',
        reason: event.reason,
      );
      emit(
        state.copyWith(
          status: OrderApprovalStatus.success,
          successMessage: message,
          hasApiError: false,
          errorMessage: '',
        ),
      );
    } on DioException catch (error) {
      emit(
        state.copyWith(
            status: OrderApprovalStatus.failure,
            hasApiError: true,
            errorMessage: error.response?.data['error']),
      );
    } catch (error) {
      emit(
        state.copyWith(
          status: OrderApprovalStatus.failure,
          errorMessage: 'An error occured',
        ),
      );
    }
  }
}
