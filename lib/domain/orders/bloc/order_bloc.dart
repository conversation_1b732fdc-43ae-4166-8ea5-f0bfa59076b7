import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/orders/models/order.dart';
import 'package:goldenprizma/domain/orders/models/order_query.dart';
import 'package:goldenprizma/helpers/logger.dart';
import 'package:goldenprizma/repositories/order_repository.dart';

part 'order_event.dart';
part 'order_state.dart';

class OrderBloc extends Bloc<OrderEvent, OrderState> {
  final OrderRepository orderRepository;

  OrderBloc({required this.orderRepository}) : super(const OrderState()) {
    on<OrderRequestLoad>(_onRequestLoad);
    on<OrderReload>(_onOrderReload);
    on<OrderQueryChanged>(_onOrderQueryChanged);
    on<OrderSearchQueryChanged>(_onOrderSearchQueryChanged);
  }

  FutureOr<void> _onRequestLoad(
      OrderRequestLoad event, Emitter<OrderState> emit) async {
    if (state.status == OrderStatus.loading || state.hasReachedMax) {
      return;
    }
    try {
      if (state.query.page > 1) {
        emit(state.copyWith(status: OrderStatus.loading));
      }
      if (state.query.page == 1 && state.orders.isNotEmpty) {
        emit(state.copyWith(
          query: state.query.copyWith(page: state.query.page + 1),
        ));
      }

      var result = await _getOrders(query: state.query);

      List<Order> orders = result['orders'];
      int totalOrders = result['total'];

      Set<Order> uniqueOrders = Set<Order>.from(
          state.orders); // Assuming Order is your order class/type
      uniqueOrders.addAll(orders);

      var statuses = state.query.statuses;

      if (statuses.isNotEmpty && !statuses.contains(event.status)) {
        statuses.add("${event.status}");
      }

      if (statuses.isEmpty) {
        statuses = ["${event.status}"];
      }

      emit(state.copyWith(
        orders: uniqueOrders.toList(),
        status: OrderStatus.success,
        query: state.query.copyWith(
          page: orders.isNotEmpty ? state.query.page + 1 : state.query.page,
          statuses: statuses,
        ),
        hasReachedMax: orders.isEmpty,
        totalOrders: totalOrders,
        hasFetched: true,
      ));
    } catch (error) {
      emit(state.copyWith(status: OrderStatus.failure));
    }
  }

  Future<Map> _getOrders({required OrderQuery query}) async {
    return await orderRepository.getOrders(query.toMap());
  }

  FutureOr<void> _onOrderReload(
      OrderReload event, Emitter<OrderState> emit) async {
    if (state.status == OrderStatus.loading) {
      return;
    }
    try {
      emit(state.copyWith(status: OrderStatus.initial));
      var result = await _getOrders(query: event.query.copyWith(page: 1));
      List<Order> orders = result['orders'];
      int totalOrders = result['total'];
      emit(state.copyWith(
        orders: result['orders'],
        status: OrderStatus.success,
        hasReachedMax: orders.isEmpty,
        query: event.query.copyWith(page: 1),
        totalOrders: totalOrders,
      ));
    } catch (error) {
      emit(state.copyWith(status: OrderStatus.failure));
    }
  }

  FutureOr<void> _onOrderQueryChanged(
      OrderQueryChanged event, Emitter<OrderState> emit) {
    logger("query== ${event.query}");
    emit(state.copyWith(query: event.query.copyWith(page: 1)));
    add(OrderReload(query: state.query));
  }

  FutureOr<void> _onOrderSearchQueryChanged(
      OrderSearchQueryChanged event, Emitter<OrderState> emit) {
    emit(state.copyWith(
      query: state.query.copyWith(search: event.search, page: 1),
    ));
    add(OrderReload(query: state.query));
  }
}
