import 'dart:async';
import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/domain/orders/models/scrap_response.dart';
import 'package:goldenprizma/helpers/url_to_file.dart';
import 'package:goldenprizma/presentation/views/forms/order/country_input.dart';
import 'package:goldenprizma/presentation/views/forms/order/currency_input.dart';
import 'package:goldenprizma/presentation/views/forms/order/description_input.dart';
import 'package:goldenprizma/presentation/views/forms/order/image_input.dart';
import 'package:goldenprizma/presentation/views/forms/order/item_price_input.dart';
import 'package:goldenprizma/presentation/views/forms/order/link_input.dart';
import 'package:goldenprizma/presentation/views/forms/order/order_form_model.dart';
import 'package:goldenprizma/presentation/views/forms/order/quantity_field.dart';
import 'package:goldenprizma/presentation/views/forms/order/size_input.dart';
import 'package:goldenprizma/repositories/order_repository.dart';
import 'package:validators/validators.dart';

part 'order_form_event.dart';
part 'order_form_state.dart';

class OrderFormBloc extends Bloc<OrderFormEvent, OrderFormState> {
  OrderFormBloc({required this.orderRepository})
      : super(const OrderFormState()) {
    on<OrderFormChanged>(_onOrderFormChanged);
    on<ScrapLink>(_onScrapLink);
    on<OrderFormSubmitted>(_onOrderFormSubmitted);
    on<OrderFormReset>(_onOrderFormReset);
    on<ClientScraped>(_onClientScraped);
  }

  final OrderRepository orderRepository;

  FutureOr<void> _onOrderFormChanged(
      OrderFormChanged event, Emitter<OrderFormState> emit) {
    emit(
      state.copyWith(
        // status: Formz.validate(state.form.inputs),
        form: event.form,
      ),
    );
  }

  FutureOr<void> _onScrapLink(
    ScrapLink event,
    Emitter<OrderFormState> emit,
  ) async {
    try {
      if (event.link.isEmpty || !isURL(event.link)) {
        return;
      }
      emit(state.copyWith(scrapStatus: ScrapStatus.loading));
      OrderFormModel form = state.form;

      ScrapResponse scrapResponse =
          await orderRepository.scrap(link: event.link);

      if (scrapResponse.countryId != null) {
        form = form.copyWith(
            countryInput: CountryInput.dirty(scrapResponse.countryId));
      }

      if (scrapResponse.image != null && scrapResponse.image!.isNotEmpty) {
        File file = await urlToFile(scrapResponse.image ?? '');
        form = form.copyWith(imageInput: ImageInput.dirty(file));
      }

      emit(state.copyWith(
        scrapStatus: ScrapStatus.success,
        form: form,
        scrapResponse: scrapResponse,
      ));
    } catch (error) {
      emit(state.copyWith(scrapStatus: ScrapStatus.failure));
    }
  }

  FutureOr<void> _onClientScraped(
    ClientScraped event,
    Emitter<OrderFormState> emit,
  ) async {
    emit(state.copyWith(
        scrapResponse: state.scrapResponse.copyWith(
      websiteId: event.websiteId ?? state.scrapResponse.websiteId,
      itemCode: event.itemCode != null && event.itemCode!.isNotEmpty
          ? "${event.itemCode}"
          : state.scrapResponse.itemCode,
      itemPrice: event.itemPrice != null && event.itemPrice != 0.0
          ? "${event.itemPrice}"
          : state.scrapResponse.itemPrice,
    )));
  }

  FutureOr<void> _onOrderFormSubmitted(
    OrderFormSubmitted event,
    Emitter<OrderFormState> emit,
  ) async {
    emit(state.copyWith(
      status: Formz.validate(state.form.inputs),
      form: state.form.copyWith(
        linkInput: LinkInput.dirty(state.form.linkInput.value),
        countryInput: CountryInput.dirty(state.form.countryInput.value),
        sizeInput: SizeInput.dirty(state.form.sizeInput.value),
        imageInput: ImageInput.dirty(state.form.imageInput.value),
        quantityInput: QuantityInput.dirty(state.form.quantityInput.value),
        itemPriceInput: ItemPriceInput.dirty(state.form.itemPriceInput.value),
        currencyInput: CurrencyInput.dirty(state.form.currencyInput.value),
        descriptionInput:
            DescriptionInput.dirty(state.form.descriptionInput.value),
      ),
    ));

    if (state.form.status.isValidated) {
      emit(state.copyWith(status: FormzStatus.submissionInProgress));

      try {
        // send post request
        Map<String, dynamic> response = await orderRepository.create(
          {
            'link': state.form.linkInput.value,
            'country_id': state.form.countryInput.value,
            'size_id': state.form.sizeInput.value?.id,
            'quantity': state.form.quantityInput.value,
            'description': state.form.descriptionInput.value,
            'item_price': state.form.itemPriceInput.value ??
                state.scrapResponse.itemPrice,
            'item_code': state.scrapResponse.itemCode,
            'from_currency': state.form.currencyInput.value ??
                state.scrapResponse.fromCurrency,
            'website_id': state.scrapResponse.websiteId,
            'brand_id': state.scrapResponse.brandId,
            'image_path': state.form.imageInput.value?.path
          },
        );

        emit(state.copyWith(
          status: FormzStatus.submissionSuccess,
          apiSuccessMessage: response['message'],
          hasServerError: false,
          apirErrorMessage: '',
        ));
      } catch (error) {
        if (error is DioException) {
          emit(state.copyWith(
            status: FormzStatus.submissionFailure,
            hasServerError: true,
            apirErrorMessage: error.response?.data['error'] ?? '',
          ));
        } else {
          emit(state.copyWith(
              status: FormzStatus.submissionFailure, hasServerError: false));
        }
      }
    }
  }

  FutureOr<void> _onOrderFormReset(
    OrderFormReset event,
    Emitter<OrderFormState> emit,
  ) {
    emit(state.copyWith(
      status: FormzStatus.pure,
      form: state.form.copyWith(
        linkInput: const LinkInput.pure(),
        countryInput: const CountryInput.pure(),
        sizeInput: const SizeInput.pure(),
        imageInput: const ImageInput.pure(),
        descriptionInput: const DescriptionInput.pure(),
      ),
      scrapResponse: const ScrapResponse(),
    ));
  }
}
