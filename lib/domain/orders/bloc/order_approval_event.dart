part of 'order_approval_bloc.dart';

abstract class OrderApprovalEvent extends Equatable {
  const OrderApprovalEvent();

  @override
  List<Object> get props => [];
}

class AcceptOrder extends OrderApprovalEvent {
  const AcceptOrder({
    required this.orderId,
    this.reason = '',
  });

  final int orderId;
  final String reason;

  @override
  List<Object> get props => [orderId, reason];
}

class RejectOrder extends OrderApprovalEvent {
  const RejectOrder({
    required this.orderId,
    this.reason = '',
  });

  final int orderId;
  final String reason;

  @override
  List<Object> get props => [orderId, reason];
}
