part of 'order_approval_bloc.dart';

enum OrderApprovalStatus { intial, success, loading, failure }

class OrderApprovalState extends Equatable {
  const OrderApprovalState({
    this.status = OrderApprovalStatus.intial,
    this.errorMessage = '',
    this.successMessage = '',
    this.hasApiError = false,
  });
  final OrderApprovalStatus status;
  final String errorMessage;
  final String successMessage;
  final bool hasApiError;

  @override
  List<Object> get props => [status, errorMessage, successMessage, hasApiError];

  OrderApprovalState copyWith({
    OrderApprovalStatus? status,
    String? errorMessage,
    String? successMessage,
    bool? hasApiError,
  }) {
    return OrderApprovalState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      successMessage: successMessage ?? this.successMessage,
      hasApiError: hasApiError ?? this.hasApiError,
    );
  }

  @override
  String toString() {
    return 'OrderApprovalState(status: $status, errorMessage: $errorMessage, successMessage: $successMessage, hasApiError: $hasApiError)';
  }
}
