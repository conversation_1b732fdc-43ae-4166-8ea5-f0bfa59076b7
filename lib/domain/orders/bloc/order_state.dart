part of 'order_bloc.dart';

enum OrderStatus { initial, loading, success, failure }

class OrderState extends Equatable {
  const OrderState({
    this.orders = const [],
    this.totalOrders = 0,
    this.hasFetched = false,
    this.hasReachedMax = false,
    this.status = OrderStatus.initial,
    this.query = const OrderQuery(),
  });

  final List<Order> orders;
  final int totalOrders;
  final bool hasFetched;
  final bool hasReachedMax;
  final OrderStatus status;
  final OrderQuery query;

  @override
  List<Object> get props =>
      [orders, totalOrders, hasFetched, hasReachedMax, status, query];

  OrderState copyWith({
    List<Order>? orders,
    int? totalOrders,
    bool? hasFetched,
    bool? hasReachedMax,
    int? page,
    OrderStatus? status,
    OrderQuery? query,
  }) {
    return OrderState(
      orders: orders ?? this.orders,
      totalOrders: totalOrders ?? this.totalOrders,
      hasFetched: hasFetched ?? this.hasFetched,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      status: status ?? this.status,
      query: query ?? this.query,
    );
  }

  bool isQueryEmpty() =>
      query.brandIds.isEmpty &&
      query.search.isEmpty &&
      query.statuses.isEmpty &&
      query.sizeIds.isEmpty &&
      query.websiteIds.isEmpty;

  @override
  String toString() {
    return 'OrderState(orders: $orders, totalOrders: $totalOrders, hasFetched: $hasFetched, hasReachedMax: $hasReachedMax, status: $status, query: $query)';
  }
}
