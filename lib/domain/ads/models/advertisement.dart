import 'package:equatable/equatable.dart';

class Advertisement extends Equatable {
  final int id;
  final String url;
  final String? actionUrl;
  final String image;
  final bool isPage;
  final int sortNumber;
  final bool inApp;
  final String appAction;
  final String? appActionData;
  final String? note;

  const Advertisement({
    required this.id,
    required this.url,
    this.actionUrl,
    required this.image,
    this.isPage = false,
    this.sortNumber = 0,
    this.inApp = true,
    this.appAction = 'external_url',
    this.appActionData,
    this.note,
  });

  Advertisement copyWith({
    int? id,
    String? url,
    String? actionUrl,
    String? image,
    bool? isPage,
    int? sortNumber,
    bool? inApp,
    String? appAction,
    String? appActionData,
    String? note,
  }) {
    return Advertisement(
      id: id ?? this.id,
      url: url ?? this.url,
      actionUrl: actionUrl ?? this.actionUrl,
      image: image ?? this.image,
      isPage: isPage ?? this.isPage,
      sortNumber: sortNumber ?? this.sortNumber,
      inApp: inApp ?? this.inApp,
      appAction: appAction ?? this.appAction,
      appActionData: appActionData ?? this.appActionData,
      note: note ?? this.note,
    );
  }

  Advertisement.fromJson(Map<String, dynamic> jsonData)
      : id = jsonData['id'],
        url = jsonData['url'],
        actionUrl = jsonData['action_url'],
        image = jsonData['image'],
        isPage = jsonData['is_page'] ?? false,
        sortNumber = jsonData['sort_number'] ?? 0,
        inApp = jsonData['in_app'] ?? true,
        appAction = jsonData['app_action'] ?? 'external_url',
        appActionData = jsonData['app_action_data'],
        note = jsonData['note'];

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'url': url,
      'action_url': actionUrl,
      'image': image,
      'is_page': isPage,
      'sort_number': sortNumber,
      'in_app': inApp,
      'app_action': appAction,
      'app_action_data': appActionData,
      'note': note,
    };
  }

  // Helper methods for different app actions
  bool get isExternalUrl => appAction == 'external_url';
  bool get isBrandStore => appAction == 'brand_store';
  bool get isPageAction => appAction == 'page';

  // Get brand ID for brand store action
  int? get brandId => isBrandStore && appActionData != null
      ? int.tryParse(appActionData!)
      : null;

  // Get page slug for page action
  String? get pageSlug => isPageAction ? appActionData : null;

  // Get external URL
  String? get externalUrl => isExternalUrl ? appActionData : null;

  @override
  String toString() {
    return 'Advertisement(id: $id, url: $url, actionUrl: $actionUrl, image: $image, '
        'isPage: $isPage, sortNumber: $sortNumber, inApp: $inApp, '
        'appAction: $appAction, appActionData: $appActionData, note: $note)';
  }

  @override
  List<Object?> get props => [
        id,
        url,
        actionUrl,
        image,
        isPage,
        sortNumber,
        inApp,
        appAction,
        appActionData,
        note,
      ];
}
