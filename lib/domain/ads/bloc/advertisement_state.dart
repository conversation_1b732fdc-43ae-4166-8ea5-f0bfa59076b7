part of 'advertisement_bloc.dart';

enum AdvertisementStatus { initial, loading, success, failure }

class AdvertisementState extends Equatable {
  final List<Advertisement> advertismenets;
  final AdvertisementStatus status;
  final int page;
  final bool hasReachedMax;

  const AdvertisementState({
    this.advertismenets = const [],
    this.status = AdvertisementStatus.initial,
    this.page = 1,
    this.hasReachedMax = false,
  });

  @override
  List<Object> get props => [advertismenets, status, page, hasReachedMax];

  AdvertisementState copyWith({
    List<Advertisement>? advertismenets,
    AdvertisementStatus? status,
    int? page,
    bool? hasReachedMax,
  }) {
    return AdvertisementState(
      advertismenets: advertismenets ?? this.advertismenets,
      status: status ?? this.status,
      page: page ?? this.page,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
    );
  }
}
