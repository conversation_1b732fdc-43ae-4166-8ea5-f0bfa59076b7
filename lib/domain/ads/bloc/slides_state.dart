part of 'slides_bloc.dart';

enum SlidesStatus { initial, success, failure }

class SlidesState extends Equatable {
  final List<Advertisement> slides;
  final SlidesStatus status;

  const SlidesState({
    this.slides = const [],
    this.status = SlidesStatus.initial,
  });

  @override
  List<Object> get props => [status, slides];

  SlidesState copyWith({
    List<Advertisement>? slides,
    SlidesStatus? status,
  }) {
    return SlidesState(
      slides: slides ?? this.slides,
      status: status ?? this.status,
    );
  }
}
