import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/ads/models/advertisement.dart';
import 'package:goldenprizma/repositories/slides_repository.dart';

part 'slides_event.dart';
part 'slides_state.dart';

class SlidesBloc extends Bloc<SlidesEvent, SlidesState> {
  final SlidesRepository _slidesRepository;
  SlidesBloc(this._slidesRepository) : super(const SlidesState()) {
    on<SlidesRequestLoad>(_onSlidesRequestLoad);
  }

  FutureOr<void> _onSlidesRequestLoad(
      SlidesRequestLoad event, Emitter<SlidesState> emit) async {
    try {
      List<Advertisement> slides = await _slidesRepository.getSlides();

      emit(state.copyWith(slides: slides, status: SlidesStatus.success));
    } catch (error) {
      emit(state.copyWith(status: SlidesStatus.failure));
    }
  }
}
