import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/ads/models/advertisement.dart';
import 'package:goldenprizma/repositories/advertisemnt_repository.dart';

part 'advertisement_event.dart';
part 'advertisement_state.dart';

class AdvertisementBloc extends Bloc<AdvertisementEvent, AdvertisementState> {
  final AdvertisementsRepository _advertisementsRepository;
  AdvertisementBloc(this._advertisementsRepository)
      : super(const AdvertisementState()) {
    on<AdvertisementRequestLoad>(_onRequestLoad);
  }

  FutureOr<void> _onRequestLoad(
      AdvertisementRequestLoad event, Emitter<AdvertisementState> emit) async {
    try {
      //prevent api call if it's already loading
      // or if it's reached max rows
      if (state.hasReachedMax || state.status == AdvertisementStatus.loading) {
        return;
      }

      if (state.page > 1) {
        emit(state.copyWith(status: AdvertisementStatus.loading));
      }

      List<Advertisement> advertisements =
          await _advertisementsRepository.getAds(state.page);

      emit(state.copyWith(
        advertismenets: List.of(state.advertismenets)..addAll(advertisements),
        page: state.hasReachedMax ? state.page : state.page + 1,
        status: AdvertisementStatus.success,
        hasReachedMax: advertisements.isEmpty,
      ));
    } catch (exception) {
      emit(state.copyWith(status: AdvertisementStatus.failure));
    }
  }
}
