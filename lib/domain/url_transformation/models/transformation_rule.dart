import 'package:equatable/equatable.dart';

/// Represents a URL transformation rule for redirecting URLs to specific locales
class TransformationRule extends Equatable {
  const TransformationRule({
    required this.domain,
    required this.sourcePattern,
    required this.targetPattern,
    this.decodeUrl = false,
    this.priority = 0,
  });

  /// Domain to match (e.g., "zara.com", "*.shein.com")
  final String domain;

  /// Pattern to match in the source URL (e.g., "*/share/*", "*go=https%3A*")
  final String sourcePattern;

  /// Target pattern to transform to (e.g., "/tr/tr", "/tr/en")
  final String targetPattern;

  /// Whether to decode URL-encoded parameters
  final bool decodeUrl;

  /// Priority for rule matching (higher number = higher priority)
  final int priority;

  TransformationRule copyWith({
    String? domain,
    String? sourcePattern,
    String? targetPattern,
    bool? decodeUrl,
    int? priority,
  }) {
    return TransformationRule(
      domain: domain ?? this.domain,
      sourcePattern: sourcePattern ?? this.sourcePattern,
      targetPattern: targetPattern ?? this.targetPattern,
      decodeUrl: decodeUrl ?? this.decodeUrl,
      priority: priority ?? this.priority,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'domain': domain,
      'source_pattern': sourcePattern,
      'target_pattern': targetPattern,
      'decode_url': decodeUrl,
      'priority': priority,
    };
  }

  factory TransformationRule.fromMap(Map<String, dynamic> map) {
    return TransformationRule(
      domain: map['domain'] ?? '',
      sourcePattern: map['source_pattern'] ?? '',
      targetPattern: map['target_pattern'] ?? '',
      decodeUrl: map['decode_url'] ?? false,
      priority: map['priority']?.toInt() ?? 0,
    );
  }

  factory TransformationRule.fromJson(Map<String, dynamic> json) {
    return TransformationRule(
      domain: json['domain'] ?? '',
      sourcePattern: json['source_pattern'] ?? '',
      targetPattern: json['target_pattern'] ?? '',
      decodeUrl: json['decode_url'] ?? false,
      priority: json['priority']?.toInt() ?? 0,
    );
  }

  @override
  List<Object?> get props => [
        domain,
        sourcePattern,
        targetPattern,
        decodeUrl,
        priority,
      ];

  @override
  String toString() {
    return 'TransformationRule(domain: $domain, sourcePattern: $sourcePattern, targetPattern: $targetPattern, decodeUrl: $decodeUrl, priority: $priority)';
  }
}

/// Container for all transformation rules
class TransformationRules extends Equatable {
  const TransformationRules({
    required this.rules,
    this.version = 1,
    this.lastUpdated,
  });

  final List<TransformationRule> rules;
  final int version;
  final DateTime? lastUpdated;

  TransformationRules copyWith({
    List<TransformationRule>? rules,
    int? version,
    DateTime? lastUpdated,
  }) {
    return TransformationRules(
      rules: rules ?? this.rules,
      version: version ?? this.version,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'rules': rules.map((rule) => rule.toMap()).toList(),
      'version': version,
      'last_updated': lastUpdated?.toIso8601String(),
    };
  }

  factory TransformationRules.fromMap(Map<String, dynamic> map) {
    return TransformationRules(
      rules: List<TransformationRule>.from(
        (map['rules'] ?? []).map((x) => TransformationRule.fromMap(x)),
      ),
      version: map['version']?.toInt() ?? 1,
      lastUpdated: map['last_updated'] != null
          ? DateTime.parse(map['last_updated'])
          : null,
    );
  }

  factory TransformationRules.fromJson(Map<String, dynamic> json) {
    return TransformationRules(
      rules: List<TransformationRule>.from(
        (json['rules'] ?? []).map((x) => TransformationRule.fromJson(x)),
      ),
      version: json['version']?.toInt() ?? 1,
      lastUpdated: json['last_updated'] != null
          ? DateTime.parse(json['last_updated'])
          : null,
    );
  }

  @override
  List<Object?> get props => [rules, version, lastUpdated];

  @override
  String toString() {
    return 'TransformationRules(rules: ${rules.length}, version: $version, lastUpdated: $lastUpdated)';
  }
}
