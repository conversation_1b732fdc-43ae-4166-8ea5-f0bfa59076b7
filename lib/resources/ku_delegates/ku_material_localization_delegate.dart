import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

class _KuMaterialLocalizationsDelegate
    extends LocalizationsDelegate<MaterialLocalizations> {
  const _KuMaterialLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) => locale.languageCode == 'ku';

  @override
  Future<MaterialLocalizations> load(Locale locale) async {
    return SynchronousFuture<MaterialLocalizations>(
      KuMaterialLocalizations(
        localeName: "ku",
        fullYearFormat: intl.DateFormat('y'),
        mediumDateFormat: intl.DateFormat('EEE, MMM d'),
        longDateFormat: intl.DateFormat('EEEE, MMMM d, y'),
        yearMonthFormat: intl.DateFormat('MMMM y'),
        decimalFormat: intl.NumberFormat('#,##0.###'),
        twoDigitZeroPaddedFormat: intl.NumberFormat('00'),
        compactDateFormat: intl.DateFormat(),
        shortDateFormat: intl.DateFormat(),
        shortMonthDayFormat: intl.DateFormat(),
      ),
    );
  }

  @override
  bool shouldReload(_KuMaterialLocalizationsDelegate old) => false;
}

class KuMaterialLocalizations extends GlobalMaterialLocalizations {
  const KuMaterialLocalizations({
    super.localeName = 'ku',
    required super.fullYearFormat,
    required super.mediumDateFormat,
    required super.longDateFormat,
    required super.yearMonthFormat,
    required super.decimalFormat,
    required super.twoDigitZeroPaddedFormat,
    required super.compactDateFormat,
    required super.shortMonthDayFormat,
    required super.shortDateFormat,
  });

  @override
  String get aboutListTileTitleRaw => r'لەبارەی $applicationName';

  @override
  String get alertDialogLabel => r'وریاکەرەوە';

  @override
  String get anteMeridiemAbbreviation => r'AM';

  @override
  String get backButtonTooltip => r'گەڕانەوە';

  @override
  String get cancelButtonLabel => r'پەشیمانبوونەوە';

  @override
  String get closeButtonLabel => r'داخستن';

  @override
  String get closeButtonTooltip => r'داخستن';

  @override
  String get collapsedIconTapHint => r'گەورەکردن';

  @override
  String get continueButtonLabel => r'بەردەوامبە';

  @override
  String get copyButtonLabel => r'لەبەرگرتنەوە';

  @override
  String get cutButtonLabel => r'بڕین';

  @override
  String get deleteButtonTooltip => r'سڕینەوە';

  @override
  String get dialogLabel => r'دایالۆگ';

  @override
  String get drawerLabel => r'مێنو';

  @override
  String get expandedIconTapHint => r'داخستن';

  @override
  String get hideAccountsLabel => r'شاردنەوەی هەژمارەکان';

  @override
  String get licensesPageTitle => r'مۆڵەت';

  @override
  String get modalBarrierDismissLabel => r'لابردن';

  @override
  String get nextMonthTooltip => r'مانگی داهاتوو';

  @override
  String get nextPageTooltip => r'لاپەڕەی داهاتوو';

  @override
  String get okButtonLabel => r'باشە';

  @override
  String get openAppDrawerTooltip => r'مێنو بکەرەوە';

  @override
  String get pageRowsInfoTitleRaw => r'$firstRow–$lastRow of $rowCount';

  @override
  String get pageRowsInfoTitleApproximateRaw =>
      r'$firstRow–$lastRow of about $rowCount';

  @override
  String get pasteButtonLabel => r'دانان';

  @override
  String get popupMenuLabel => r'Popup مێنو';

  @override
  String get postMeridiemAbbreviation => r'PM';

  @override
  String get previousMonthTooltip => r'مانگی پێشوو';

  @override
  String get previousPageTooltip => r'لاپەڕەی پێشوو';

  @override
  String get refreshIndicatorSemanticLabel => r'تازەکردنەوە';

  @override
  String get remainingTextFieldCharacterCountFew => '';

  @override
  String get remainingTextFieldCharacterCountMany => '';

  @override
  String get remainingTextFieldCharacterCountOne => r'١ پیت ماوە';

  @override
  String get remainingTextFieldCharacterCountOther =>
      r'$remainingCount پیت ماوە';

  @override
  String get remainingTextFieldCharacterCountTwo => '';

  @override
  String get remainingTextFieldCharacterCountZero => r'هیچ پیت نەماوە';

  @override
  String get reorderItemDown => r'بڕۆ خوارەوە';

  @override
  String get reorderItemLeft => r'بڕۆ بۆلای چەپ';

  @override
  String get reorderItemRight => r'بڕۆ بۆلای ڕاست';

  @override
  String get reorderItemToEnd => r'بڕۆ بۆ کۆتایی';

  @override
  String get reorderItemToStart => r'بڕۆ بۆ سەرەتا';

  @override
  String get reorderItemUp => r'بڕۆ سەرەوە';

  @override
  String get rowsPerPageTitle => r'ڕیز بۆ هەر لاپەڕەیەک:';

  @override
  ScriptCategory get scriptCategory => ScriptCategory.tall;

  @override
  String get searchFieldLabel => r'گەڕان';

  @override
  String get selectAllButtonLabel => r'هەموی دیاریبکە';

  @override
  String get selectedRowCountTitleFew => '';

  @override
  String get selectedRowCountTitleMany => '';

  @override
  String get selectedRowCountTitleOne => r'١ دانە دیاریکراوە';

  @override
  String get selectedRowCountTitleOther => r'$selectedRowCount دانە دیاریکراوە';

  @override
  String get selectedRowCountTitleTwo => '';

  @override
  String get selectedRowCountTitleZero => r'هیچ دیارینەکراوە';

  @override
  String get showAccountsLabel => r'هەژمارەکان پیشاندبدە';

  @override
  String get showMenuTooltip => r'مێنو پیشانبدە';

  @override
  String get signedInLabel => r'داخڵبووە';

  @override
  String get tabLabelRaw => r'Tab $tabIndex of $tabCount';

  @override
  TimeOfDayFormat get timeOfDayFormatRaw => TimeOfDayFormat.h_colon_mm_space_a;

  @override
  String get timePickerHourModeAnnouncement => r'کاتژمێر دیاریبکە';

  @override
  String get timePickerMinuteModeAnnouncement => r'خولەک دیاریبکە';

  @override
  String get viewLicensesButtonLabel => r'مۆڵەتەکان ببینە';

  @override
  List<String> get narrowWeekdays =>
      const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'];

  @override
  int get firstDayOfWeekIndex => 0;

  static const LocalizationsDelegate<MaterialLocalizations> delegate =
      _KuMaterialLocalizationsDelegate();

  @override
  String get moreButtonTooltip => '';

  @override
  String get calendarModeButtonLabel => '';

  @override
  String get dateHelpText => '';

  @override
  String get dateInputLabel => '';

  @override
  String get dateOutOfRangeLabel => '';

  @override
  String get datePickerHelpText => '';

  @override
  String get dateRangeEndDateSemanticLabelRaw => '';

  @override
  String get dateRangeEndLabel => '';

  @override
  String get dateRangePickerHelpText => '';

  @override
  String get dateRangeStartDateSemanticLabelRaw => '';

  @override
  String get dateRangeStartLabel => '';

  @override
  String get dateSeparator => '';

  @override
  String get dialModeButtonLabel => '';

  @override
  String get firstPageTooltip => '';

  @override
  String get inputDateModeButtonLabel => '';

  @override
  String get inputTimeModeButtonLabel => '';

  @override
  String get invalidDateFormatLabel => '';

  @override
  String get invalidDateRangeLabel => '';

  @override
  String get invalidTimeLabel => '';

  @override
  String get lastPageTooltip => '';

  @override
  String get licensesPackageDetailTextOther => '';

  @override
  String get saveButtonLabel => '';

  @override
  String get selectYearSemanticsLabel => '';

  @override
  String get timePickerDialHelpText => '';

  @override
  String get timePickerHourLabel => '';

  @override
  String get timePickerInputHelpText => '';

  @override
  String get timePickerMinuteLabel => '';

  @override
  String get unspecifiedDate => '';

  @override
  String get unspecifiedDateRange => '';

  @override
  String get keyboardKeyAlt => '';

  @override
  String get keyboardKeyAltGraph => '';

  @override
  String get keyboardKeyBackspace => '';

  @override
  String get keyboardKeyCapsLock => '';

  @override
  String get keyboardKeyChannelDown => '';

  @override
  String get keyboardKeyChannelUp => '';

  @override
  String get keyboardKeyControl => '';

  @override
  String get keyboardKeyDelete => '';

  @override
  String get keyboardKeyEject => '';

  @override
  String get keyboardKeyEnd => '';

  @override
  String get keyboardKeyEscape => '';

  @override
  String get keyboardKeyFn => '';

  @override
  String get keyboardKeyHome => '';

  @override
  String get keyboardKeyInsert => '';

  @override
  String get keyboardKeyMeta => '';

  @override
  String get keyboardKeyMetaMacOs => '';

  @override
  String get keyboardKeyMetaWindows => '';

  @override
  String get keyboardKeyNumLock => '';

  @override
  String get keyboardKeyNumpad0 => '';

  @override
  String get keyboardKeyNumpad1 => '';

  @override
  String get keyboardKeyNumpad2 => '';

  @override
  String get keyboardKeyNumpad3 => '';

  @override
  String get keyboardKeyNumpad4 => '';

  @override
  String get keyboardKeyNumpad5 => '';

  @override
  String get keyboardKeyNumpad6 => '';

  @override
  String get keyboardKeyNumpad7 => '';

  @override
  String get keyboardKeyNumpad8 => '';

  @override
  String get keyboardKeyNumpad9 => '';

  @override
  String get keyboardKeyNumpadAdd => '';

  @override
  String get keyboardKeyNumpadComma => '';

  @override
  String get keyboardKeyNumpadDecimal => '';

  @override
  String get keyboardKeyNumpadDivide => '';

  @override
  String get keyboardKeyNumpadEnter => '';

  @override
  String get keyboardKeyNumpadEqual => '';

  @override
  String get keyboardKeyNumpadMultiply => '';

  @override
  String get keyboardKeyNumpadParenLeft => '';

  @override
  String get keyboardKeyNumpadParenRight => '';

  @override
  String get keyboardKeyNumpadSubtract => '';

  @override
  String get keyboardKeyPageDown => '';

  @override
  String get keyboardKeyPageUp => '';

  @override
  String get keyboardKeyPower => '';

  @override
  String get keyboardKeyPowerOff => '';

  @override
  String get keyboardKeyPrintScreen => '';

  @override
  String get keyboardKeyScrollLock => '';

  @override
  String get keyboardKeySelect => '';

  @override
  String get keyboardKeySpace => '';

  @override
  String get bottomSheetLabel => "";

  @override
  String get collapsedHint => "";

  @override
  String get currentDateLabel => "";

  @override
  String get expandedHint => "";

  @override
  String get expansionTileCollapsedHint => "";

  @override
  String get expansionTileCollapsedTapHint => "";

  @override
  String get expansionTileExpandedHint => "";

  @override
  String get expansionTileExpandedTapHint => "";

  @override
  String get keyboardKeyShift => "";

  @override
  String get menuBarMenuLabel => "";

  @override
  String get scanTextButtonLabel => "";

  @override
  String get scrimLabel => "";

  @override
  String get scrimOnTapHintRaw => "";

  @override
  String get lookUpButtonLabel => throw UnimplementedError();

  @override
  String get menuDismissLabel => throw UnimplementedError();

  @override
  String get searchWebButtonLabel => throw UnimplementedError();

  @override
  String get shareButtonLabel => throw UnimplementedError();

  @override
  String get clearButtonTooltip => 'پاککردن';

  @override
  String get selectedDateLabel => 'بەرواری دیاریکراو';
}
