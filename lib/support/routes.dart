import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/auth/bloc/otp_bloc.dart';
import 'package:goldenprizma/domain/auth/bloc/profile_bloc.dart';
import 'package:goldenprizma/domain/auth/bloc/register_bloc.dart';
import 'package:goldenprizma/domain/orders/bloc/order_bloc.dart';
import 'package:goldenprizma/helpers/logger.dart';
import 'package:goldenprizma/presentation/views/screens/check_auth_screen.dart';
import 'package:goldenprizma/presentation/views/screens/home_screen.dart';
import 'package:goldenprizma/presentation/views/screens/locale_screen.dart';
import 'package:goldenprizma/presentation/views/screens/new_order_screen.dart';
import 'package:goldenprizma/presentation/views/screens/notifications_screen.dart';
import 'package:goldenprizma/presentation/views/screens/onboarding_screen.dart';
import 'package:goldenprizma/presentation/views/screens/otp_screen.dart';
import 'package:goldenprizma/presentation/views/screens/profile_screen.dart';
import 'package:goldenprizma/presentation/views/screens/register_screen.dart';
import 'package:goldenprizma/presentation/views/screens/transactions_screen.dart';
import 'package:goldenprizma/presentation/views/screens/wallet_screen.dart';
import 'package:goldenprizma/repositories/city_repository.dart';
import 'package:goldenprizma/repositories/district_repository.dart';

import '../presentation/views/auth/login_screen.dart';

Map<String, WidgetBuilder> routes() {
  return <String, WidgetBuilder>{
    LocaleScreen.routeName: (BuildContext context) => const LocaleScreen(),
    HomeScreen.routeName: (BuildContext context) => const HomeScreen(),
    OnboardingScreen.routeName: (BuildContext context) =>
        const OnboardingScreen(),
    OTPScreen.routeName: (BuildContext context) => BlocProvider(
          create: (context) => OtpBloc(),
          child: Builder(builder: (context) {
            final isForgetPassword =
                ModalRoute.of(context)?.settings.arguments as bool;
            logger("isForgetPassword $isForgetPassword");
            return OTPScreen(isForgetPassword: isForgetPassword);
          }),
        ),

    CheckAuthScreen.routeName: (BuildContext context) =>
        const CheckAuthScreen(),
    LoginScreen.routeName: (BuildContext context) => const LoginScreen(),
    RegisterScreen.routeName: (BuildContext context) => BlocProvider(
          create: (context) => RegisterBloc(),
          child: Builder(builder: (context) {
            final Map<String, String> data = ModalRoute.of(context)
                ?.settings
                .arguments as Map<String, String>;

            return RegisterScreen(
                phoneNumber: data['phoneNumber']!,
                countryCode: data['countryCode']!,
                dialCode: data['dialCode']!);
          }),
        ),
    // Verification.routeName: (BuildContext context) => const Verification(),
    Notifications.routeName: (BuildContext context) => BlocProvider.value(
          value: context.read<OrderBloc>(),
          child: const Notifications(),
        ),
    ProfileScreen.routeName: (BuildContext context) => MultiRepositoryProvider(
          providers: [
            RepositoryProvider(create: (_) => CityRepository()),
            RepositoryProvider(create: (_) => DistrictRepository()),
          ],
          child: BlocProvider(
            create: (context) => ProfileBloc(),
            child: const ProfileScreen(),
          ),
        ),
    NewOrderScreen.routeName: (BuildContext context) => const NewOrderScreen(),
    WalletScreen.routeName: (BuildContext context) => const WalletScreen(),
    TransactionsScreen.routeName: (BuildContext context) =>
        const TransactionsScreen(),
  };
}
