import 'package:bloc/bloc.dart';
import 'package:goldenprizma/helpers/logger.dart';

class AppBlocObserver extends BlocObserver {
  @override
  void onCreate(BlocBase bloc) {
    super.onCreate(bloc);
    logger('>>>>>onCreate --${bloc.runtimeType}');
  }

  @override
  void onChange(BlocBase bloc, Change change) {
    super.onChange(bloc, change);
    logger('>>>>> onChange -- ${bloc.runtimeType} $change');
  }

  @override
  void onError(BlocBase bloc, Object error, StackTrace stackTrace) {
    logger('#### onError -- ${bloc.runtimeType} $error $stackTrace ####');
    super.onError(bloc, error, stackTrace);
  }

  // @override
  // void onTransition(Bloc bloc, Transition transition) {
  //   super.onTransition(bloc, transition);
  //   logger('<<<<< ${bloc.runtimeType} $transition >>>>');
  // }

  @override
  void onClose(BlocBase bloc) {
    super.onClose(bloc);
    logger('>>>>>onClose -- ${bloc.runtimeType}');
  }
}
