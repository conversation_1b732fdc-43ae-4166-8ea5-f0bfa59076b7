import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/store/models/store_order.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';
import 'package:goldenprizma/helpers/logger.dart';

class StoreOrderRepository extends Repository {
  Future<Map<String, dynamic>> placeStoreOrder(StoreOrder storeOrder) async {
    logger("placeStoreOrder: ${storeOrder.toMap()}");
    
    try {
      Response response = await api.post(
        ApiUrls.placeStoreOrder,
        data: storeOrder.toMap(),
        options: Options(
          headers: {
            'auth': true,
            'Content-Type': 'application/json',
          },
        ),
      );

      logger("placeStoreOrder response: ${response.data}");

      return {
        'success': response.data['success'] ?? true,
        'message': response.data['message'] ?? 'Order placed successfully',
        'data': response.data['data'],
      };
    } catch (error) {
      logger("placeStoreOrder error: $error");
      
      if (error is DioException) {
        final errorMessage = error.response?.data?['message'] ?? 
                           error.response?.data?['error'] ?? 
                           'Failed to place order';
        
        return {
          'success': false,
          'message': errorMessage,
          'data': null,
        };
      }
      
      return {
        'success': false,
        'message': 'An unexpected error occurred',
        'data': null,
      };
    }
  }
}
