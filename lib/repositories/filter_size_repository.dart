import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/orders/models/size_model.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';

class FilterSizeRepository extends Repository {
  final List<SizeModel> _sizes = [];

  Future<List<SizeModel>> getAll(
      {String filter = '', bool ignoreCache = false}) async {
    if (_sizes.isNotEmpty && filter.isEmpty && ignoreCache == true) {
      return _sizes;
    }

    Response response = await api.get(
      ApiUrls.sizes,
      queryParameters: {'search': filter},
      options: Options(headers: {'auth': true}),
    );

    _sizes
      ..clear()
      ..addAll(SizeModel.fromJsonList(response.data['data']));

    return _sizes;
  }
}
