import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:goldenprizma/domain/home/<USER>/home_data.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';

class HomeRepository extends Repository {
  Future<HomeData> getHomeData() async {
    try {
      Response response = await api.get(
        ApiUrls.home,
        options: Options(headers: {'auth': false}),
      );

      debugPrint('Home data response: ${response.data}');
      debugPrint('Home data status code: ${response.statusCode}');

      if (response.data['success'] == true) {
        final homeData = HomeData.fromJson(response.data);
        debugPrint(
            'Parsed HomeData: slides count = ${homeData.slides.length}, exchange rates count = ${homeData.exchangeRates.length}');
        return homeData;
      } else {
        final errorMessage = response.data['message'] ?? 'Unknown error';
        debugPrint('Home data API returned success=false: $errorMessage');
        throw Exception('Failed to fetch home data: $errorMessage');
      }
    } on DioException catch (e) {
      debugPrint('Home data DioException: ${e.message}');
      debugPrint('Home data DioException response: ${e.response?.data}');
      debugPrint(
          'Home data DioException status code: ${e.response?.statusCode}');
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      debugPrint('Home data parsing error: $e');
      throw Exception('Failed to fetch home data: $e');
    }
  }
}
