import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/store/models/category.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/store_repository.dart';

class CategoriesRepository extends StoreRepository {
  final List<Category> _categories = [];

  Future<List<Category>> getCategories() async {
    if (_categories.isNotEmpty) {
      return _categories;
    }

    try {
      Response response = await api.get(ApiUrls.storeCategories);

      _categories.addAll(
        List.of(response.data['data'])
            .map((jsonData) => Category.fromMap(jsonData))
            .toList(),
      );

      return _categories;
    } catch (e) {
      throw Exception('Failed to fetch categories: $e');
    }
  }

  void clearCache() {
    _categories.clear();
  }
}
