import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/models/city.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';

class CityRepository extends Repository {
  List<City> cities = [];

  Future<List<City>> getAll({String? search}) async {
    if (cities.isNotEmpty && search!.isEmpty) {
      return cities;
    }

    Response response =
        await api.get(ApiUrls.cities, queryParameters: {'search': search});
    List list = response.data['data'];
    cities.addAll(City.fromJsonList(list));
    return City.fromJsonList(list);
  }
}
