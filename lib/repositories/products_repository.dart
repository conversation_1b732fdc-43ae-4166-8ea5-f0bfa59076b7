import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/store/models/product.dart';
import 'package:goldenprizma/domain/store/models/pagination.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/store_repository.dart';

class ProductsRepository extends StoreRepository {
  Future<PaginatedResponse<Product>> getProducts({
    int page = 1,
    String? search,
    int? brandId,
    int? categoryId,
    bool? isOutlet,
    bool? hasPromotion,
  }) async {
    try {
      Map<String, dynamic> queryParams = {'page': page};

      if (search != null && search.isNotEmpty) {
        queryParams['q'] = search;
      }

      if (brandId != null) {
        queryParams['brand_id'] = brandId;
      }

      if (categoryId != null) {
        queryParams['category_id'] = categoryId;
      }

      if (isOutlet != null) {
        queryParams['outlet'] = isOutlet;
      }

      if (hasPromotion != null) {
        queryParams['has_promotion'] = hasPromotion;
      }

      Response response = await api.get(
        ApiUrls.storeProducts,
        queryParameters: queryParams,
      );

      return PaginatedResponse.fromMap(
        response.data,
        (json) => Product.fromMap(json),
      );
    } catch (e) {
      throw Exception('Failed to fetch products: $e');
    }
  }

  Future<Product> getProductDetail({required int productId}) async {
    try {
      Response response = await api.get('${ApiUrls.storeProducts}/$productId');

      return Product.fromMap(response.data['data']);
    } catch (e) {
      throw Exception('Failed to fetch product detail: $e');
    }
  }
}
