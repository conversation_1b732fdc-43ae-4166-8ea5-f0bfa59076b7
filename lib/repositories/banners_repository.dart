import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/store/models/banner.dart' as model;
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/store_repository.dart';

class BannersRepository extends StoreRepository {
  final List<model.Banner> _banners = [];

  Future<List<model.Banner>> getBanners() async {
    if (_banners.isNotEmpty) {
      return _banners;
    }

    try {
      Response response = await api.get(ApiUrls.storeBanners);
      _banners.addAll(
        List.of(response.data['data'])
            .map((jsonData) => model.Banner.fromMap(jsonData))
            .toList(),
      );

      return _banners;
    } catch (e) {
      throw Exception('Failed to fetch banners: $e');
    }
  }

  void clearCache() {
    _banners.clear();
  }
}
