import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/support/models/reply.dart';
import 'package:goldenprizma/domain/support/models/ticket.dart';
import 'package:goldenprizma/domain/support/models/ticket_subject.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';

class SupportRepository extends Repository {
  List<TicketSubject> subjects = [];

  Future<List<Ticket>> getTickets({int page = 1}) async {
    Response response = await api.get(
      ApiUrls.tickets,
      queryParameters: {'page': page},
      options: Options(headers: {'auth': true}),
    );

    return List.of(response.data['data'])
        .map((jsonData) => Ticket.fromJson(jsonData))
        .toList();
  }

  Future<List<TicketSubject>> getSubjects() async {
    if (subjects.isNotEmpty) {
      return subjects;
    }

    Response response = await api.get(
      ApiUrls.ticketSubjects,
      options: Options(headers: {'auth': true}),
    );

    subjects.addAll(List.of(response.data['data'])
        .map((jsonData) => TicketSubject.fromJson(jsonData))
        .toList());

    return subjects;
  }

  Future<String> createTicket(
    Map<String, dynamic> mapData,
  ) async {
    Response response = await api.post(
      ApiUrls.tickets,
      data: mapData,
      options: Options(headers: {'auth': true}),
    );

    return response.data['message'];
  }

  Future<Ticket> getTicket({required int ticketId}) async {
    Response response = await api.get('${ApiUrls.tickets}/$ticketId');
    return Ticket.fromJson(response.data['data']);
  }

  Future<List<TicketReply>> getReplies({required int ticketId}) async {
    Response response = await api.get(
      '${ApiUrls.tickets}/$ticketId/replies',
      options: Options(
        headers: {'auth': true},
      ),
    );
    return List.of(response.data['data'])
        .map((jsonData) => TicketReply.fromJson(jsonData))
        .toList();
  }

  Future<Map<String, dynamic>> createReply(
      {required int ticketId, required String body}) async {
    Response response = await api.post(
      '${ApiUrls.tickets}/$ticketId/replies',
      data: {'body': body},
      options: Options(
        headers: {'auth': true},
      ),
    );
    return {
      'message': response.data['message'],
      'reply': TicketReply.fromJson(response.data['data']),
    };
  }
}
