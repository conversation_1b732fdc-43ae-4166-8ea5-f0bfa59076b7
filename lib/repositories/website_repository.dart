import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/websites/country_websites.dart';
import 'package:goldenprizma/domain/websites/models/website.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';

class WebsiteRepository extends Repository {
  Future<List<Website>> fetchAll() async {
    Response response = await api.get(ApiUrls.websites);
    return List.of(response.data)
        .map((json) => Website.fromJson(json))
        .toList();
  }

  Future<List<CountryWebsites>> fetchPopulars(
      {Map<String, dynamic>? queryParams}) async {
    Response response =
        await api.get(ApiUrls.popularWebsites, queryParameters: queryParams);
    List<CountryWebsites> list = [];
    var data = response.data['data'];
    data.forEach((key, value) {
      list.add(CountryWebsites(
          name: key,
          websites: List.of(value).map((e) => Website.fromJson(e)).toList()));
    });
    return list;
  }
}
