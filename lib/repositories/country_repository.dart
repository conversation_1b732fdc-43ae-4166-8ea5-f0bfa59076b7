import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/orders/models/country_model.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';

class CountryRepository extends Repository {
  final List<CountryModel> countries = [];

  Future<List<CountryModel>> getAll() async {
    if (countries.isNotEmpty) {
      return countries;
    }

    Response response = await api.get(ApiUrls.countries,
        options: Options(headers: {'auth': true}));

    countries.addAll(CountryModel.fromJsonList(response.data['data']));

    return countries
        .where((element) => element.name.toLowerCase() != 'iraq')
        .toList();
  }
}
