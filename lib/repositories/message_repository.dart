import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/chat/models/message.dart';
import 'package:goldenprizma/helpers/chat_urls.dart';
import 'package:goldenprizma/helpers/dio.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';

class MessageRepository extends Repository {
  Future<List<Message>> getMessages({
    required int conversationId,
    Map<String, dynamic>? queryParameters,
  }) async {
    Response response = await chatDio().get(
      ChatUrls.messages(
        inboxId: 'gHgywYWNz6nbrNp2GzWBvaYo',
        contactId: '2648e630-acfe-41d6-a7ed-4d04f6097d5b',
        conversationId: conversationId.toString(),
      ),
      queryParameters: queryParameters,
    );

    return List.of(response.data)
        .map((jsonData) => Message.fromJson(jsonData))
        .toList();
  }
}
