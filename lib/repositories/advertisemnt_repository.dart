import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/ads/models/advertisement.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';

class AdvertisementsRepository extends Repository {
  Future<List<Advertisement>> getAds(int page) async {
    await Future.delayed(const Duration(milliseconds: 1000));
    Response response =
        await api.get(ApiUrls.advertisements, queryParameters: {'page': page});

    return List.of(response.data['data'])
        .map((jsonData) => Advertisement.fromJson(jsonData))
        .toList();
  }
}
