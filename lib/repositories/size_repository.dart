import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/orders/models/size_model.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';

class SizeRepository extends Repository {
  final List<SizeModel> sizes = [];

  Future<List<SizeModel>> getAll({String filter = ''}) async {
    // if (sizes.isNotEmpty && filter.isEmpty) {
    //   return sizes;
    // }

    Response response = await api.get(
      ApiUrls.sizes,
      queryParameters: {'search': filter},
      options: Options(headers: {'auth': true}),
    );

    return SizeModel.fromJsonList(response.data['data']);

    // return sizes;si
  }
}
