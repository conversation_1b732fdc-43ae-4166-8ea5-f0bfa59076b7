import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/orders/models/filter_brand_model.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';

class FilterBrandRepository extends Repository {
  final List<FilterBrandModel> _brands = [];

  Future<List<FilterBrandModel>> getAll(
      {String filter = '', bool ignoreCache = false}) async {
    if (_brands.isNotEmpty && filter.isEmpty && ignoreCache == false) {
      return _brands;
    }

    Response response = await api.get(
      ApiUrls.filterBrands,
      queryParameters: {'search': filter},
      options: Options(headers: {'auth': true}),
    );

    _brands
      ..clear()
      ..addAll(FilterBrandModel.fromJsonList(response.data['data']));

    return _brands;
  }
}
