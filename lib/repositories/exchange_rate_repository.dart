import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/exchange_rates/models/exchange_rate.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';

class ExchangeRateRepostory extends Repository {
  Future<ExchangeRate?> getExchangeRate({String? currency}) async {
    if (currency == 'USD') {
      return null;
    }
    Response response = await api.get(
      ApiUrls.exchangeRate,
      queryParameters: {'currency': currency},
      options: Options(
        headers: {'auth': true},
      ),
    );
    return ExchangeRate.fromJson(response.data);
  }
}
