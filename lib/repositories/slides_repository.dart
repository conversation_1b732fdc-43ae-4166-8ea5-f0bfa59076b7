import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/ads/models/advertisement.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';

class SlidesRepository extends Repository {
  final List<Advertisement> _slides = [];

  Future<List<Advertisement>> getSlides() async {
    if (_slides.isNotEmpty) {
      return _slides;
    }

    Response response = await api.get(ApiUrls.slides);

    _slides.addAll(
      List.of(response.data['data'])
          .map((jsonData) => Advertisement.fromJson(jsonData))
          .toList(),
    );

    return _slides;
  }
}
