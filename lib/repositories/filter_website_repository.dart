import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/orders/models/filter_website_model.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';

class FilterWebsiteRepository extends Repository {
  final List<FilterWebsiteModel> _websites = [];

  Future<List<FilterWebsiteModel>> getAll(
      {String filter = '', bool ignoreCache = false}) async {
    if (_websites.isNotEmpty && filter.isEmpty && ignoreCache == true) {
      return _websites;
    }

    Response response = await api.get(
      ApiUrls.filterWebsites,
      queryParameters: {'search': filter},
      options: Options(headers: {'auth': true}),
    );

    _websites
      ..clear()
      ..addAll(FilterWebsiteModel.fromJsonList(response.data['data']));

    return _websites;
  }
}
