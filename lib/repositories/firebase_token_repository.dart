import 'package:dio/dio.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';

class FirebaseTokenRepository extends Repository {
  Future<void> updateTokenForCurrentDevice({String? token}) async {
    await api.put(
      ApiUrls.updateFcmToken,
      data: {'fcm_token': token},
      options: Options(
        headers: {'auth': true},
      ),
    );
  }
}
