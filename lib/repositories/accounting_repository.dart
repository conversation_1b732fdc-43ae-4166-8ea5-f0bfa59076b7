import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/accounting/models/account.dart';
import 'package:goldenprizma/domain/accounting/models/pending_deposit.dart';
import 'package:goldenprizma/domain/accounting/models/transaction.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';

class AccountingRepostory extends Repository {
  Future<List<Transaction>> getTransactions(
      {int page = 1, Map<String, dynamic>? queryParameters}) async {
    Map<String, dynamic> params = {'page': page};

    if (queryParameters != null) {
      params.addAll(queryParameters);
    }

    Response response = await api.get(
      ApiUrls.transactions,
      options: Options(headers: {'auth': true}),
      queryParameters: params,
    );
    return List.of(response.data['data'])
        .map((jsonData) => Transaction.fromJson(jsonData))
        .toList();
  }

  Future<Account> getAccount() async {
    Response response = await api.get(
      ApiUrls.account,
      options: Options(headers: {'auth': true}),
    );

    return Account.fromJson(response.data['data']);
  }

  Future<Map<String, dynamic>> createDeposit({
    required double amount,
    required String gateway,
  }) async {
    Response response = await api.post(
      '${ApiUrls.deposits}/$gateway/initiate',
      data: {
        'amount_usd': amount,
      },
      options: Options(headers: {'auth': true}),
    );

    return {
      'message': response.data['success'] == true
          ? 'Deposit request submitted successfully'
          : 'Deposit request failed',
      'payment_url': response.data['payment_url'],
      'success': response.data['success'],
    };
  }

  Future<List<PendingDeposit>> getPendingDeposits() async {
    Response response = await api.get(
      ApiUrls.pendingDeposits,
      options: Options(headers: {'auth': true}),
    );

    return List.of(response.data['data'])
        .map((jsonData) => PendingDeposit.fromJson(jsonData))
        .toList();
  }
}
