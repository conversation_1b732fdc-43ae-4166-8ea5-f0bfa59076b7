import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/chat/models/conversation.dart';
import 'package:goldenprizma/helpers/chat_urls.dart';
import 'package:goldenprizma/helpers/dio.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';

class ConversationRepostory extends Repository {
  Future<List<Conversation>> getConversations({
    int page = 1,
    Map<String, dynamic>? queryParameters,
  }) async {
    Map<String, dynamic> params = {'page': page};

    if (queryParameters != null) {
      params.addAll(queryParameters);
    }

    Response response = await chatDio().get(
      ChatUrls.conversations(
          inboxId: 'gHgywYWNz6nbrNp2GzWBvaYo',
          contactId: '2648e630-acfe-41d6-a7ed-4d04f6097d5b'),
      queryParameters: params,
    );

    return List.of(response.data)
        .map((jsonData) => Conversation.fromJson(jsonData))
        .toList();
  }
}
