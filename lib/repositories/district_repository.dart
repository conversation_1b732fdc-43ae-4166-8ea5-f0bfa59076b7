import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/models/district.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';

class DistrictRepository extends Repository {
  Future<List<District>> getAll(int? cityId) async {
    if (cityId == null) {
      return [];
    }
    Response response =
        await api.get(ApiUrls.districts, queryParameters: {"city_id": cityId});
    List list = response.data['data'];
    return District.fromJsonList(list);
  }
}
