import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/request_deliveries/models/request_delivery.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';

class RequestDeliveryRepository extends Repository {
  Future<List<RequestDelivery>> getAll({int page = 1}) async {
    Response response = await api.get(ApiUrls.requestDeliveries,
        queryParameters: {'page': page},
        options: Options(headers: {'auth': true}));

    return List.of(response.data['data'])
        .map((jsonData) => RequestDelivery.fromJson(jsonData))
        .toList();
  }

  Future<Map<String, dynamic>> createRequestDelivery(
      Map<String, dynamic> mapData) async {
    Response response = await api.post(ApiUrls.requestDeliveries,
        data: mapData, options: Options(headers: {'auth': true}));
    return {
      'message': response.data['message'],
      'delivery': RequestDelivery.fromJson(response.data['data']),
    };
  }
}
