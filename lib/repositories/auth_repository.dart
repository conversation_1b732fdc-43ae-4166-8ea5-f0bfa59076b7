import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/auth/models/user.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';
import 'package:goldenprizma/support/api_secret.dart';

class AuthRepository extends Repository {
  Future<Map<String, dynamic>> register(Map<String, dynamic> data) async {
    data.addAll({ApiSecret.secretKey: ApiSecret.secret});
    Response response = await api.post(ApiUrls.register, data: data);

    return {
      'message': response.data['message'],
      'api_token': response.data['api_token'],
    };
  }

  Future<User> tryGetUser({String? token}) async {
    Response response = await api.get(ApiUrls.profile,
        options: Options(headers: {'auth': true}));

    return User.fromJson(response.data['data']);
  }

  Future<String> updateProfile(Map<String, dynamic> data) async {
    Response response = await api.put(ApiUrls.profile,
        data: data, options: Options(headers: {'auth': true}));

    return response.data['message'] as String;
  }

  Future<User> getProfile() async {
    Response response = await api.get(ApiUrls.profile,
        options: Options(
          headers: {
            'auth': true,
          },
        ));
    return User.fromJson(response.data['data']);
  }

  Future<String> updateProfilePhoto(String imagePath) async {
    FormData formData = FormData.fromMap({'_method': 'PUT'});
    formData.files
        .add(MapEntry('image', MultipartFile.fromFileSync(imagePath)));

    Response response = await api.post(
      ApiUrls.updateProfilePhoto,
      data: formData,
      options: Options(
        headers: {
          'auth': true,
        },
      ),
    );
    return response.data['message'];
  }

  Future<String> login(
      {required String dialCode,
      required String username,
      required String password}) async {
    Response response = await api.post(ApiUrls.login, data: {
      'username': username,
      'password': password,
      'dial_code': dialCode
    });
    return response.data['api_token'];
  }

  Future<bool> sendOtp(
      {required String phoneNumber,
      required String countryCode,
      String scope = 'register'}) async {
    Response response = await api.post(ApiUrls.sendOtp, data: {
      "phone_number": phoneNumber,
      "country_code": countryCode,
      "scope": scope,
    });
    return response.data['success'];
  }

  Future<String> forgetPasswordOtpVerify(
      {required String phoneNumber,
      required String countryCode,
      required String code}) async {
    Response response = await api.post(ApiUrls.forgetPassword, data: {
      "phone_number": phoneNumber,
      "country_code": countryCode,
      "code": code
    });
    return response.data['token'];
  }

  Future<String> changePassword(
      {required String phoneNumber,
      required String countryCode,
      required String password,
      required String passwordConfirmation,
      required String token,
      required String code}) async {
    Response response = await api.patch(ApiUrls.updatePassword, data: {
      "phone_number": phoneNumber,
      "country_code": countryCode,
      "password": password,
      "password_confirmation": passwordConfirmation,
      "token": token,
      "code": code
    });
    return response.data['message'] ?? "";
  }

  Future<dynamic> changePasswordUsingOldPassword({
    required String oldPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    Response response = await api.patch(ApiUrls.changePassword,
        options: Options(
          headers: {'auth': true},
        ),
        data: {
          "old_password": oldPassword,
          "new_password": newPassword,
          "password_confirmation": confirmPassword,
        });
    return response.data;
  }

  Future<bool> verifyOtp(
      {required String phoneNumber,
      required String countryCode,
      required String code}) async {
    Response response = await api.post(ApiUrls.verifyOtp, data: {
      "phone_number": phoneNumber,
      "country_code": countryCode,
      "code": code
    });
    return response.data['success'];
  }

  Future<void> logout() async {
    await api.post(ApiUrls.logout,
        options: Options(
          headers: {'auth': true},
        ));
  }

  Future<String> deleteMyAccount() async {
    Response response = await api.post(ApiUrls.deleteRequest,
        options: Options(
          headers: {
            'auth': true,
          },
        ));
    return response.data['message'];
  }
}
