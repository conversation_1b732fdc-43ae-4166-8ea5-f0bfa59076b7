import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:goldenprizma/domain/url_transformation/models/transformation_rule.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';
import 'package:goldenprizma/services/url_transformation_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Repository for managing URL transformation rules
class UrlTransformationRepository extends Repository {
  static const String _cacheKey = 'url_transformation_rules';
  static const String _versionKey = 'url_transformation_rules_version';
  static const String _lastUpdatedKey = 'url_transformation_rules_last_updated';

  final UrlTransformationService _transformationService =
      UrlTransformationService();

  /// Fetch transformation rules from backend and cache them
  /// Always tries API first, then falls back to cache
  Future<TransformationRules> fetchTransformationRules(
      {bool useCacheFirst = false}) async {
    try {
      // Try to load from cache first only if explicitly requested
      if (useCacheFirst) {
        final cachedRules = await _loadFromCache();
        if (cachedRules != null) {
          _transformationService.loadRules(cachedRules);
          return cachedRules;
        }
      }

      // Always try to fetch from backend first
      debugPrint('Fetching URL transformation rules from backend...');
      final response = await api.get(ApiUrls.urlTransformations);

      final transformationRules = TransformationRules.fromJson(response.data);

      // Cache the rules
      await _saveToCache(transformationRules);

      // Load into service
      _transformationService.loadRules(transformationRules);

      debugPrint(
          'Successfully fetched and cached ${transformationRules.rules.length} transformation rules');
      return transformationRules;
    } catch (e) {
      debugPrint('Error fetching transformation rules from API: $e');

      // Try to load from cache as fallback
      final cachedRules = await _loadFromCache();
      if (cachedRules != null) {
        debugPrint('Using cached transformation rules as fallback');
        _transformationService.loadRules(cachedRules);
        return cachedRules;
      }

      // Return empty rules if everything fails
      debugPrint('No cached rules available, returning empty rules');
      const emptyRules = TransformationRules(rules: []);
      _transformationService.loadRules(emptyRules);
      return emptyRules;
    }
  }

  /// Initialize transformation rules (call this on app startup)
  Future<void> initializeTransformationRules() async {
    try {
      await fetchTransformationRules();
    } catch (e) {
      debugPrint('Failed to initialize transformation rules: $e');
    }
  }

  /// Check if cached rules need updating
  Future<bool> needsUpdate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastUpdated = prefs.getString(_lastUpdatedKey);

      if (lastUpdated == null) return true;

      final lastUpdatedDate = DateTime.parse(lastUpdated);
      final now = DateTime.now();

      // Update if cache is older than 24 hours
      return now.difference(lastUpdatedDate).inHours > 24;
    } catch (e) {
      debugPrint('Error checking update status: $e');
      return true;
    }
  }

  /// Refresh rules if needed
  Future<void> refreshIfNeeded() async {
    try {
      if (await needsUpdate()) {
        debugPrint('Transformation rules need update, refreshing...');
        await fetchTransformationRules(useCacheFirst: false);
      }
    } catch (e) {
      debugPrint('Error during refresh check: $e');
    }
  }

  /// Save transformation rules to local cache
  Future<void> _saveToCache(TransformationRules rules) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final rulesJson = jsonEncode(rules.toMap());

      await prefs.setString(_cacheKey, rulesJson);
      await prefs.setInt(_versionKey, rules.version);
      await prefs.setString(_lastUpdatedKey, DateTime.now().toIso8601String());

      debugPrint('Transformation rules cached successfully');
    } catch (e) {
      debugPrint('Error caching transformation rules: $e');
    }
  }

  /// Load transformation rules from local cache
  Future<TransformationRules?> _loadFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final rulesJson = prefs.getString(_cacheKey);

      if (rulesJson == null) {
        debugPrint('No cached transformation rules found');
        return null;
      }

      final rulesMap = jsonDecode(rulesJson) as Map<String, dynamic>;
      final rules = TransformationRules.fromMap(rulesMap);

      debugPrint(
          'Loaded ${rules.rules.length} transformation rules from cache (version: ${rules.version})');
      return rules;
    } catch (e) {
      debugPrint('Error loading cached transformation rules: $e');
      return null;
    }
  }

  /// Clear cached transformation rules
  Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheKey);
      await prefs.remove(_versionKey);
      await prefs.remove(_lastUpdatedKey);

      _transformationService.clearCache();
      debugPrint('Transformation rules cache cleared');
    } catch (e) {
      debugPrint('Error clearing transformation rules cache: $e');
    }
  }

  /// Get current cache info
  Future<Map<String, dynamic>> getCacheInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final version = prefs.getInt(_versionKey);
      final lastUpdated = prefs.getString(_lastUpdatedKey);
      final hasCache = prefs.containsKey(_cacheKey);

      return {
        'has_cache': hasCache,
        'version': version,
        'last_updated': lastUpdated,
        'needs_update': await needsUpdate(),
      };
    } catch (e) {
      debugPrint('Error getting cache info: $e');
      return {
        'has_cache': false,
        'version': null,
        'last_updated': null,
        'needs_update': true,
      };
    }
  }
}
