import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/store/models/brand.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/store_repository.dart';

class BrandsRepository extends StoreRepository {
  final List<Brand> _brands = [];

  Future<List<Brand>> getBrands() async {
    if (_brands.isNotEmpty) {
      return _brands;
    }

    try {
      Response response = await api.get(ApiUrls.storeBrands);

      _brands.addAll(
        List.of(response.data['data'])
            .map((jsonData) => Brand.fromMap(jsonData))
            .toList(),
      );

      return _brands;
    } catch (e) {
      throw Exception('Failed to fetch brands: $e');
    }
  }

  void clearCache() {
    _brands.clear();
  }
}
