import 'package:dio/dio.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:goldenprizma/helpers/logger.dart';

class SplashRepository extends Repository {
  String? image;
  late SharedPreferences _preferences;
  final String _key = "SPLASH_KEY";
  Future init() async {
    try {
      _preferences = await SharedPreferences.getInstance();
      image = _preferences.getString(_key);
      if (image == "" || image == null) {
        image = null;
      }
    } catch (e) {
      logger("erro in init splash data $e");
    }
  }

  Future _set(String? value) async {
    image = value;
    if (value != null) {
      _preferences.setString(_key, value);
    } else {
      _preferences.remove(_key);
    }
  }

  Future<void> getNewImage() async {
    try {
      Response response = await api.get(ApiUrls.splash,
          queryParameters: {}, options: Options(headers: {'auth': true}));
      _set(response.data['data']);
    } catch (e) {
      logger("erro in get splash data $e");
    }
  }
}
