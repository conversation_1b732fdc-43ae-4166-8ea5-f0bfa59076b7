import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/chat/models/chat_subject.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';

class ChatSubjectsRepository extends Repository {
  Future<List<ChatSubject>> getAll({String? type = 'general'}) async {
    Response response =
        await api.get(ApiUrls.chatSubejcts, queryParameters: {'type': type});
    return List.of(response.data['data'])
        .map((e) => ChatSubject.fromJson(e))
        .toList();
  }
}
