import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/support/models/contact.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';

class ContactRepository extends Repository {
  final Map<String, Contact> contacts = {};
  Future<Contact> getContactDetails({required int branchId}) async {
    String index = branchId.toString();
    if (contacts.containsKey(index) && contacts[index] != null) {
      return contacts[index] as Contact;
    }

    Response response = await api
        .get(ApiUrls.contact, queryParameters: {'branch_id': branchId});

    var contact = Contact.fromJson(response.data['data']);
    return contacts[index] = contact;
  }
}
