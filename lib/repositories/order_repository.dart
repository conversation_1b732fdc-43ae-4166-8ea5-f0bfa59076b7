import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/orders/models/order.dart';
import 'package:goldenprizma/domain/orders/models/scrap_response.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';
import 'package:goldenprizma/helpers/logger.dart';

class OrderRepository extends Repository {
  Future<Map<String, dynamic>> getOrders(Map<String, dynamic> query) async {
    logger("getOrders $query");
    Response response = await api.get(ApiUrls.orders,
        queryParameters: {
          ...query,
          'statuses[]': query['statuses'],
          'websiteIds[]': query['websiteIds'],
          'brandIds[]': query['brandIds'],
          'sizeIds[]': query['sizeIds'],
        },
        options: Options(headers: {'auth': true}));
    var orders = List.of(response.data['data'])
        .map((jsonData) => Order.fromJson(jsonData))
        .toList();
    return {
      'orders': orders,
      'total': response.data['meta']['total'],
    };
  }

  Future<int> getTotalOrders() async {
    Response response = await api.get(ApiUrls.orders,
        options: Options(headers: {'auth': true}));

    return response.data['meta']['total'].toInt();
  }

  Future<Order> getOrder({required int orderId}) async {
    Response response = await api.get(ApiUrls.orderDetail + orderId.toString(),
        options: Options(headers: {'auth': true}));
    return Order.fromJson(response.data['data']);
  }

  Future<ScrapResponse> scrap({required String link}) async {
    Response response = await api.get(
      ApiUrls.scrap,
      queryParameters: {'link': link},
      options: Options(headers: {'auth': true}),
    );
    if (response.data['success']) {
      return ScrapResponse.fromJson(response.data['data']);
    }
    return const ScrapResponse();
  }

  Future<Map<String, dynamic>> create(Map<String, dynamic> data) async {
    FormData formData = FormData.fromMap(data);
    formData.files
        .add(MapEntry('image', MultipartFile.fromFileSync(data['image_path'])));

    Response response = await api.post(
      ApiUrls.orders,
      data: formData,
      options: Options(
        headers: {
          'auth': true,
        },
      ),
    );
    return {
      'message': response.data['message'],
    };
  }

  Future<String> acceptOrRejectOrder(
      {required int orderId, required String action, String? reason}) async {
    Response response = await api.put(
      action == 'accept' ? ApiUrls.acceptOrder : ApiUrls.rejectOrder,
      data: {
        'order_id': orderId,
        'reason': reason,
      },
      options: Options(
        headers: {
          'auth': true,
        },
      ),
    );
    return response.data['message'];
  }

  Future<Map<String, dynamic>> reorder({required int orderId}) async {
    Response response = await api.post(
      ApiUrls.reorderOrder,
      data: {
        'order_id': orderId,
      },
      options: Options(
        headers: {
          'auth': true,
        },
      ),
    );
    return {
      'message': response.data['message'],
    };
  }
}
