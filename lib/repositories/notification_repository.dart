import 'package:dio/dio.dart';
import 'package:goldenprizma/domain/notifications/models/notification.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';
import 'package:goldenprizma/helpers/logger.dart';

class NotificationRepository extends Repository {
  Future<List<NotificationModel>> getNotifications({int page = 1}) async {
    Response response = await api.get(
      ApiUrls.notifications,
      queryParameters: {'page': page},
      options: Options(headers: {
        'auth': true,
      }),
    );
    try {
      return NotificationModel.fromJsonList(List.of(response.data['data']));
    } catch (e) {
      logger(e.toString());
    }
    return NotificationModel.fromJsonList(List.of(response.data['data']));
  }

  Future<NotificationModel> markAsRead({required String id}) async {
    Response response = await api.put(
      ApiUrls.notifications,
      data: {'id': id},
      options: Options(headers: {
        'auth': true,
      }),
    );
    return NotificationModel.fromJson(response.data['data']);
  }

  Future<List<NotificationModel>> markAllAsRead() async {
    Response response = await api.put(
      ApiUrls.notificationMarkAllAsRead,
      options: Options(headers: {
        'auth': true,
      }),
    );

    return List.of(response.data['data'])
        .map((jsonData) => NotificationModel.fromJson(jsonData))
        .toList();
  }
}
