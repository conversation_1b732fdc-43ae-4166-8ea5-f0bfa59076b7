import 'package:flutter/services.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class TokenRepository {
  final FlutterSecureStorage storage = const FlutterSecureStorage();
  static const apiKey = 'api_token';

  Future<String?> get() async {
    try {
      return await storage.read(key: api<PERSON><PERSON>);
    } on PlatformException catch (e) {
      if (e.code == 'read') {
        await delete();
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<void> set(String token) async {
    if (token.isNotEmpty) {
      try {
        await storage.write(key: api<PERSON>ey, value: token);
      } catch (e) {
        // ignore
      }
    }
  }

  Future<void> delete() async {
    try {
      await storage.delete(key: api<PERSON><PERSON>);
    } catch (e) {
      // ignore
    }
  }
}
