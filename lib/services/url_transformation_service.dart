import 'package:flutter/foundation.dart';
import 'package:goldenprizma/domain/url_transformation/models/transformation_rule.dart';

/// Service for transforming URLs based on dynamic rules
class UrlTransformationService {
  static final UrlTransformationService _instance =
      UrlTransformationService._internal();
  factory UrlTransformationService() => _instance;
  UrlTransformationService._internal();

  TransformationRules? _cachedRules;

  /// Transform a URL using the loaded transformation rules
  String transformUrl(String originalUrl, {TransformationRules? rules}) {
    try {
      final transformationRules = rules ?? _cachedRules;
      if (transformationRules == null || transformationRules.rules.isEmpty) {
        debugPrint('No transformation rules available, returning original URL');
        return originalUrl;
      }

      debugPrint(
          'Using transformation rules with ${transformationRules.rules.length} rules');
      for (final rule in transformationRules.rules) {
        debugPrint(
            '  Available rule: ${rule.domain} - ${rule.sourcePattern} -> ${rule.targetPattern}');
      }

      final uri = Uri.parse(originalUrl);

      // Simple pattern matching against the original URL
      final matchingRules = _findMatchingRules(uri, transformationRules.rules);

      debugPrint(
          'Found ${matchingRules.length} matching rules for: $originalUrl');
      for (final rule in matchingRules) {
        debugPrint(
            '  Rule: ${rule.sourcePattern} -> ${rule.targetPattern} (priority: ${rule.priority})');
      }

      if (matchingRules.isEmpty) {
        debugPrint('No matching transformation rules for: $originalUrl');
        return originalUrl;
      }

      // Use the highest priority rule
      final rule = matchingRules.first;
      final transformedUrl = _applyTransformation(uri, rule);

      debugPrint('Applying transformation: $originalUrl -> $transformedUrl');

      debugPrint('URL transformed: $originalUrl -> $transformedUrl');
      return transformedUrl;
    } catch (e) {
      debugPrint('Error transforming URL: $e');
      return originalUrl; // Fallback to original URL on any error
    }
  }

  /// Find rules that match the given URI
  List<TransformationRule> _findMatchingRules(
      Uri uri, List<TransformationRule> rules) {
    final matchingRules = <TransformationRule>[];

    for (final rule in rules) {
      if (_matchesDomain(uri.host, rule.domain) &&
          _matchesPattern(uri, rule.sourcePattern)) {
        matchingRules.add(rule);
      }
    }

    // Sort by priority (highest first)
    matchingRules.sort((a, b) => b.priority.compareTo(a.priority));
    return matchingRules;
  }

  /// Check if the host matches the domain pattern
  bool _matchesDomain(String host, String domainPattern) {
    if (domainPattern.startsWith('*.')) {
      final domain = domainPattern.substring(2);
      return host.endsWith(domain);
    }
    return host.contains(domainPattern);
  }

  /// Check if the URI matches the source pattern
  bool _matchesPattern(Uri uri, String pattern) {
    final fullUrl = uri.toString();
    final path = uri.path;
    final query = uri.query;

    // Handle different pattern types
    if (pattern.contains('*')) {
      return _matchesWildcardPattern(fullUrl, pattern) ||
          _matchesWildcardPattern(path, pattern) ||
          (query.isNotEmpty && _matchesWildcardPattern(query, pattern));
    }

    return fullUrl.contains(pattern) ||
        path.contains(pattern) ||
        query.contains(pattern);
  }

  /// Match wildcard patterns (simple implementation)
  bool _matchesWildcardPattern(String text, String pattern) {
    final regexPattern = pattern
        .replaceAll('*', '.*')
        .replaceAll('?', '.')
        .replaceAll('+', r'\+')
        .replaceAll('(', r'\(')
        .replaceAll(')', r'\)')
        .replaceAll('[', r'\[')
        .replaceAll(']', r'\]')
        .replaceAll('{', r'\{')
        .replaceAll('}', r'\}');

    try {
      final regex = RegExp(regexPattern, caseSensitive: false);
      return regex.hasMatch(text);
    } catch (e) {
      debugPrint('Invalid regex pattern: $pattern, error: $e');
      return false;
    }
  }

  /// Apply the transformation rule to the URI
  String _applyTransformation(Uri uri, TransformationRule rule) {
    String originalUrl = uri.toString();

    // Parse the source pattern to understand what to replace
    return _applyPatternTransformation(
        originalUrl, rule.sourcePattern, rule.targetPattern);
  }

  /// Apply pattern-based transformation using simple string replacement
  String _applyPatternTransformation(
      String originalUrl, String sourcePattern, String targetPattern) {
    final parts = sourcePattern.split('*');

    // Handle single-part patterns like "*/share/*"
    if (parts.length == 3 && parts[0].isEmpty && parts[2].isEmpty) {
      final partToReplace = parts[1]; // "/share/"
      String replacement = targetPattern;

      // Preserve trailing slash if needed
      if (partToReplace.endsWith('/') && !replacement.endsWith('/')) {
        replacement = '$replacement/';
      }

      return originalUrl.replaceAll(partToReplace, replacement);
    }

    // Handle multi-part patterns like "*/?go=https%3A//www.zara.com/share/*"
    if (parts.length == 3) {
      final middlePart = parts[1]; // "/?go=https%3A//www.zara.com/share/"

      if (originalUrl.contains(middlePart)) {
        // Split the URL at the middle part
        final urlParts = originalUrl.split(middlePart);
        if (urlParts.length == 2) {
          // Reconstruct: before + targetPattern + after
          return urlParts[0] + targetPattern + urlParts[1];
        }
      }
    }

    return originalUrl;
  }

  /// Load transformation rules (to be called by repository)
  void loadRules(TransformationRules rules) {
    _cachedRules = rules;
    debugPrint(
        'Loaded ${rules.rules.length} transformation rules (version: ${rules.version})');
  }

  /// Get currently loaded rules
  TransformationRules? get cachedRules => _cachedRules;

  /// Clear cached rules
  void clearCache() {
    _cachedRules = null;
    debugPrint('Transformation rules cache cleared');
  }

  /// Force reload rules from backend
  void forceReload() {
    clearCache();
    debugPrint('Forced cache clear - rules will be reloaded on next transform');
  }
}
