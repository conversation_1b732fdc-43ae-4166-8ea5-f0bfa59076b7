import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/ads/models/advertisement.dart';
import 'package:goldenprizma/domain/home/<USER>/slide.dart';
import 'package:goldenprizma/domain/store/cubit/store_filter_cubit.dart';
import 'package:goldenprizma/domain/store/models/brand.dart';
import 'package:goldenprizma/presentation/views/screens/home_screen.dart';
import 'package:goldenprizma/presentation/views/screens/website_launcher.dart';
import 'package:goldenprizma/services/url_transformation_service.dart';

/// Service to handle different advertisement app actions
class AdvertisementActionService {
  static final AdvertisementActionService _instance =
      AdvertisementActionService._internal();
  factory AdvertisementActionService() => _instance;
  AdvertisementActionService._internal();

  final UrlTransformationService _urlTransformationService =
      UrlTransformationService();

  /// Handle advertisement tap based on app_action
  Future<void> handleAdvertisementTap(
    BuildContext context,
    Advertisement advertisement,
  ) async {
    if (!context.mounted) return;

    try {
      switch (advertisement.appAction) {
        case 'external_url':
          await _handleExternalUrl(context, advertisement);
          break;
        case 'page':
          await _handlePageAction(context, advertisement);
          break;
        case 'brand_store':
          await _handleBrandStoreAction(context, advertisement);
          break;
        default:
          // Fallback to original URL behavior for unknown actions
          if (context.mounted) {
            await _handleFallbackUrl(context, advertisement);
          }
          break;
      }
    } catch (e) {
      debugPrint('Error handling advertisement action: $e');
      // Fallback to original URL behavior on error
      if (context.mounted) {
        await _handleFallbackUrl(context, advertisement);
      }
    }
  }

  /// Handle slide tap (for home slides that don't have app actions yet)
  Future<void> handleSlideTap(
    BuildContext context,
    Slide slide,
  ) async {
    if (!context.mounted) return;

    final url = slide.url;
    if (url != null && url.isNotEmpty) {
      // Apply URL transformation if needed
      final transformedUrl = _urlTransformationService.transformUrl(url);

      if (context.mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => WebsiteLauncher(
              url: transformedUrl,
              websiteName: '',
              minimalDesign: true,
            ),
          ),
        );
      }
    }
  }

  /// Handle external_url action
  Future<void> _handleExternalUrl(
    BuildContext context,
    Advertisement advertisement,
  ) async {
    if (!context.mounted) return;

    final url = advertisement.externalUrl ?? advertisement.url;
    if (url.isNotEmpty) {
      // Apply URL transformation if needed
      final transformedUrl = _urlTransformationService.transformUrl(url);

      if (context.mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => WebsiteLauncher(
              url: transformedUrl,
              websiteName: '',
              minimalDesign: true,
            ),
          ),
        );
      }
    }
  }

  /// Handle page action
  Future<void> _handlePageAction(
    BuildContext context,
    Advertisement advertisement,
  ) async {
    if (!context.mounted) return;

    final url = advertisement.url;
    if (url.isNotEmpty) {
      // Apply URL transformation if needed
      final transformedUrl = _urlTransformationService.transformUrl(url);

      if (context.mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => WebsiteLauncher(
              url: transformedUrl,
              websiteName: '',
              minimalDesign: true,
            ),
          ),
        );
      }
    }
  }

  /// Handle brand_store action
  Future<void> _handleBrandStoreAction(
    BuildContext context,
    Advertisement advertisement,
  ) async {
    if (!context.mounted) return;

    final brandId = advertisement.brandId;
    if (brandId != null) {
      // Create a brand object with the ID for filtering
      final brand = Brand(
        id: brandId,
        name: advertisement.note ??
            'Brand $brandId', // Use note as brand name if available
        isTop: false,
        sortOrder: 0,
      );

      // Set the brand filter before navigation
      try {
        final cubit = context.read<StoreFilterCubit>();
        cubit.selectBrand(brandId, brand);

        // Navigate to store tab (index 1) instead of pushing a new route
        // This preserves the bottom navigation and app bar
        if (context.mounted) {
          // Try to find the HomeScreen and switch to store tab
          _navigateToStoreTab(context);
        }
      } catch (e) {
        debugPrint('Error setting brand filter: $e');
        // Fallback to URL if cubit is not available
        if (context.mounted) {
          await _handleFallbackUrl(context, advertisement);
        }
      }
    } else {
      // Fallback to URL if brand ID is not available
      if (context.mounted) {
        await _handleFallbackUrl(context, advertisement);
      }
    }
  }

  /// Navigate to store tab in the main navigation
  void _navigateToStoreTab(BuildContext context) {
    // Pop to the root route (HomeScreen) if we're not already there
    Navigator.of(context).popUntil((route) => route.isFirst);

    // Navigate to HomeScreen with store tab selected (index 1)
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => const HomeScreen(initialPage: 1),
      ),
    );
  }

  /// Fallback to original URL behavior
  Future<void> _handleFallbackUrl(
    BuildContext context,
    Advertisement advertisement,
  ) async {
    if (!context.mounted) return;

    final url = advertisement.url;
    if (url.isNotEmpty) {
      // Apply URL transformation if needed
      final transformedUrl = _urlTransformationService.transformUrl(url);

      if (context.mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => WebsiteLauncher(
              url: transformedUrl,
              websiteName: '',
              minimalDesign: true,
            ),
          ),
        );
      }
    }
  }

  /// Get action description for debugging/logging
  String getActionDescription(Advertisement advertisement) {
    switch (advertisement.appAction) {
      case 'external_url':
        return 'External URL: ${advertisement.externalUrl ?? advertisement.url}';
      case 'page':
        return 'Page: ${advertisement.pageSlug ?? advertisement.url}';
      case 'brand_store':
        return 'Brand Store: Brand ID ${advertisement.brandId} (${advertisement.note ?? 'Unknown'})';
      default:
        return 'Unknown action: ${advertisement.appAction} - Fallback to URL: ${advertisement.url}';
    }
  }
}
