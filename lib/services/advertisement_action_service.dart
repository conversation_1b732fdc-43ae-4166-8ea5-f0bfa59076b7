import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/ads/models/advertisement.dart';
import 'package:goldenprizma/domain/home/<USER>/slide.dart';
import 'package:goldenprizma/domain/store/cubit/store_filter_cubit.dart';
import 'package:goldenprizma/domain/store/models/brand.dart';
import 'package:goldenprizma/presentation/views/screens/home_screen.dart';
import 'package:goldenprizma/presentation/views/screens/website_launcher.dart';
import 'package:goldenprizma/services/url_transformation_service.dart';

class _ActionData {
  final String appAction;
  final String? url;
  final String? actionUrl;
  final String? appActionData;
  final String? note;
  final int? brandId;
  final String? externalUrl;
  final String? pageSlug;

  const _ActionData({
    required this.appAction,
    this.url,
    this.actionUrl,
    this.appActionData,
    this.note,
    this.brandId,
    this.externalUrl,
    this.pageSlug,
  });

  factory _ActionData.fromAdvertisement(Advertisement advertisement) {
    return _ActionData(
      appAction: advertisement.appAction,
      url: advertisement.url,
      actionUrl: advertisement.actionUrl,
      appActionData: advertisement.appActionData,
      note: advertisement.note,
      brandId: advertisement.brandId,
      externalUrl: advertisement.externalUrl,
      pageSlug: advertisement.pageSlug,
    );
  }

  factory _ActionData.fromSlide(Slide slide) {
    return _ActionData(
      appAction: slide.appAction,
      url: slide.url,
      actionUrl: slide.actionUrl,
      appActionData: slide.appActionData,
      note: null, // Slide doesn't have note field
      brandId: slide.brandId,
      externalUrl: slide.externalUrl,
      pageSlug: slide.pageSlug,
    );
  }
}

/// Service to handle different advertisement app actions
class AdvertisementActionService {
  static final AdvertisementActionService _instance =
      AdvertisementActionService._internal();
  factory AdvertisementActionService() => _instance;
  AdvertisementActionService._internal();

  final UrlTransformationService _urlTransformationService =
      UrlTransformationService();

  /// Handle advertisement tap based on app_action
  Future<void> handleAdvertisementTap(
    BuildContext context,
    Advertisement advertisement,
  ) async {
    if (!context.mounted) return;

    // Convert advertisement to action data and use unified handlers
    final actionData = _ActionData.fromAdvertisement(advertisement);

    try {
      switch (actionData.appAction) {
        case 'external_url':
          await _handleExternalUrlAction(context, actionData);
          break;
        case 'page':
          await _handlePageActionInternal(context, actionData);
          break;
        case 'brand_store':
          await _handleBrandStoreActionInternal(context, actionData);
          break;
        default:
          // Fallback to original URL behavior for unknown actions
          if (context.mounted) {
            await _handleFallbackUrlAction(context, actionData);
          }
          break;
      }
    } catch (e) {
      debugPrint('Error handling advertisement action: $e');
      // Fallback to original URL behavior on error
      if (context.mounted) {
        await _handleFallbackUrlAction(context, actionData);
      }
    }
  }

  /// Handle slide tap with app action support
  Future<void> handleSlideTap(
    BuildContext context,
    Slide slide,
  ) async {
    if (!context.mounted) return;

    debugPrint('Handling slide action: $slide}');

    // Convert slide to action data and use existing handlers
    final actionData = _ActionData.fromSlide(slide);

    try {
      switch (actionData.appAction) {
        case 'external_url':
          await _handleExternalUrlAction(context, actionData);
          break;
        case 'page':
          await _handlePageActionInternal(context, actionData);
          break;
        case 'brand_store':
          await _handleBrandStoreActionInternal(context, actionData);
          break;
        default:
          // Fallback to original URL behavior for unknown actions
          if (context.mounted) {
            await _handleFallbackUrlAction(context, actionData);
          }
          break;
      }
    } catch (e) {
      debugPrint('Error handling slide action: $e');
      // Fallback to original URL behavior on error
      if (context.mounted) {
        await _handleFallbackUrlAction(context, actionData);
      }
    }
  }

  /// Handle external_url action (unified for both Advertisement and Slide)
  Future<void> _handleExternalUrlAction(
    BuildContext context,
    _ActionData actionData,
  ) async {
    if (!context.mounted) return;

    final url = actionData.externalUrl ?? actionData.url;
    if (url != null && url.isNotEmpty) {
      // Apply URL transformation if needed
      final transformedUrl = _urlTransformationService.transformUrl(url);

      if (context.mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => WebsiteLauncher(
              url: transformedUrl,
              websiteName: '',
              minimalDesign: true,
            ),
          ),
        );
      }
    }
  }

  /// Handle page action (unified for both Advertisement and Slide)
  Future<void> _handlePageActionInternal(
    BuildContext context,
    _ActionData actionData,
  ) async {
    if (!context.mounted) return;

    final url = actionData.url;
    if (url != null && url.isNotEmpty) {
      // Apply URL transformation if needed
      final transformedUrl = _urlTransformationService.transformUrl(url);

      if (context.mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => WebsiteLauncher(
              url: transformedUrl,
              websiteName: '',
              minimalDesign: true,
            ),
          ),
        );
      }
    }
  }

  /// Handle brand_store action (unified for both Advertisement and Slide)
  Future<void> _handleBrandStoreActionInternal(
    BuildContext context,
    _ActionData actionData,
  ) async {
    if (!context.mounted) return;

    final brandId = actionData.brandId;
    if (brandId != null) {
      // Create a brand object with the ID for filtering
      final brand = Brand(
        id: brandId,
        name: actionData.note ??
            'Brand $brandId', // Use note as brand name if available
        isTop: false,
        sortOrder: 0,
      );

      // Set the brand filter before navigation
      try {
        final cubit = context.read<StoreFilterCubit>();
        cubit.selectBrand(brandId, brand);

        // Navigate to store tab (index 1) instead of pushing a new route
        // This preserves the bottom navigation and app bar
        if (context.mounted) {
          // Try to find the HomeScreen and switch to store tab
          _navigateToStoreTab(context);
        }
      } catch (e) {
        debugPrint('Error setting brand filter: $e');
        // Fallback to URL if cubit is not available
        if (context.mounted) {
          await _handleFallbackUrlAction(context, actionData);
        }
      }
    } else {
      // Fallback to URL if brand ID is not available
      if (context.mounted) {
        await _handleFallbackUrlAction(context, actionData);
      }
    }
  }

  /// Navigate to store tab in the main navigation
  void _navigateToStoreTab(BuildContext context) {
    // Pop to the root route (HomeScreen) if we're not already there
    Navigator.of(context).popUntil((route) => route.isFirst);

    // Navigate to HomeScreen with store tab selected (index 1)
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => const HomeScreen(initialPage: 1),
      ),
    );
  }

  /// Fallback to original URL behavior (unified for both Advertisement and Slide)
  Future<void> _handleFallbackUrlAction(
    BuildContext context,
    _ActionData actionData,
  ) async {
    if (!context.mounted) return;

    final url = actionData.url;
    if (url != null && url.isNotEmpty) {
      // Apply URL transformation if needed
      final transformedUrl = _urlTransformationService.transformUrl(url);

      if (context.mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => WebsiteLauncher(
              url: transformedUrl,
              websiteName: '',
              minimalDesign: true,
            ),
          ),
        );
      }
    }
  }

  /// Get action description for debugging/logging
  String getActionDescription(Advertisement advertisement) {
    switch (advertisement.appAction) {
      case 'external_url':
        return 'External URL: ${advertisement.externalUrl ?? advertisement.url}';
      case 'page':
        return 'Page: ${advertisement.pageSlug ?? advertisement.url}';
      case 'brand_store':
        return 'Brand Store: Brand ID ${advertisement.brandId} (${advertisement.note ?? 'Unknown'})';
      default:
        return 'Unknown action: ${advertisement.appAction} - Fallback to URL: ${advertisement.url}';
    }
  }

  /// Get slide action description for debugging/logging
  String getSlideActionDescription(Slide slide) {
    switch (slide.appAction) {
      case 'external_url':
        return 'External URL: ${slide.externalUrl ?? slide.url}';
      case 'page':
        return 'Page: ${slide.pageSlug ?? slide.url}';
      case 'brand_store':
        return 'Brand Store: Brand ID ${slide.brandId}';
      default:
        return 'Unknown action: ${slide.appAction} - Fallback to URL: ${slide.url}';
    }
  }
}
