double cleanPrice(String input) {
  // Map of Arabic to English numerals
  const arabicToEnglishNumbers = {
    '٠': '0',
    '١': '1',
    '٢': '2',
    '٣': '3',
    '٤': '4',
    '٥': '5',
    '٦': '6',
    '٧': '7',
    '٨': '8',
    '٩': '9'
  };

  // Replace Arabic numerics with English numerics
  String converted = input.split('').map((char) {
    return arabicToEnglishNumbers[char] ?? char;
  }).join('');

  // Remove any non-numeric characters, including currency symbols
  String cleaned = converted.replaceAll(RegExp(r'[^0-9.]'), '');

  return double.tryParse(cleaned) ?? 0.0;
}
