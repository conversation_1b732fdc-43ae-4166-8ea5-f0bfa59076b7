import 'package:dio/dio.dart';
import 'package:goldenprizma/env.dart';
import 'package:goldenprizma/helpers/app_settings.dart';
import 'package:goldenprizma/main.dart';
import 'package:goldenprizma/repositories/token_repository.dart';

Dio dio() {
  return Dio(
    BaseOptions(
      baseUrl: Env.apiUrl,
      responseType: ResponseType.json,
      receiveDataWhenStatusError: true,
    ),
  )..interceptors.add(InterceptorsWrapper(
      onRequest: (RequestOptions options, RequestInterceptorHandler handler) =>
          requestInterceptor(options, handler),
    ));
}

void requestInterceptor(
    RequestOptions options, RequestInterceptorHandler handler) async {
  final TokenRepository tokenRepository = getIt.get<TokenRepository>();

  if (options.headers.containsKey('auth')) {
    String? token = await tokenRepository.get();
    options.headers.addAll({'Authorization': 'Bearer $token'});
  }

  // Get current locale properly
  String? locale = await AppSettings.getLocale();

  options.headers.addAll({
    'Accept-Language': locale ?? 'en',
    'Accept': 'application/json',
  });
  handler.next(options);
}

Dio chatDio() {
  return Dio(
    BaseOptions(
        baseUrl: Env.apiUrl,
        responseType: ResponseType.json,
        receiveDataWhenStatusError: true,
        headers: {
          'api_access_token': Env.chatApiAccessKey,
          'Content-type': 'application/json',
          'Accept': 'application/json',
        }),
  );
}

Dio storeDio() {
  return Dio(
    BaseOptions(
      baseUrl: Env.storeApiUrl,
      responseType: ResponseType.json,
      receiveDataWhenStatusError: true,
    ),
  )..interceptors.add(InterceptorsWrapper(
      onRequest: (RequestOptions options, RequestInterceptorHandler handler) =>
          requestInterceptor(options, handler),
    ));
}
