import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';

SnackBar errorSnackBar(String message) {
  return SnackBar(
    backgroundColor: Colors.red,
    behavior: SnackBarBehavior.floating,
    content: Row(
      children: [
        const Icon(
          Ionicons.warning_outline,
          color: Colors.white,
        ),
        const SizedBox(width: 12),
        Flexible(
          child: Text(
            message,
            style: const TextStyle(color: Colors.white),
          ),
        ),
      ],
    ),
  );
}

SnackBar successSnackBar(String message) {
  return SnackBar(
    backgroundColor: Colors.green,
    behavior: SnackBarBehavior.floating,
    content: Row(
      children: [
        const Icon(
          Ionicons.checkmark_circle_outline,
          color: Colors.white,
        ),
        const SizedBox(width: 12),
        Flexible(
          child: Text(
            message,
            style: const TextStyle(color: Colors.white),
          ),
        ),
      ],
    ),
  );
}

ScaffoldMessengerState showErrorSnackBar(
    {required BuildContext context, required String message}) {
  return ScaffoldMessenger.of(context)
    ..hideCurrentSnackBar()
    ..showSnackBar(errorSnackBar(message));
}

ScaffoldMessengerState showSuccessSnackBar(
    {required BuildContext context, required String message}) {
  return ScaffoldMessenger.of(context)
    ..hideCurrentSnackBar()
    ..showSnackBar(successSnackBar(message));
}
