import 'package:flutter/material.dart';
import 'package:goldenprizma/helpers/clean_price.dart';

typedef CurrencyCode = String;

// Define currency symbol to ISO code mapping
const Map<String, CurrencyCode> currencyMap = {
  "€": "EUR",
  "£": "GBP",
  "OM": "OMR",
  "AED": "AED",
  "SR": "SAR",
  "AU\$": "AUD",
  "DH": "AED",
  "\$": "USD",
  "kr": "SEK",
  "KWD": "KWD",
  "JOD": "JOD",
  "BD": "BHD",
  "QR": "QAR",
};

// Define conversion rates to AED
const Map<CurrencyCode, double> conversionRatesToAED = {
  "EUR": 3.965,
  "GBP": 4.75,
  "OMR": 9.54,
  "AED": 1.0,
  "SAR": 0.978,
  "AUD": 2.45,
  "USD": 3.674,
  "SEK": 0.342,
  "KWD": 11.98,
  "JOD": 5.18,
  "BHD": 9.74,
  "QAR": 1.01,
};

// Regular expression to match currency and amount pattern
final RegExp currencyRegex = RegExp(
    r'^\s*(€|£|OM|AED|SR|AU\$|DH|\$|kr|KWD|JOD|BD|QR)?\s*([\d,]+(\.\d+)?)\s*(€|£|OM|AED|SR|AU\$|DH|\$|kr|KWD|JOD|BD|QR)?\s*$',
    caseSensitive: false);

double convertToAED(String amountStr) {
  // Remove leading and trailing whitespaces and quotes
  amountStr = amountStr.trim().replaceAll("\"", "").replaceAll("'", "");

  // Attempt to match the input string to the regex pattern
  final match = currencyRegex.firstMatch(amountStr);

  debugPrint("amountStr: $amountStr");

  final cleanAmount = cleanPrice(amountStr);

  if (match == null) {
    return cleanAmount; // Return 0 if the input does not match
  }

  // Extract currency symbol and amount
  final currencySymbol = match.group(1) ?? match.group(4) ?? "AED";

  // Get the ISO currency code from the symbol
  final currencyCode = currencyMap[currencySymbol];
  if (currencyCode == null) {
    return cleanAmount; // Return 0 if the currency code is unsupported
  }

  // Get the conversion rate from the map
  final rateToAED = conversionRatesToAED[currencyCode] ?? 1.0;

  // Calculate and return the converted amount in AED
  return cleanAmount * rateToAED;
}
