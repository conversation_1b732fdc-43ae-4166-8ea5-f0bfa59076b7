import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:goldenprizma/helpers/user_agent_manager.dart';
import 'package:webview_flutter/webview_flutter.dart';

// ignore: depend_on_referenced_packages
import 'package:webview_flutter_android/webview_flutter_android.dart';
// ignore: depend_on_referenced_packages
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

/// Helper class for creating and configuring WebView instances with crash prevention
class WebViewHelper {
  /// Creates a WebViewController with optimized settings for stability
  static WebViewController createController({
    required String url,
    NavigationDelegate? navigationDelegate,
    bool enableJavaScript = true,
    bool enableUserAgent = true,
    Map<String, JavaScriptChannelParams>? javascriptChannels,
  }) {
    // Create platform-specific parameters
    late final PlatformWebViewControllerCreationParams params;

    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = AndroidWebViewControllerCreationParams();
    }

    final controller = WebViewController.fromPlatformCreationParams(params);

    // Configure Android-specific settings for stability
    if (controller.platform is AndroidWebViewController) {
      AndroidWebViewController.enableDebugging(kDebugMode);
      (controller.platform as AndroidWebViewController)
          .setMediaPlaybackRequiresUserGesture(false);
    }

    // Set user agent if enabled
    if (enableUserAgent) {
      final baseUserAgent = UserAgentManager().getUserAgentForWebsite(url);
      final userAgent =
          UserAgentManager().getRandomizedUserAgent(baseUserAgent);
      controller.setUserAgent(userAgent);
      debugPrint('WebView using user agent: $userAgent');
    }

    // Configure JavaScript
    if (enableJavaScript) {
      controller.setJavaScriptMode(JavaScriptMode.unrestricted);
    } else {
      controller.setJavaScriptMode(JavaScriptMode.disabled);
    }

    // Set navigation delegate with error handling
    controller.setNavigationDelegate(
      navigationDelegate ?? _createDefaultNavigationDelegate(),
    );

    // Add JavaScript channels if provided
    if (javascriptChannels != null) {
      for (final entry in javascriptChannels.entries) {
        controller.addJavaScriptChannel(entry.key,
            onMessageReceived: entry.value.onMessageReceived);
      }
    }

    return controller;
  }

  /// Creates a default navigation delegate with comprehensive error handling
  static NavigationDelegate _createDefaultNavigationDelegate() {
    return NavigationDelegate(
      onProgress: (int progress) {
        debugPrint('WebView loading progress: $progress%');
      },
      onPageStarted: (String url) {
        debugPrint('WebView started loading: $url');
      },
      onPageFinished: (String url) {
        debugPrint('WebView finished loading: $url');
      },
      onWebResourceError: (WebResourceError error) {
        debugPrint('''
          WebView resource error:
          code: ${error.errorCode}
          description: ${error.description}
          errorType: ${error.errorType}
          isForMainFrame: ${error.isForMainFrame}
        ''');
      },
      onNavigationRequest: (NavigationRequest request) {
        debugPrint('WebView navigation request: ${request.url}');
        return NavigationDecision.navigate;
      },
      onHttpError: (HttpResponseError error) {
        debugPrint('WebView HTTP error: ${error.response?.statusCode}');
      },
      onUrlChange: (UrlChange change) {
        debugPrint('WebView URL changed: ${change.url}');
      },
    );
  }

  /// Safely executes JavaScript with timeout and error handling
  static Future<T?> safeExecuteJavaScript<T>(
    WebViewController controller,
    String script, {
    Duration timeout = const Duration(seconds: 10),
    T? defaultValue,
  }) async {
    try {
      final result = await controller
          .runJavaScriptReturningResult(script)
          .timeout(timeout);
      return result as T?;
    } catch (e) {
      debugPrint('JavaScript execution error: $e');
      return defaultValue;
    }
  }

  /// Safely loads a URL with timeout and error handling
  static Future<bool> safeLoadUrl(
    WebViewController controller,
    String url, {
    Duration timeout = const Duration(seconds: 30),
  }) async {
    try {
      await controller.loadRequest(Uri.parse(url)).timeout(timeout);
      return true;
    } catch (e) {
      debugPrint('URL loading error: $e');
      return false;
    }
  }

  /// Gets the current URL safely
  static Future<String?> getCurrentUrl(WebViewController controller) async {
    try {
      return await controller.currentUrl();
    } catch (e) {
      debugPrint('Error getting current URL: $e');
      return null;
    }
  }

  /// Safely reloads the WebView
  static Future<bool> safeReload(WebViewController controller) async {
    try {
      await controller.reload();
      return true;
    } catch (e) {
      debugPrint('Error reloading WebView: $e');
      return false;
    }
  }

  /// Checks if the WebView can go back
  static Future<bool> canGoBack(WebViewController controller) async {
    try {
      return await controller.canGoBack();
    } catch (e) {
      debugPrint('Error checking canGoBack: $e');
      return false;
    }
  }

  /// Checks if the WebView can go forward
  static Future<bool> canGoForward(WebViewController controller) async {
    try {
      return await controller.canGoForward();
    } catch (e) {
      debugPrint('Error checking canGoForward: $e');
      return false;
    }
  }

  /// Safely navigates back
  static Future<bool> goBack(WebViewController controller) async {
    try {
      await controller.goBack();
      return true;
    } catch (e) {
      debugPrint('Error going back: $e');
      return false;
    }
  }

  /// Safely navigates forward
  static Future<bool> goForward(WebViewController controller) async {
    try {
      await controller.goForward();
      return true;
    } catch (e) {
      debugPrint('Error going forward: $e');
      return false;
    }
  }

  /// Clears the WebView cache
  static Future<bool> clearCache(WebViewController controller) async {
    try {
      await controller.clearCache();
      return true;
    } catch (e) {
      debugPrint('Error clearing cache: $e');
      return false;
    }
  }

  /// Clears local storage
  static Future<bool> clearLocalStorage(WebViewController controller) async {
    try {
      await controller.clearLocalStorage();
      return true;
    } catch (e) {
      debugPrint('Error clearing local storage: $e');
      return false;
    }
  }

  /// Gets recommended WebView settings for different use cases
  static Map<String, dynamic> getRecommendedSettings({
    required WebViewUsage usage,
  }) {
    switch (usage) {
      case WebViewUsage.scraping:
        return {
          'enableJavaScript': true,
          'enableUserAgent': true,
          'enableCache': false,
          'enableLocalStorage': false,
          'timeout': const Duration(seconds: 30),
        };
      case WebViewUsage.browsing:
        return {
          'enableJavaScript': true,
          'enableUserAgent': true,
          'enableCache': true,
          'enableLocalStorage': true,
          'timeout': const Duration(seconds: 60),
        };
      case WebViewUsage.display:
        return {
          'enableJavaScript': false,
          'enableUserAgent': false,
          'enableCache': true,
          'enableLocalStorage': false,
          'timeout': const Duration(seconds: 15),
        };
    }
  }
}

/// Enum for different WebView usage patterns
enum WebViewUsage {
  scraping,
  browsing,
  display,
}

/// JavaScript channel parameters
class JavaScriptChannelParams {
  final void Function(JavaScriptMessage) onMessageReceived;

  const JavaScriptChannelParams({
    required this.onMessageReceived,
  });
}
