import 'package:flutter/material.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppSettings {
  static const localeKey = 'locale';

  static setLocale(locale, context) async {
    Provider.of<AppProvider>(context, listen: false).setLocale(Locale(locale));
    SharedPreferences preferences = await SharedPreferences.getInstance();
    return await preferences.setString(localeKey, locale);
  }

  static getLocale() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    return preferences.getString(localeKey);
  }

  static setOnboarded(bool value) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    return await preferences.setBool('is_onboarded', value);
  }

  static bool isRtl(context) {
    return ['ar', 'ku']
        .contains(Provider.of<AppProvider>(context).getCurrentLocaleCode);
  }
}
