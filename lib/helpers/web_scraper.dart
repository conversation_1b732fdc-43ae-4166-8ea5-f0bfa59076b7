import 'dart:io';

import 'package:flutter/material.dart';
import 'package:goldenprizma/helpers/clean_price.dart';
import 'package:goldenprizma/helpers/shein_currency_converter.dart';
import 'package:goldenprizma/helpers/url_to_file.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebScraper {
  Future<String?> getItemName(
      {required String script, required WebViewController controller}) async {
    final content = await controller.runJavaScriptReturningResult('''
        (function() {
          $script
        })();
        ''');

    return content.toString();
  }

  Future<double?> getPrice(
      {required String script,
      required WebViewController controller,
      bool convertToAed = false}) async {
    final content = await controller.runJavaScriptReturningResult('''
        (function() {
          $script
        })();
        ''');

    if (content.toString().isEmpty) return 0.0;

    if (convertToAed) {
      return convertToAED(content.toString());
    }

    return cleanPrice(content.toString());
  }

  Future<File?> getImage({
    required String script,
    required WebViewController controller,
  }) async {
    final html = await controller.runJavaScriptReturningResult('''
        (function() {
          $script
        })();
        ''');

    if (html.toString().isNotEmpty) {
      final url = html.toString().replaceAll('"', '');

      if (url.isEmpty) return null;

      File file = await urlToFile(url);
      debugPrint(file.toString());
      return file; // exit the loop if content is found
    }

    return null;
  }
}
