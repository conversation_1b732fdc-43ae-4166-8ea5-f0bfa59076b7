import 'dart:async';

import 'package:flutter/material.dart';
import 'package:goldenprizma/presentation/widgets/success_dialog.dart';

Future<void> showSuccessDialogWithAutoHide(
    {required BuildContext context,
    required String message,
    bool autoClose = true}) async {
  Timer? timer = Timer(const Duration(seconds: 3), () {
    if (autoClose && context.mounted) {
      Navigator.of(context, rootNavigator: true).pop();
    }
  });
  return await showDialog(
    context: context,
    builder: (context) {
      return SuccessDialog(message: message);
    },
  ).then((value) {
    timer?.cancel();
    timer = null;
  });
}
