// ignore_for_file: constant_identifier_names

class ApiUrls {
  static const login = '/v1/auth/login';
  static const logout = '/v1/auth/logout';
  static const register = '/v1/auth/register';
  static const orders = '/v1/orders';
  static const orderDetail = '/v1/orders/';
  static const acceptOrder = '/v1/orders/accept';
  static const rejectOrder = '/v1/orders/reject';
  static const reorderOrder = '/v1/orders/reorder';
  static const cities = '/v1/cities';
  static const districts = '/v1/districts';
  static const websites = '/v1/websites';
  static const filterWebsites = '/v1/filter/websites';
  static const filterBrands = '/v1/filter/brands';
  static const popularWebsites = '/v1/1/websites/popular';
  static const slides = '/v1/advertisements/slides';
  static const advertisements = '/v1/advertisements';
  static const notifications = '/v1/1/notifications';
  static const notificationMarkAllAsRead =
      '/v1/1/notifications/mark-all-as-read';
  static const updateFcmToken = '/v1/auth/firebase-token';
  static const transactions = '/v1/transactions';
  static const account = '/v1/account';
  static const requestDeliveries = '/v1/requested-deliveries';
  static const tickets = '/v1/support/tickets';
  static const ticketSubjects = '/v1/support/tickets/subjects';
  static const sizes = '/v1/sizes';
  static const splash = '/v1/splash';
  static const countries = '/v1/countries';
  static const scrap = '/v1/scrap';
  static const contact = '/v1/contact';
  static const profile = '/v1/auth/profile';
  static const updateProfilePhoto = '/v1/auth/profile/update-profile-photo';
  static const exchangeRate = '/v1/exchange-rate';
  static const deleteRequest = '/v1/auth/delete';
  static const sendOtp = '/v2/otp/send';
  static const urlTransformations = '/v1/url-transformations';

  /// using to verify code for forget password
  static const forgetPassword = '/v2/auth/forgot-password';
  static const updatePassword =
      '/v2/auth/update-password'; // update password using forget method
  static const changePassword =
      '/v2/auth/change-password'; // update password with old (current) password
  static const verifyOtp = '/v2/otp/verify';

  static const chatSubejcts = '/v2/chat-subjects';
  static const deposits = '/v2/payment-gateways';
  static const pendingDeposits = '/v2/pending-deposits';

  static const home = '/v2/home';

  static const placeStoreOrder = '/v2/orders/pos/create';

  // Store APIs
  static const storeHome = '/v1/home';
  static const storeBanners = '/v1/banners';
  static const storeProducts = '/v1/products';
  static const storeBrands = '/v1/brands';
  static const storeCategories = '/v1/categories';
}
