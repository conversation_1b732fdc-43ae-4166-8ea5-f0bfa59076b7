import 'package:flutter/material.dart';

void retry({
  required Future<dynamic> Function() callback,
  int maxAttempts = 5,
  int delay = 600,
  Function? onMaxReached,
  Function? onError,
}) async {
  int retries = 0;

  try {
    while (retries < maxAttempts) {
      final result = await callback();

      if (result != null) {
        break;
      }

      await Future.delayed(Duration(milliseconds: delay));
      retries++;
    }

    if (retries == maxAttempts && onMaxReached != null) {
      onMaxReached();
    }
    // ignore: empty_catches
  } catch (e) {
    if (onError != null) {
      onError(e);
    }
    debugPrint(e.toString());
  }
}
