import 'dart:math';

/// Manages randomized user agents to avoid website blocking
class UserAgentManager {
  static final UserAgentManager _instance = UserAgentManager._internal();
  factory UserAgentManager() => _instance;
  UserAgentManager._internal();

  final Random _random = Random();
  String? _currentSessionUserAgent;

  /// List of realistic mobile user agents for different browsers and platforms
  static const List<String> _mobileUserAgents = [
    // iOS Safari - Latest versions
    'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1',

    // iPad Safari
    'Mozilla/5.0 (iPad; CPU OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (iPad; CPU OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',

    // Android Chrome - Latest versions
    'Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
    'Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
    'Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
    'Mozilla/5.0 (Linux; Android 12; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
    'Mozilla/5.0 (Linux; Android 11; SM-A515F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',

    // Android Samsung Internet
    'Mozilla/5.0 (Linux; Android 13; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/23.0 Chrome/********* Mobile Safari/537.36',
    'Mozilla/5.0 (Linux; Android 12; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/22.0 Chrome/********* Mobile Safari/537.36',

    // Android Firefox
    'Mozilla/5.0 (Mobile; rv:119.0) Gecko/119.0 Firefox/119.0',
    'Mozilla/5.0 (Mobile; rv:118.0) Gecko/118.0 Firefox/118.0',

    // iOS Chrome
    'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/119.0.6045.109 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/118.0.5993.69 Mobile/15E148 Safari/604.1',

    // iOS Firefox
    'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) FxiOS/119.0.0 Mobile/15E148 Safari/605.1.15',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) FxiOS/118.0.0 Mobile/15E148 Safari/605.1.15',

    // Android Edge
    'Mozilla/5.0 (Linux; Android 13; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 EdgA/118.0.2088.78',
    'Mozilla/5.0 (Linux; Android 12; Pixel 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 EdgA/117.0.2045.53',
  ];

  /// Get a random user agent for a new website visit
  String getRandomUserAgent() {
    return _mobileUserAgents[_random.nextInt(_mobileUserAgents.length)];
  }

  /// Get the current session user agent (consistent within a session)
  String getSessionUserAgent() {
    _currentSessionUserAgent ??= getRandomUserAgent();
    return _currentSessionUserAgent!;
  }

  /// Get a new random user agent and update the session
  String getNewSessionUserAgent() {
    _currentSessionUserAgent = getRandomUserAgent();
    return _currentSessionUserAgent!;
  }

  /// Reset the session user agent (will generate a new one on next call)
  void resetSession() {
    _currentSessionUserAgent = null;
  }

  /// Get a user agent specifically optimized for a given website
  String getUserAgentForWebsite(String url) {
    final uri = Uri.tryParse(url);
    if (uri == null) return getRandomUserAgent();

    final host = uri.host.toLowerCase();

    // For specific websites that work better with certain user agents
    if (host.contains('shein.com')) {
      // Shein works well with iOS Safari
      final iosSafariAgents = _mobileUserAgents
          .where((ua) =>
              ua.contains('iPhone') &&
              ua.contains('Safari') &&
              !ua.contains('CriOS') &&
              !ua.contains('FxiOS'))
          .toList();
      return iosSafariAgents[_random.nextInt(iosSafariAgents.length)];
    }

    if (host.contains('amazon.com') || host.contains('amazon.')) {
      // Amazon works well with Chrome
      final chromeAgents = _mobileUserAgents
          .where((ua) => ua.contains('Chrome') && !ua.contains('EdgA'))
          .toList();
      return chromeAgents[_random.nextInt(chromeAgents.length)];
    }

    if (host.contains('aliexpress.com') || host.contains('alibaba.com')) {
      // AliExpress works well with Android Chrome
      final androidChromeAgents = _mobileUserAgents
          .where((ua) =>
              ua.contains('Android') &&
              ua.contains('Chrome') &&
              !ua.contains('Samsung') &&
              !ua.contains('EdgA'))
          .toList();
      return androidChromeAgents[_random.nextInt(androidChromeAgents.length)];
    }

    // For other websites, return a random user agent
    return getRandomUserAgent();
  }

  /// Get additional headers that can help avoid detection
  Map<String, String> getAntiDetectionHeaders() {
    return {
      'Accept':
          'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
      'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
      'Accept-Encoding': 'gzip, deflate, br',
      'DNT': '1',
      'Upgrade-Insecure-Requests': '1',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'none',
      'Sec-Fetch-User': '?1',
      'Cache-Control': 'max-age=0',
    };
  }

  /// Get the current user agent being used
  String? getCurrentUserAgent() {
    return _currentSessionUserAgent;
  }

  /// Get a user agent with slight randomization to avoid fingerprinting
  String getRandomizedUserAgent([String? baseUserAgent]) {
    final base = baseUserAgent ?? getRandomUserAgent();

    // Add slight variations to common version numbers to avoid detection
    String randomized = base;

    // Randomize Chrome version (within reasonable range)
    if (base.contains('Chrome/')) {
      final chromeVersions = [
        '*********',
        '*********',
        '*********',
        '*********'
      ];
      final randomVersion =
          chromeVersions[_random.nextInt(chromeVersions.length)];
      randomized = randomized.replaceAll(
          RegExp(r'Chrome/[\d.]+'), 'Chrome/$randomVersion');
    }

    // Randomize Safari version
    if (base.contains('Version/')) {
      final safariVersions = ['17.1', '17.0', '16.6', '16.5'];
      final randomVersion =
          safariVersions[_random.nextInt(safariVersions.length)];
      randomized = randomized.replaceAll(
          RegExp(r'Version/[\d.]+'), 'Version/$randomVersion');
    }

    // Randomize iOS version
    if (base.contains('OS ') && base.contains('like Mac OS X')) {
      final iosVersions = ['17_1', '17_0', '16_6', '16_5', '16_4'];
      final randomVersion = iosVersions[_random.nextInt(iosVersions.length)];
      randomized = randomized.replaceAll(
          RegExp(r'OS [\d_]+ like'), 'OS $randomVersion like');
    }

    // Randomize Android version
    if (base.contains('Android ')) {
      final androidVersions = ['14', '13', '12', '11'];
      final randomVersion =
          androidVersions[_random.nextInt(androidVersions.length)];
      randomized = randomized.replaceAll(
          RegExp(r'Android \d+'), 'Android $randomVersion');
    }

    return randomized;
  }

  /// Check if a user agent is likely to be blocked
  bool isUserAgentLikelyBlocked(String userAgent) {
    // Check for common signs of automated browsers
    final blockedPatterns = [
      'HeadlessChrome',
      'PhantomJS',
      'Selenium',
      'WebDriver',
      'Bot',
      'Spider',
      'Crawler',
    ];

    final lowerUserAgent = userAgent.toLowerCase();
    return blockedPatterns
        .any((pattern) => lowerUserAgent.contains(pattern.toLowerCase()));
  }
}
