// ignore_for_file: use_build_context_synchronously

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/helpers/app_settings.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocaleSelectorModal extends StatelessWidget {
  const LocaleSelectorModal({super.key});

  @override
  Widget build(BuildContext context) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: isDarkMode
              ? [AppColors.boxDarkColor, AppColors.darkBodyColor]
              : [Colors.white, Colors.grey.shade50],
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: SafeArea(
        top: false,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: isDarkMode ? Colors.grey.shade600 : Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 20, 24, 8),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      HugeIcons.strokeRoundedLanguageCircle,
                      color: AppColors.primaryColor,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          context.loc.selectLanguage,
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: isDarkMode
                                ? Colors.white
                                : AppColors.headerColor,
                          ),
                        ),
                        Text(
                          context.loc.choosePreferedLanguage,
                          style: TextStyle(
                            fontSize: 14,
                            color: isDarkMode
                                ? Colors.grey.shade400
                                : Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Language options
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 8, 24, 32),
              child: Column(
                children: [
                  _buildLanguageOption(
                    context,
                    languageName: 'English',
                    localeName: 'English',
                    localeCode: 'en',
                    flagPath: 'assets/images/flags/en.png',
                    isDarkMode: isDarkMode,
                  ),
                  const SizedBox(height: 12),
                  _buildLanguageOption(
                    context,
                    languageName: 'Arabic',
                    localeName: 'العربية',
                    localeCode: 'ar',
                    flagPath: 'assets/images/flags/ar.png',
                    isDarkMode: isDarkMode,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageOption(
    BuildContext context, {
    required String languageName,
    required String localeName,
    required String localeCode,
    required String flagPath,
    required bool isDarkMode,
  }) {
    final isSelected =
        Provider.of<AppProvider>(context).getCurrentLocaleCode == localeCode;

    return Container(
      width: double.infinity,
      height: 68,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isSelected
              ? isDarkMode
                  ? [
                      AppColors.primaryColor.withOpacity(0.2),
                      AppColors.primaryColor.withOpacity(0.1),
                    ]
                  : [
                      AppColors.primaryColor.withOpacity(0.1),
                      AppColors.primaryColor.withOpacity(0.05),
                    ]
              : isDarkMode
                  ? [
                      AppColors.boxDarkColor,
                      AppColors.boxDarkColor.withOpacity(0.8)
                    ]
                  : [Colors.white, Colors.grey.shade50],
        ),
        borderRadius: BorderRadius.circular(16),
        border: isSelected
            ? Border.all(
                color: AppColors.primaryColor.withOpacity(0.3),
                width: 2,
              )
            : Border.all(
                color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade200,
                width: 1,
              ),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withOpacity(0.3)
                : Colors.grey.withOpacity(0.1),
            blurRadius: isSelected ? 12 : 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          splashColor: AppColors.primaryColor.withOpacity(0.1),
          highlightColor: AppColors.primaryColor.withOpacity(0.05),
          onTap: () async {
            await Future.delayed(const Duration(milliseconds: 150));
            changeLocale(localeCode, context);
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                // Flag with circular border
                Container(
                  width: 40,
                  height: 40,
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected
                          ? AppColors.primaryColor
                          : isDarkMode
                              ? Colors.grey.shade600
                              : Colors.grey.shade300,
                      width: isSelected ? 2 : 1.5,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(19),
                    child: Image.asset(
                      flagPath,
                      fit: BoxFit.cover,
                      width: 36,
                      height: 36,
                    ),
                  ),
                ),

                const SizedBox(width: 16),

                // Language information
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        languageName,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: isSelected
                              ? AppColors.primaryColor
                              : isDarkMode
                                  ? Colors.white
                                  : AppColors.headerColor,
                        ),
                      ),
                      if (languageName != localeName) ...[
                        const SizedBox(height: 2),
                        Text(
                          localeName,
                          style: TextStyle(
                            fontSize: 14,
                            color: isDarkMode
                                ? Colors.grey.shade400
                                : Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                // Selection indicator
                if (isSelected)
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: const BoxDecoration(
                      color: AppColors.primaryColor,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      HugeIcons.strokeRoundedTick02,
                      color: Colors.white,
                      size: 16,
                    ),
                  )
                else
                  Icon(
                    AppSettings.isRtl(context)
                        ? HugeIcons.strokeRoundedArrowLeft01
                        : HugeIcons.strokeRoundedArrowRight01,
                    color: isDarkMode
                        ? Colors.grey.shade400
                        : Colors.grey.shade600,
                    size: 20,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

void changeLocale(String locale, BuildContext context) async {
  Provider.of<AppProvider>(context, listen: false).setLocale(Locale(locale));
  SharedPreferences pref = await SharedPreferences.getInstance();
  pref.setString('locale', locale);
  Navigator.of(context).pop();
  await FirebaseMessaging.instance.subscribeToTopic(locale);
  for (var element in ['ar', 'ku', 'en']) {
    if (element != locale) {
      FirebaseMessaging.instance.unsubscribeFromTopic(element);
    }
  }
}
