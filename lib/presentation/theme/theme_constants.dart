import 'package:flutter/material.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';

class GoldenThemes {
  static final light = ThemeData.light(useMaterial3: false).copyWith(
    brightness: Brightness.light,
    scaffoldBackgroundColor: Colors.grey.shade50,
    splashColor: AppColors.primaryColor,
    primaryColor: AppColors.primaryColor,
    drawerTheme: const DrawerThemeData(backgroundColor: Colors.white),
    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.white,
      foregroundColor: Colors.black,
      elevation: 0,
    ),
    bottomNavigationBarTheme:
        const BottomNavigationBarThemeData(backgroundColor: Colors.white),
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      backgroundColor: AppColors.primaryColor,
    ),
    inputDecorationTheme: InputDecorationTheme(
      contentPadding: const EdgeInsets.all(16),
      filled: true,
      fillColor: Colors.grey.shade200,
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Colors.red),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Colors.red),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Colors.transparent),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.primaryColor),
      ),
    ),
    buttonTheme: ButtonThemeData(
      height: 50,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
      ),
    ),
    iconTheme: IconThemeData(color: Colors.grey.shade600),
    colorScheme: const ColorScheme.light(
      primary: AppColors.primaryColor,
      secondary: AppColors.primaryColor,
      surface: Colors.white,
      surfaceBright: Colors.white,
      error: Colors.red,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: Colors.black,
      onError: Colors.white,
    ),
  );

  static final dark = ThemeData.dark(useMaterial3: false).copyWith(
      scaffoldBackgroundColor: AppColors.darkBodyColor,
      primaryColor: AppColors.primaryColor,
      splashColor: AppColors.primaryColor,
      drawerTheme:
          const DrawerThemeData(backgroundColor: AppColors.appbarDarkColor),
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.appbarDarkColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      bottomAppBarTheme:
          const BottomAppBarTheme(color: AppColors.appbarDarkColor),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: AppColors.primaryColor,
      ),
      inputDecorationTheme: InputDecorationTheme(
        contentPadding: const EdgeInsets.all(16),
        filled: true,
        fillColor: AppColors.boxDarkColor,
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.red),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.red),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.transparent),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.primaryColor),
        ),
      ),
      buttonTheme: ButtonThemeData(
        height: 50,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.0),
        ),
      ),
      iconTheme: const IconThemeData(color: Colors.white70),
      cardColor: AppColors.boxDarkColor,
      dividerColor: Colors.grey[700],
      primaryColorLight: AppColors.primaryColor,
      colorScheme: const ColorScheme.dark(
        primary: AppColors.primaryColor,
        secondary: AppColors.primaryColor,
        surface: AppColors.boxDarkColor,
        surfaceBright: AppColors.boxDarkColor,
        error: Colors.red,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: Colors.white,
        onError: Colors.white,
      ));
}
