import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/domain/support/bloc/ticket_bloc.dart';
import 'package:goldenprizma/domain/support/bloc/ticket_form_bloc.dart';
import 'package:goldenprizma/domain/support/bloc/ticket_subject_bloc.dart';
import 'package:goldenprizma/domain/support/models/ticket_subject.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/helpers/custom_snackbars.dart';
import 'package:goldenprizma/main.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/repositories/support_repository.dart';
import 'package:ionicons/ionicons.dart';
import 'package:provider/provider.dart';

class TicketForm extends StatelessWidget {
  TicketForm({super.key});

  final TextEditingController _textEditingController = TextEditingController();

  static PageRouteBuilder pageRoute(TicketBloc ticketBloc) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) =>
          MultiBlocProvider(
        providers: [
          BlocProvider.value(value: ticketBloc),
          BlocProvider(
            create: (context) => TicketFormBloc(),
          ),
          BlocProvider(
            create: (context) => TicketSubjectBloc(
              supportRepository: getIt.get<SupportRepository>(),
            )..add(TicketSubjectRequestLoad()),
          ),
        ],
        child: TicketForm(),
      ),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(0.0, 1.0);
        const end = Offset.zero;

        var curve = Curves.ease;

        var tween =
            Tween(begin: begin, end: end).chain(CurveTween(curve: curve));

        final offsetAnimation = animation.drive(tween);
        return SlideTransition(
          position: offsetAnimation,
          child: child,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);
    return Scaffold(
      appBar: _appBar(context),
      body: BlocListener<TicketFormBloc, TicketFormState>(
        listener: (context, state) {
          if (state.status.isSubmissionFailure) {
            showErrorSnackBar(context: context, message: state.errorMessage);
          }

          if (state.status.isSubmissionSuccess) {
            showSuccessSnackBar(
              context: context,
              message: state.successMessage,
            );
            context.read<TicketBloc>().add(TicketReload());
            Navigator.of(context).pop();
          }
        },
        child: GestureDetector(
          onTap: () {
            FocusManager.instance.primaryFocus?.unfocus();
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              color: Provider.of<AppProvider>(context).isDarkMode(context)
                  ? AppColors.boxDarkColor
                  : Colors.white,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 16),
                _subjectField(isDarkMode),
                const SizedBox(height: 16),
                _descriptionField(context),
                const SizedBox(height: 32),
                _submitButton(),
                const SizedBox(height: 12),
                _resetButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _subjectField(bool isDarkMode) {
    return BlocBuilder<TicketSubjectBloc, TicketSubjectState>(
      builder: (context, state) {
        if (state.status == TicketSubjectStatus.initial) {
          return const Center(child: CircularProgressIndicator.adaptive());
        }
        final List<TicketSubject> subjects = state.subjects;
        return BlocBuilder<TicketFormBloc, TicketFormState>(
          builder: (context, state) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  width: 1.0,
                  color: state.subject.invalid
                      ? Colors.red
                      : isDarkMode
                          ? Colors.grey
                          : Colors.grey.shade300,
                ),
              ),
              child: Column(
                children: [
                  DropdownButton<String>(
                    hint: Text(context.loc.subject),
                    isExpanded: true,
                    borderRadius: BorderRadius.circular(5),
                    underline: const SizedBox(),
                    value:
                        state.subject.value != '' ? state.subject.value : null,
                    items: subjects.map((TicketSubject value) {
                      return DropdownMenuItem<String>(
                        value: value.title,
                        child: Text(value.title),
                      );
                    }).toList(),
                    onChanged: (String? value) {
                      context
                          .read<TicketFormBloc>()
                          .add(TicketSubjectFieldChanged(subject: value ?? ''));
                    },
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _descriptionField(BuildContext context) {
    return BlocBuilder<TicketFormBloc, TicketFormState>(
      builder: (context, state) {
        return TextFormField(
            controller: _textEditingController,
            keyboardType: TextInputType.multiline,
            maxLines: 5,
            decoration: InputDecoration(
              border: const OutlineInputBorder(),
              labelText: context.loc.description,
              alignLabelWithHint: true,
              errorText: state.hasServerError
                  ? state.errorMessage
                  : state.description.validationErrorMessage(context),
              errorMaxLines: 4,
            ),
            onChanged: (value) {
              context
                  .read<TicketFormBloc>()
                  .add(TicketDescriptionFieldChanged(description: value));
            });
      },
    );
  }

  _submitButton() {
    return BlocBuilder<TicketFormBloc, TicketFormState>(
      builder: (context, state) {
        return MaterialButton(
          onPressed: state.status.isSubmissionInProgress
              ? null
              : () {
                  context
                      .read<TicketFormBloc>()
                      .add(const TicketFormSubmitted());
                },
          color: AppColors.primaryColor,
          minWidth: double.infinity,
          height: 50,
          elevation: 0,
          disabledTextColor: Colors.grey,
          textColor: Colors.white,
          child: state.status.isSubmissionInProgress
              ? const CircularProgressIndicator.adaptive()
              : Text(
                  context.loc.createTicket,
                ),
        );
      },
    );
  }

  _resetButton() {
    return BlocBuilder<TicketFormBloc, TicketFormState>(
      builder: (context, state) {
        return SizedBox(
          height: 50,
          width: double.infinity,
          child: TextButton(
            onPressed: state.status.isSubmissionInProgress
                ? null
                : () {
                    _textEditingController.clear();
                    context.read<TicketFormBloc>().add(const TicketFormReset());
                    Navigator.of(context, rootNavigator: true).pop();
                  },
            child: Text(context.loc.cancel),
          ),
        );
      },
    );
  }

  AppBar _appBar(BuildContext context) {
    return AppBar(
      title: Text(context.loc.createTicket),
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Ionicons.close),
        onPressed: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }
}
