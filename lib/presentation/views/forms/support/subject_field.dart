import 'package:formz/formz.dart';

enum SubjectValidationError { empty }

class SubjectField extends FormzInput<String, SubjectValidationError> {
  const SubjectField.pure() : super.pure('');
  const SubjectField.dirty(super.value) : super.dirty();

  @override
  SubjectValidationError? validator(String value) {
    if (value.isEmpty) {
      return SubjectValidationError.empty;
    }
    return null;
  }

  String? get validationErrorMessage {
    if (valid || pure) {
      return null;
    }

    if (error == SubjectValidationError.empty) {
      return 'Subject is required';
    }
    return null;
  }
}
