import 'package:flutter/material.dart';
import 'package:formz/formz.dart';

enum ImageValidationError { empty, minSize, maxSize, mimType }

class ImageField extends FormzInput<FileImage?, ImageValidationError> {
  const ImageField.pure() : super.pure(null);
  const ImageField.dirty(super.value) : super.dirty();

  @override
  ImageValidationError? validator(FileImage? value) {
    return null;

    // print(value!.type);

    // if (!value.type) {
    //   return null;
    // }

    // validate image
  }
}
