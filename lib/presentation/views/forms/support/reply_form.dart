import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/domain/support/bloc/reply_form_bloc.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/widgets/success_dialog.dart';
import 'package:provider/provider.dart';

class ReplyForm extends StatelessWidget {
  ReplyForm({super.key, required this.ticketId});

  final int ticketId;
  final TextEditingController _textEditingController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return BlocListener<ReplyFormBloc, ReplyFormState>(
      listener: (context, state) {
        if (state.status.isSubmissionSuccess) {
          // reset form
          context.read<ReplyFormBloc>().add(const ReplyFormReset());
          _textEditingController.clear();

          // Show success dialog
          showDialog(
            context: context,
            builder: (context) {
              return SuccessDialog(message: state.successMessage);
            },
          );

          // context.read<ReplyBloc>().add(ReplyAppend(reply: state.ticketReply));
          // Navigator.of(context).pop();
          // add created request from api response instead of refreshing the list
        }
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Add New Reply',
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 15),
          ),
          const SizedBox(height: 24),
          _bodyField(context),
          const SizedBox(height: 16),
          _submitButton(ticketId),
          const SizedBox(height: 12),
          _cancelButton(),
        ],
      ),
    );
  }

  Widget _bodyField(BuildContext context) {
    return BlocBuilder<ReplyFormBloc, ReplyFormState>(
      builder: (context, state) {
        return TextFormField(
            controller: _textEditingController,
            autofocus: true,
            maxLines: 6,
            decoration: InputDecoration(
              border: const OutlineInputBorder(),
              labelText: "Body",
              // labelText: context.loc.note,
              errorText: state.hasServerError
                  ? state.errorMessage
                  : state.body.validationErrorMessage(context),
              errorMaxLines: 4,
            ),
            onChanged: (value) {
              context.read<ReplyFormBloc>().add(ReplyBodyChanged(body: value));
            });
      },
    );
  }

  _submitButton(int ticketId) {
    return BlocBuilder<ReplyFormBloc, ReplyFormState>(
      builder: (context, state) {
        return MaterialButton(
          onPressed: state.status.isValid
              ? () {
                  context
                      .read<ReplyFormBloc>()
                      .add(ReplyFormSubmitted(ticketId: ticketId));
                }
              : null,
          color: AppColors.primaryColor,
          minWidth: double.infinity,
          height: 50,
          elevation: 0,
          disabledColor: Provider.of<AppProvider>(context).isDarkMode(context)
              ? AppColors.darkBodyColor
              : Colors.grey.shade100,
          disabledTextColor:
              Provider.of<AppProvider>(context).isDarkMode(context)
                  ? Colors.grey.shade800
                  : Colors.grey.shade300,
          textColor: Colors.white,
          child: state.status.isSubmissionInProgress
              ? const CircularProgressIndicator.adaptive()
              : Text(
                  context.loc.submit,
                ),
        );
      },
    );
  }

  _cancelButton() {
    return BlocBuilder<ReplyFormBloc, ReplyFormState>(
      builder: (context, state) {
        return SizedBox(
          height: 50,
          width: double.infinity,
          child: TextButton(
            onPressed: () {
              _textEditingController.clear();
              context.read<ReplyFormBloc>().add(const ReplyFormReset());
              Navigator.of(context).pop();
            },
            child: Text(context.loc.cancel),
          ),
        );
      },
    );
  }
}
