// ignore_for_file: use_super_parameters

import 'package:flutter/material.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';

enum ReplyBodyValidationError { empty, minChar, maxChar }

class ReplyBodyField extends FormzInput<String, ReplyBodyValidationError> {
  const ReplyBodyField.dirty(value) : super.dirty(value);
  const ReplyBodyField.pure() : super.pure('');

  @override
  validator(value) {
    if (value.isEmpty) {
      return ReplyBodyValidationError.empty;
    }
    if (value.length < 10) {
      return ReplyBodyValidationError.minChar;
    }

    if (value.length > 500) {
      return ReplyBodyValidationError.maxChar;
    }
    return null;
  }

  String? validationErrorMessage(BuildContext context) {
    if (valid || pure) {
      return null;
    }

    if (error == ReplyBodyValidationError.minChar) {
      return context.loc.validationMinChar(10);
    }

    if (error == ReplyBodyValidationError.maxChar) {
      return context.loc.validationMaxChar(500);
    }

    return context.loc.validationRequired(context.loc.body);
  }
}
