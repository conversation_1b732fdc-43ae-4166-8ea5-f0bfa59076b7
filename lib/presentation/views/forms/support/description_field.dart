import 'package:flutter/material.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';

enum DescriptionValidationError { empty, minChar, maxChar }

class DescriptionField extends FormzInput<String, DescriptionValidationError> {
  const DescriptionField.pure() : super.pure('');
  const DescriptionField.dirty(super.value) : super.dirty();

  @override
  DescriptionValidationError? validator(String value) {
    if (value.isEmpty) {
      return DescriptionValidationError.empty;
    }
    if (value.length < 10) {
      return DescriptionValidationError.minChar;
    }

    if (value.length > 500) {
      return DescriptionValidationError.maxChar;
    }
    return null;
  }

  String? validationErrorMessage(BuildContext context) {
    if (valid || pure) {
      return null;
    }

    if (error == DescriptionValidationError.minChar) {
      return context.loc.validationMinChar(10);
    }

    if (error == DescriptionValidationError.maxChar) {
      return context.loc.validationMaxChar(500);
    }

    return context.loc.validationRequired(context.loc.description);
  }
}
