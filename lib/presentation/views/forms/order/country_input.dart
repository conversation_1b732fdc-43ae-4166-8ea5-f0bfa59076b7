import 'package:flutter/material.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';

enum CountryValidationError { empty }

class CountryInput extends FormzInput<int?, CountryValidationError> {
  const CountryInput.pure() : super.pure(null);
  const CountryInput.dirty(super.value) : super.dirty();

  @override
  CountryValidationError? validator(int? value) {
    if (value == null) {
      return CountryValidationError.empty;
    }
    return null;
  }

  String? validationErrorMessage(BuildContext context) {
    if (valid || pure) {
      return null;
    }
    if (error == CountryValidationError.empty) {
      return context.loc.validationRequired(context.loc.country);
    }
    return null;
  }
}
