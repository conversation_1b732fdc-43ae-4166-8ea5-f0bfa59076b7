import 'package:flutter/material.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/support/validations.dart';

enum LinkValidationError { empty, invalid }

class LinkInput extends FormzInput<String, LinkValidationError> {
  const LinkInput.pure() : super.pure('');
  const LinkInput.dirty(super.value) : super.dirty();

  @override
  LinkValidationError? validator(String value) {
    if (value.isEmpty) {
      return LinkValidationError.empty;
    }

    if (!Validations.validateUrl(value)) {
      return LinkValidationError.invalid;
    }
    return null;
  }

  String? validationErrorMessage(BuildContext context) {
    if (valid || pure) {
      return null;
    }
    if (error == LinkValidationError.empty) {
      return context.loc.validationRequired(context.loc.link);
    }
    if (error == LinkValidationError.invalid) {
      return context.loc.validationInvalid(context.loc.link);
    }
    return null;
  }
}
