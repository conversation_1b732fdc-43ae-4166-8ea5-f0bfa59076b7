import 'package:formz/formz.dart';
import 'package:validators/sanitizers.dart';

enum QuantityValidationError { empty, min }

class QuantityInput extends FormzInput<String, QuantityValidationError> {
  const QuantityInput.pure() : super.pure('');
  const QuantityInput.dirty(super.value) : super.dirty();

  @override
  QuantityValidationError? validator(String value) {
    if (value.isEmpty) {
      return QuantityValidationError.empty;
    }

    if (toInt(value) < 1) {
      return QuantityValidationError.min;
    }
    return null;
  }

  String? get validationErrorMessage {
    if (valid || pure) {
      return null;
    }

    if (error == QuantityValidationError.empty ||
        error == QuantityValidationError.min) {
      return "Quantity of minimum 1 is required";
    }
    return null;
  }
}
