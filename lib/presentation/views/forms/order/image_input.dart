import 'dart:io';

import 'package:flutter/material.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';

enum ImageValidationError { empty, invalid }

class ImageInput extends FormzInput<File?, ImageValidationError> {
  const ImageInput.pure() : super.pure(null);
  const ImageInput.dirty(super.value) : super.dirty();

  @override
  ImageValidationError? validator(File? value) {
    if (value == null) {
      return ImageValidationError.empty;
    }
    return null;
  }

  String? validationErrorMessage(BuildContext context) {
    if (valid) {
      return null;
    }
    if (error == ImageValidationError.empty) {
      return context.loc.validationRequired(context.loc.image);
    }
    if (error == ImageValidationError.invalid) {
      return context.loc.validationInvalid(context.loc.image);
    }
    return null;
  }
}
