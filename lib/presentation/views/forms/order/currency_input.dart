import 'package:flutter/material.dart';
import 'package:formz/formz.dart';

enum CurrencyValidationError { empty }

class CurrencyInput extends FormzInput<String?, CurrencyValidationError> {
  const CurrencyInput.pure() : super.pure(null);
  const CurrencyInput.dirty(super.value) : super.dirty();

  @override
  CurrencyValidationError? validator(String? value) {
    if (value == null) {
      return CurrencyValidationError.empty;
    }
    return null;
  }

  String? validationErrorMessage(BuildContext context) {
    return null;
  }
}
