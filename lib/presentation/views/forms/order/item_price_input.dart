import 'package:flutter/material.dart';
import 'package:formz/formz.dart';

enum ItemPriceValidationError { empty }

class ItemPriceInput extends FormzInput<double?, ItemPriceValidationError> {
  const ItemPriceInput.pure() : super.pure(null);
  const ItemPriceInput.dirty(super.value) : super.dirty();

  @override
  ItemPriceValidationError? validator(double? value) {
    return null;
  }

  String? validationErrorMessage(BuildContext context) {
    return null;
  }
}
