import 'package:formz/formz.dart';

enum DescriptionValidationError { maxChar }

class DescriptionInput extends FormzInput<String, DescriptionValidationError> {
  const DescriptionInput.pure() : super.pure('');
  const DescriptionInput.dirty(super.value) : super.dirty();

  @override
  DescriptionValidationError? validator(String value) {
    if (value.isNotEmpty && value.length > 500) {
      return DescriptionValidationError.maxChar;
    }
    return null;
  }

  String? get validationErrorMessage {
    if (valid || pure) {
      return null;
    }

    if (error == DescriptionValidationError.maxChar) {
      return 'Maximum 500 characters';
    }
    return null;
  }
}
