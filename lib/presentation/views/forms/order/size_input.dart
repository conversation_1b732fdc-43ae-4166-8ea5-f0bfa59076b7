import 'package:flutter/material.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/domain/orders/models/size_model.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';

enum SizeValidationError { empty }

class SizeInput extends FormzInput<SizeModel?, SizeValidationError> {
  const SizeInput.pure() : super.pure(null);
  const SizeInput.dirty(super.value) : super.dirty();

  @override
  SizeValidationError? validator(SizeModel? value) {
    if (value == null) {
      return SizeValidationError.empty;
    }
    return null;
  }

  String? validationErrorMessage(BuildContext context) {
    if (valid || pure) {
      return null;
    }
    if (error == SizeValidationError.empty) {
      return context.loc.validationRequired(context.loc.size);
    }
    return null;
  }
}
