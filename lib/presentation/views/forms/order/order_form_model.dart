import 'package:equatable/equatable.dart';
import 'package:formz/formz.dart';

import 'package:goldenprizma/presentation/views/forms/order/country_input.dart';
import 'package:goldenprizma/presentation/views/forms/order/currency_input.dart';
import 'package:goldenprizma/presentation/views/forms/order/description_input.dart';
import 'package:goldenprizma/presentation/views/forms/order/image_input.dart';
import 'package:goldenprizma/presentation/views/forms/order/item_price_input.dart';
import 'package:goldenprizma/presentation/views/forms/order/link_input.dart';
import 'package:goldenprizma/presentation/views/forms/order/quantity_field.dart';
import 'package:goldenprizma/presentation/views/forms/order/size_input.dart';

class OrderFormModel extends Equatable with FormzMixin {
  const OrderFormModel({
    this.linkInput = const LinkInput.pure(),
    this.imageInput = const ImageInput.pure(),
    this.quantityInput = const QuantityInput.dirty('1'),
    this.sizeInput = const SizeInput.pure(),
    this.descriptionInput = const DescriptionInput.pure(),
    this.countryInput = const CountryInput.pure(),
    this.itemPriceInput = const ItemPriceInput.pure(),
    this.currencyInput = const CurrencyInput.pure(),
  });

  final LinkInput linkInput;
  final ImageInput imageInput;
  final QuantityInput quantityInput;
  final SizeInput sizeInput;
  final DescriptionInput descriptionInput;
  final CountryInput countryInput;
  final ItemPriceInput itemPriceInput;
  final CurrencyInput currencyInput;

  @override
  List<FormzInput> get inputs => [
        linkInput,
        imageInput,
        quantityInput,
        sizeInput,
        descriptionInput,
        countryInput,
        itemPriceInput,
        currencyInput,
      ];

  @override
  List<Object> get props => [
        linkInput,
        imageInput,
        quantityInput,
        sizeInput,
        descriptionInput,
        countryInput,
        itemPriceInput,
        currencyInput,
      ];

  OrderFormModel copyWith({
    LinkInput? linkInput,
    ImageInput? imageInput,
    QuantityInput? quantityInput,
    SizeInput? sizeInput,
    DescriptionInput? descriptionInput,
    CountryInput? countryInput,
    ItemPriceInput? itemPriceInput,
    CurrencyInput? currencyInput,
  }) {
    return OrderFormModel(
      linkInput: linkInput ?? this.linkInput,
      imageInput: imageInput ?? this.imageInput,
      quantityInput: quantityInput ?? this.quantityInput,
      sizeInput: sizeInput ?? this.sizeInput,
      descriptionInput: descriptionInput ?? this.descriptionInput,
      countryInput: countryInput ?? this.countryInput,
      itemPriceInput: itemPriceInput ?? this.itemPriceInput,
      currencyInput: currencyInput ?? this.currencyInput,
    );
  }
}
