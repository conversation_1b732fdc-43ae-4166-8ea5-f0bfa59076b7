import 'package:formz/formz.dart';
import 'package:validators/validators.dart';

enum EmailValidationError { empty, invalid }

class EmailInput extends FormzInput<String, EmailValidationError> {
  const EmailInput.pure({String value = ''}) : super.pure(value);
  const EmailInput.dirty(super.value) : super.dirty();

  @override
  EmailValidationError? validator(String value) {
    if (value.isEmpty) {
      return EmailValidationError.empty;
    }
    if (!isEmail(value)) {
      return EmailValidationError.invalid;
    }
    return null;
  }

  String? get validationErrorMessage {
    if (valid || pure) {
      return null;
    }
    if (error == EmailValidationError.empty) {
      return 'Email is required';
    }

    if (error == EmailValidationError.invalid) {
      return 'Email is invalid';
    }
    return null;
  }
}
