import 'package:flutter/material.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';

enum PasswordValidationError { empty, length, easy }

class PasswordInput extends FormzInput<String, PasswordValidationError> {
  const PasswordInput.pure() : super.pure('');
  const PasswordInput.dirty(super.value) : super.dirty();

  @override
  PasswordValidationError? validator(String value) {
    if (value.isEmpty) {
      return PasswordValidationError.empty;
    }
    if (value.length < 8) {
      return PasswordValidationError.length;
    }
    if (value.startsWith('12345678')) {
      return PasswordValidationError.easy;
    }
    return null;
  }

  String? validationErrorMessage(BuildContext context) {
    if (valid || pure) {
      return null;
    }
    if (error == PasswordValidationError.empty) {
      return context.loc.validationRequired(context.loc.password);
    }

    if (error == PasswordValidationError.length) {
      return context.loc.passwordIsShort;
    }

    if (error == PasswordValidationError.easy) {
      return context.loc.strongPassword;
    }
    return null;
  }
}
