import 'package:equatable/equatable.dart';
import 'package:formz/formz.dart';

import 'package:goldenprizma/presentation/views/forms/auth/city_input.dart';
import 'package:goldenprizma/presentation/views/forms/auth/fullname_input.dart';
import 'package:goldenprizma/presentation/views/forms/auth/password_input.dart';
import 'package:goldenprizma/presentation/views/forms/auth/phone_code_input.dart';
import 'package:goldenprizma/presentation/views/forms/auth/phone_number_input.dart';

class RegisterFormModel extends Equatable with FormzMixin {
  const RegisterFormModel({
    this.fullNameInput = const FullNameInput.pure(),
    this.phoneNumberInput = const PhoneNumberInput.pure(),
    this.phoneCodeInput = const PhoneCodeInput.pure(),
    this.cityInput = const CityInput.pure(),
    this.passwordInput = const PasswordInput.pure(),
    this.confirmPasswordInput = const PasswordInput.pure(),
  });
  final FullNameInput fullNameInput;
  final PhoneNumberInput phoneNumberInput;
  final PhoneCodeInput phoneCodeInput;
  final CityInput cityInput;
  final PasswordInput passwordInput;
  final PasswordInput confirmPasswordInput;

  @override
  List<FormzInput> get inputs => [
        fullNameInput,
        phoneNumberInput,
        phoneCodeInput,
        cityInput,
        passwordInput,
        confirmPasswordInput
      ];

  @override
  List<Object> get props => [
        fullNameInput,
        phoneNumberInput,
        phoneCodeInput,
        cityInput,
        passwordInput,
        confirmPasswordInput
      ];

  RegisterFormModel copyWith({
    FullNameInput? fullNameInput,
    PhoneNumberInput? phoneNumberInput,
    PhoneCodeInput? phoneCodeInput,
    CityInput? cityInput,
    PasswordInput? passwordInput,
    PasswordInput? confirmPasswordInput,
  }) {
    return RegisterFormModel(
      fullNameInput: fullNameInput ?? this.fullNameInput,
      phoneNumberInput: phoneNumberInput ?? this.phoneNumberInput,
      phoneCodeInput: phoneCodeInput ?? this.phoneCodeInput,
      cityInput: cityInput ?? this.cityInput,
      passwordInput: passwordInput ?? this.passwordInput,
      confirmPasswordInput: confirmPasswordInput ?? this.confirmPasswordInput,
    );
  }

  @override
  String toString() {
    return 'RegisterFormModel(fullNameInput: $fullNameInput, phoneNumberInput: $phoneNumberInput, phoneCodeInput: $phoneCodeInput, cityInput: $cityInput, passwordInput: $passwordInput, confirmPasswordInput: $confirmPasswordInput)';
  }
}
