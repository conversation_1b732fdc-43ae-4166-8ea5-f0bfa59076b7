import 'package:flutter/material.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/domain/models/city.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';

enum CityValidationError { empty }

class CityInput extends FormzInput<City?, CityValidationError> {
  const CityInput.pure() : super.pure(null);
  const CityInput.dirty(super.value) : super.dirty();

  @override
  CityValidationError? validator(City? value) {
    if (value == null) {
      return CityValidationError.empty;
    }
    return null;
  }

  String? validationErrorMessage(BuildContext context) {
    if (valid || pure) {
      return null;
    }
    if (error == CityValidationError.empty) {
      return context.loc.validationRequired(context.loc.city);
    }
    return null;
  }
}
