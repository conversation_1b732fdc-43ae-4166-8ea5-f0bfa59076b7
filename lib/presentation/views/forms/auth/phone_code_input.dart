import 'package:formz/formz.dart';

enum PhoneCodeInvalid { empty, length, invalid }

class PhoneCodeInput extends FormzInput<String, PhoneCodeInvalid> {
  const PhoneCodeInput.pure() : super.pure('');
  const PhoneCodeInput.dirty(super.value) : super.dirty();

  @override
  PhoneCodeInvalid? validator(String value) {
    if (value.isEmpty) {
      return PhoneCodeInvalid.empty;
    }
    return null;
  }
}
