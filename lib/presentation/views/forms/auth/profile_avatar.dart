// ignore_for_file: use_build_context_synchronously

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/auth/bloc/authentication_bloc.dart';
import 'package:goldenprizma/domain/auth/bloc/profile_avatar_bloc.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/helpers/custom_snackbars.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image/image.dart' as img;
import 'package:hugeicons/hugeicons.dart';
import 'package:permission_handler/permission_handler.dart';

class EditableProfileAvatar extends StatefulWidget {
  const EditableProfileAvatar({super.key});

  @override
  State<EditableProfileAvatar> createState() => EditableProfileAvatarState();
}

class EditableProfileAvatarState extends State<EditableProfileAvatar> {
  final ImagePicker _picker = ImagePicker();

  // Show image source selection bottom sheet
  void _showImageSourceBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => _buildImageSourceBottomSheet(),
    );
  }

  // Build the image source selection bottom sheet
  Widget _buildImageSourceBottomSheet() {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 24),

          // Title
          Text(
            context.loc.selectImageSource,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),

          // Camera option
          _buildSourceOption(
            icon: HugeIcons.strokeRoundedCamera01,
            title: context.loc.takePhoto,
            subtitle: context.loc.camera,
            onTap: () {
              Navigator.pop(context);
              _pickImageFromSource(ImageSource.camera);
            },
          ),

          const SizedBox(height: 16),

          // Gallery option
          _buildSourceOption(
            icon: HugeIcons.strokeRoundedImage01,
            title: context.loc.chooseFromGallery,
            subtitle: context.loc.gallery,
            onTap: () {
              Navigator.pop(context);
              _pickImageFromSource(ImageSource.gallery);
            },
          ),

          const SizedBox(height: 24),
        ],
      ),
    );
  }

  // Build individual source option
  Widget _buildSourceOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[200]!),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: Theme.of(context).primaryColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }

  // Pick image from specified source
  Future<void> _pickImageFromSource(ImageSource source) async {
    try {
      // Check permissions for camera
      if (source == ImageSource.camera) {
        final cameraStatus = await Permission.camera.request();
        if (cameraStatus.isDenied) {
          _showPermissionDialog(context.loc.camera);
          return;
        }
      }

      final XFile? image = await _picker.pickImage(
        source: source,
        imageQuality: 90,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (image != null) {
        debugPrint('Image selected: ${image.path}');
        await _cropAndUploadImage(File(image.path));
      } else {
        debugPrint('No image selected');
      }
    } catch (e) {
      showErrorSnackBar(
        context: context,
        message: context.loc.imageUploadFailed,
      );
    }
  }

  // Crop and upload the selected image
  Future<void> _cropAndUploadImage(File imageFile) async {
    try {
      debugPrint('Starting image cropping for: ${imageFile.path}');

      // Try using image_cropper first
      CroppedFile? croppedFile;
      try {
        croppedFile = await ImageCropper().cropImage(
          sourcePath: imageFile.path,
          aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1),
          aspectRatioPresets: [CropAspectRatioPreset.square],
          uiSettings: [
            AndroidUiSettings(
              toolbarTitle: context.loc.cropImage,
              toolbarColor: Theme.of(context).primaryColor,
              toolbarWidgetColor: Colors.white,
              initAspectRatio: CropAspectRatioPreset.square,
              lockAspectRatio: true,
            ),
            IOSUiSettings(
              title: context.loc.cropImage,
              aspectRatioLockEnabled: true,
              resetAspectRatioEnabled: false,
            ),
          ],
        );
      } catch (cropperError) {
        debugPrint('ImageCropper failed: $cropperError');
        debugPrint('Falling back to manual cropping...');

        // Fallback: Use manual cropping with image package
        croppedFile = await _manualCropImage(imageFile);
      }

      debugPrint('Cropping completed. Result: ${croppedFile?.path ?? 'null'}');

      if (croppedFile != null && croppedFile.path.isNotEmpty) {
        // Upload the cropped image
        context.read<ProfileAvatarBloc>().add(
              ProfileAvatarChanged(image: File(croppedFile.path)),
            );
      } else {
        // User cancelled cropping - no error message needed, just return
        debugPrint('Image cropping was cancelled by user');
      }
    } catch (e) {
      debugPrint('Cropping error: $e');
      showErrorSnackBar(
        context: context,
        message: context.loc.imageUploadFailed,
      );
    }
  }

  // Manual cropping fallback using image package
  Future<CroppedFile?> _manualCropImage(File imageFile) async {
    try {
      // Read the image
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image == null) {
        debugPrint('Failed to decode image');
        return null;
      }

      // Calculate square crop dimensions (center crop)
      final size = image.width < image.height ? image.width : image.height;
      final offsetX = (image.width - size) ~/ 2;
      final offsetY = (image.height - size) ~/ 2;

      // Crop to square
      final croppedImage = img.copyCrop(
        image,
        offsetX,
        offsetY,
        size,
        size,
      );

      // Resize to avatar size (512x512 for good quality)
      final resizedImage =
          img.copyResize(croppedImage, width: 512, height: 512);

      // Save the cropped image
      final tempDir = Directory.systemTemp;
      final croppedFile = File(
          '${tempDir.path}/cropped_avatar_${DateTime.now().millisecondsSinceEpoch}.jpg');
      await croppedFile.writeAsBytes(img.encodeJpg(resizedImage, quality: 90));

      debugPrint('Manual cropping completed: ${croppedFile.path}');

      // Return a CroppedFile-like object
      return CroppedFile(croppedFile.path);
    } catch (e) {
      debugPrint('Manual cropping failed: $e');
      return null;
    }
  }

  // Show permission dialog
  void _showPermissionDialog(String permissionType) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.loc.photoPermissionTitle),
        content: Text(context.loc.photoPermissionDescription),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(context.loc.cancel),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              openAppSettings();
            },
            child: Text(context.loc.ok),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ProfileAvatarBloc, ProfileAvatarState>(
      listener: (context, state) {
        if (state.status == ProfileAvatarStatus.failure &&
            state.hasServerError &&
            state.serverErrorMessage.isNotEmpty) {
          showErrorSnackBar(
            context: context,
            message: state.serverErrorMessage,
          );
        }

        if (state.status == ProfileAvatarStatus.success &&
            state.successMessage.isNotEmpty) {
          showSuccessSnackBar(
            context: context,
            message: state.successMessage.isNotEmpty
                ? state.successMessage
                : context.loc.imageUploadSuccess,
          );
          context.read<AuthenticationBloc>().add(AuthenticationUserRequested());
        }
      },
      child: SizedBox(
        child: BlocBuilder<ProfileAvatarBloc, ProfileAvatarState>(
          builder: (context, state) {
            return Column(
              children: [
                GestureDetector(
                  onTap: state.status == ProfileAvatarStatus.updating
                      ? null
                      : _showImageSourceBottomSheet,
                  child: Stack(
                    children: [
                      // Avatar with loading state
                      Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Theme.of(context).cardColor,
                          border: Border.all(
                            color:
                                Theme.of(context).primaryColor.withOpacity(0.2),
                            width: 2,
                          ),
                        ),
                        child: state.status == ProfileAvatarStatus.updating
                            ? const Center(
                                child: CircularProgressIndicator.adaptive(),
                              )
                            : BlocBuilder<AuthenticationBloc,
                                AuthenticationState>(
                                builder: (context, authState) {
                                  return ClipOval(
                                    child: Image.network(
                                      authState.user.profilePhotoUrl,
                                      width: 96,
                                      height: 96,
                                      fit: BoxFit.cover,
                                      errorBuilder:
                                          (context, error, stackTrace) {
                                        return Container(
                                          width: 96,
                                          height: 96,
                                          color: Colors.grey[200],
                                          child: Icon(
                                            HugeIcons.strokeRoundedUser,
                                            size: 40,
                                            color: Colors.grey[400],
                                          ),
                                        );
                                      },
                                      loadingBuilder:
                                          (context, child, loadingProgress) {
                                        if (loadingProgress == null) {
                                          return child;
                                        }
                                        return Container(
                                          width: 96,
                                          height: 96,
                                          color: Colors.grey[100],
                                          child: const Center(
                                            child: CircularProgressIndicator
                                                .adaptive(),
                                          ),
                                        );
                                      },
                                    ),
                                  );
                                },
                              ),
                      ),

                      // Camera icon overlay
                      if (state.status != ProfileAvatarStatus.updating)
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: Container(
                            width: 32,
                            height: 32,
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: Colors.white,
                                width: 2,
                              ),
                            ),
                            child: const Icon(
                              HugeIcons.strokeRoundedCamera01,
                              size: 16,
                              color: Colors.white,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                TextButton.icon(
                  onPressed: state.status == ProfileAvatarStatus.updating
                      ? null
                      : _showImageSourceBottomSheet,
                  icon: Icon(
                    HugeIcons.strokeRoundedImage01,
                    size: 16,
                    color: state.status == ProfileAvatarStatus.updating
                        ? Colors.grey
                        : Theme.of(context).primaryColor,
                  ),
                  label: Text(
                    state.status == ProfileAvatarStatus.updating
                        ? context.loc.uploadingImage
                        : context.loc.updatePhoto,
                    style: TextStyle(
                      color: state.status == ProfileAvatarStatus.updating
                          ? Colors.grey
                          : Theme.of(context).primaryColor,
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
