import 'package:flutter/material.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';

enum PhoneNumberValidationError { empty, length, invalid, startWithZero }

class PhoneNumberInput extends FormzInput<String, PhoneNumberValidationError> {
  const PhoneNumberInput.pure() : super.pure('');
  const PhoneNumberInput.dirty(super.value) : super.dirty();

  @override
  PhoneNumberValidationError? validator(String value) {
    if (value.isEmpty) {
      return PhoneNumberValidationError.empty;
    }

    if (value.length != 10) {
      return PhoneNumberValidationError.length;
    }

    if (value.endsWith('0000000')) {
      return PhoneNumberValidationError.invalid;
    }
    if (value.startsWith('0')) {
      return PhoneNumberValidationError.startWithZero;
    }
    return null;
  }

  String? validationErrorMessage(BuildContext context) {
    if (valid || pure) {
      return null;
    }
    if (error == PhoneNumberValidationError.empty) {
      return context.loc.validationRequired(context.loc.phone);
    }

    if (error == PhoneNumberValidationError.length) {
      return context.loc.phoneValidationDitigs;
    }
    if (error == PhoneNumberValidationError.startWithZero) {
      return context.loc.startWithZero;
    }

    if (error == PhoneNumberValidationError.invalid) {
      return context.loc.validationInvalid(context.loc.phone);
    }
    return null;
  }
}
