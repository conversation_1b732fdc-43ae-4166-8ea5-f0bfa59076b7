import 'package:equatable/equatable.dart';
import 'package:formz/formz.dart';

import 'package:goldenprizma/presentation/views/forms/auth/email_input.dart';

class ProfileForm extends Equatable with FormzMixin {
  const ProfileForm({
    this.emailInput = const EmailInput.pure(),
  });
  final EmailInput emailInput;

  @override
  List<FormzInput> get inputs => [
        emailInput,
      ];

  @override
  List<Object> get props => [
        emailInput,
      ];

  ProfileForm copyWith({
    EmailInput? emailInput,
  }) {
    return ProfileForm(
      emailInput: emailInput ?? this.emailInput,
    );
  }

  @override
  String toString() {
    return 'ProfileForm(emailInput: $emailInput)';
  }
}
