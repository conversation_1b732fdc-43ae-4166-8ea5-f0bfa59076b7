import 'package:flutter/material.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';

enum FullNameValidationError { empty }

class FullNameInput extends FormzInput<String, FullNameValidationError> {
  const FullNameInput.pure({String value = ''}) : super.pure(value);
  const FullNameInput.dirty(super.value) : super.dirty();

  @override
  FullNameValidationError? validator(String value) {
    if (value.isEmpty) {
      return FullNameValidationError.empty;
    }
    return null;
  }

  String? validationErrorMessage(BuildContext context) {
    if (valid || pure) {
      return null;
    }
    if (error == FullNameValidationError.empty) {
      return context.loc.validationRequired(context.loc.fullName);
    }
    return null;
  }
}
