import 'package:flutter/material.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';

enum CityValidationError { empty, max100Char, min10Char }

class CityField extends FormzInput<String, CityValidationError> {
  const CityField.pure() : super.pure('');
  const CityField.dirty([super.value = '']) : super.dirty();

  @override
  CityValidationError? validator(String value) {
    if (value.isNotEmpty == true) {
      if (value.length > 100) {
        return CityValidationError.max100Char;
      }
      return null;
    }
    return CityValidationError.empty;
  }

  String? validationErrorMessage(BuildContext context) {
    if (valid || pure) {
      return null;
    }

    if (error == CityValidationError.max100Char) {
      return context.loc.validationMaxChar(100);
    }

    return context.loc.validationRequired(context.loc.city);
  }
}
