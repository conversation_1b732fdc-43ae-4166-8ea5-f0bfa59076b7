import 'package:flutter/material.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';

enum NoteValidationError { empty, max500Char, min10Char }

class NoteField extends FormzInput<String, NoteValidationError> {
  const NoteField.pure() : super.pure('');
  const NoteField.dirty([super.value = '']) : super.dirty();

  @override
  NoteValidationError? validator(String value) {
    if (value.isNotEmpty == true) {
      if (value.length > 500) {
        return NoteValidationError.max500Char;
      }
      if (value.length < 10) {
        return NoteValidationError.min10Char;
      }
      return null;
    }
    return NoteValidationError.empty;
  }

  String? validationErrorMessage(BuildContext context) {
    if (valid || pure) {
      return null;
    }

    if (error == NoteValidationError.min10Char) {
      return context.loc.validationMinChar(10);
    }

    if (error == NoteValidationError.max500Char) {
      return context.loc.validationMaxChar(500);
    }

    return context.loc.validationRequired(context.loc.note);
  }
}
