import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/domain/request_deliveries/bloc/request_delivery_bloc.dart';
import 'package:goldenprizma/domain/request_deliveries/bloc/request_delivery_form_bloc.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/widgets/success_dialog.dart';

class RequestDeliveryForm extends StatelessWidget {
  RequestDeliveryForm({super.key});

  final TextEditingController cityTextEditingController =
      TextEditingController();
  final TextEditingController textEditingController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return BlocListener<RequestDeliveryFormBloc, RequestDeliveryFormState>(
      listener: (BuildContext context, state) {
        if (state.status.isSubmissionFailure &&
            state.errorMessage.isNotEmpty &&
            state.hasServerError) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text(state.errorMessage),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ));
        }

        if (state.status.isSubmissionSuccess) {
          // reset form
          context
              .read<RequestDeliveryFormBloc>()
              .add(const RequestDeliveryFormReset());
          textEditingController.clear();
          Navigator.of(context).pop();

          // Show success dialog
          showDialog(
              context: context,
              builder: (context) {
                return SuccessDialog(message: state.successMessage);
              });
          // add created request from api response instead of refreshing the list
          context
              .read<RequestDeliveryBloc>()
              .add(RequestDeliveryAppend(requestDelivery: state.delivery));
        }
      },
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            _cityInputField(context),
            const SizedBox(height: 16),
            _noteInputField(context),
            const SizedBox(height: 16),
            _createButton(context),
          ],
        ),
      ),
    );
  }

  Widget _cityInputField(BuildContext context) {
    return BlocBuilder<RequestDeliveryFormBloc, RequestDeliveryFormState>(
      buildWhen: (previous, current) => current.city != previous.city,
      builder: (context, state) {
        return TextFormField(
          controller: cityTextEditingController,
          autofocus: true,
          decoration: InputDecoration(
            border: const OutlineInputBorder(),
            labelText: context.loc.city,
            errorText: state.city.validationErrorMessage(context),
          ),
          onChanged: (value) {
            context
                .read<RequestDeliveryFormBloc>()
                .add(RequestDeliveryFormCityChanged(city: value));
          },
        );
      },
    );
  }

  Widget _noteInputField(BuildContext context) {
    return BlocBuilder<RequestDeliveryFormBloc, RequestDeliveryFormState>(
      buildWhen: (previous, current) => current.note != previous.note,
      builder: (context, state) {
        return TextFormField(
          controller: textEditingController,
          autofocus: false,
          decoration: InputDecoration(
            border: const OutlineInputBorder(),
            labelText: context.loc.note,
            errorText: state.note.validationErrorMessage(context),
          ),
          minLines: 6,
          maxLines: 12,
          maxLength: 75,
          onChanged: (value) {
            context
                .read<RequestDeliveryFormBloc>()
                .add(RequestDeliveryFormNoteChanged(note: value));
          },
        );
      },
    );
  }

  Widget _createButton(BuildContext context) {
    return BlocBuilder<RequestDeliveryFormBloc, RequestDeliveryFormState>(
      buildWhen: (previous, current) => previous.status != current.status,
      builder: (context, state) {
        return state.status.isSubmissionInProgress
            ? const CircularProgressIndicator.adaptive()
            : MaterialButton(
                key: const Key('requestedDeliveryForm_submit_button'),
                onPressed: state.status.isValidated
                    ? () {
                        context
                            .read<RequestDeliveryFormBloc>()
                            .add(const RequestDeliveryFormSubmitted());
                      }
                    : () {
                        context.read<RequestDeliveryFormBloc>().add(
                            RequestDeliveryFormNoteChanged(
                                note: state.note.value));
                      },
                color: AppColors.primaryColor,
                minWidth: double.infinity,
                height: 50,
                elevation: 0,
                child: Text(
                  context.loc.createRequestDelivery,
                  style: const TextStyle(color: Colors.white),
                ),
              );
      },
    );
  }
}
