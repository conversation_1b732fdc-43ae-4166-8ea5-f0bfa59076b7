import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/domain/auth/bloc/login_bloc.dart';
import 'package:goldenprizma/domain/auth/bloc/update_password_bloc/update_password_bloc.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/helpers/custom_dialogs.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/auth/login_screen.dart';
import 'package:goldenprizma/presentation/views/forms/auth/password_input.dart';
import 'package:ionicons/ionicons.dart';
import 'package:provider/provider.dart';

class UpdatePasswordScreen extends StatefulWidget {
  const UpdatePasswordScreen(
      {super.key,
      required this.token,
      required this.otpCode,
      required this.phoneNumber,
      required this.countryCode});

  final String token;
  final String otpCode;
  final String phoneNumber;
  final String countryCode;

  @override
  _UpdatePasswordScreenState createState() => _UpdatePasswordScreenState();
}

class _UpdatePasswordScreenState extends State<UpdatePasswordScreen> {
  bool _passwordIsVisible = false;
  final TextEditingController _passwordConfirmationController =
      TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final double kHorizontalPadding = 24.0;
  UpdatePasswordBloc updatePasswordBloc = UpdatePasswordBloc();
  final GlobalKey<FormState> _fromKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => updatePasswordBloc,
      child: Builder(builder: (context) {
        return BlocListener<UpdatePasswordBloc, UpdatePasswordState>(
          listener: (context, state) {
            if (state.status == UpdatePasswordStatus.request) {
              showLoadingAlert(context: context);
            } else if (state.status == UpdatePasswordStatus.failure) {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.errorMessage),
                  backgroundColor: Colors.red,
                ),
              );
            } else if (state.status == UpdatePasswordStatus.updated) {
              Navigator.of(context).pop();
              showSuccessDialogWithAutoHide(
                context: context,
                message: state.successMessage,
                autoClose: true,
              ).then((value) {
                if (context.mounted) {
                  return Navigator.pushNamedAndRemoveUntil(
                      context, LoginScreen.routeName, (route) => false);
                }
              });
            }
          },
          child: Scaffold(
            body: SingleChildScrollView(
              child: SizedBox(
                height: MediaQuery.of(context).size.height,
                width: double.infinity,
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 32.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: double.infinity,
                        height: MediaQuery.of(context).size.height * 0.25,
                        child: Stack(
                          children: [
                            ClipPath(
                              clipper: OvalCliper(),
                              child: Container(
                                width: double.infinity,
                                color: Provider.of<AppProvider>(context)
                                        .isDarkMode(context)
                                    ? Colors.black
                                    : Colors.grey.shade900,
                                height:
                                    MediaQuery.of(context).size.height * 0.25,
                              ),
                            ),
                            Center(
                              child: Image(
                                image:
                                    const AssetImage('assets/images/logo.png'),
                                width:
                                    MediaQuery.of(context).size.height * 0.12,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 40),
                      Text(
                        context.loc.updatePassword,
                        style: const TextStyle(
                            fontSize: 24, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 64),
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: kHorizontalPadding),
                          child: Form(
                            key: _fromKey,
                            child: Column(
                              children: [
                                const SizedBox(height: 16),
                                _passwordField(
                                    validator: (value) {
                                      return PasswordInput.dirty(value ?? "")
                                          .validationErrorMessage(context);
                                    },
                                    controller: _passwordController,
                                    title: context.loc.passwordPlacholder),
                                const SizedBox(height: 16),
                                _passwordField(
                                    validator: (value) {
                                      if (_passwordConfirmationController
                                              .text !=
                                          _passwordController.text) {
                                        return "It doesn't match the password";
                                      }
                                      return PasswordInput.dirty(value ?? "")
                                          .validationErrorMessage(context);
                                    },
                                    controller: _passwordConfirmationController,
                                    title:
                                        context.loc.confirmPasswordPlacholder),
                                const SizedBox(height: 32),
                                _signInButton(),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget _passwordField(
      {required TextEditingController controller,
      required String title,
      required String? Function(String?)? validator}) {
    return TextFormField(
        keyboardType: TextInputType.visiblePassword,
        controller: controller,
        obscureText: !_passwordIsVisible,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        decoration: InputDecoration(
          prefixIcon: const Icon(
            Ionicons.lock_closed,
            color: Colors.grey,
          ),
          suffixIcon: IconButton(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            tooltip: 'Toggle password visibility',
            icon: _passwordIsVisible
                ? const Icon(Ionicons.eye_off)
                : const Icon(Ionicons.eye),
            onPressed: () {
              setState(() {
                _passwordIsVisible = !_passwordIsVisible;
              });
            },
          ),
          hintText: title,
          filled: true,
        ),
        validator: validator);
  }

  Widget _signInButton() {
    return BlocBuilder<LoginBloc, LoginState>(
      builder: (context, state) {
        if (state.status.isSubmissionInProgress) {
          return const Center(
            child: CircularProgressIndicator.adaptive(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
            ),
          );
        }
        return MaterialButton(
          onPressed: () {
            if (_fromKey.currentState?.validate() != true) {
              return;
            }
            updatePasswordBloc.add(UpdatePasswordRequested(
                code: widget.otpCode,
                countryCode: widget.countryCode,
                password: _passwordController.text,
                phoneNumber: widget.phoneNumber,
                token: widget.token,
                passwordConfirmation: _passwordConfirmationController.text));
          },
          color: AppColors.primaryColor,
          minWidth: MediaQuery.of(context).size.width / 2,
          elevation: 0,
          child: Text(
            context.loc.save,
            style: const TextStyle(color: Colors.white),
          ),
        );
      },
    );
  }
}

showLoadingAlert({required BuildContext context}) async {
  showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return const PopScope(
          canPop: false,
          child: Center(
            child: CircularProgressIndicator(),
          ),
        );
      });
}
