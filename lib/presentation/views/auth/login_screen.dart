import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/domain/auth/bloc/authentication_bloc.dart';
import 'package:goldenprizma/domain/auth/bloc/login_bloc.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/helpers/custom_snackbars.dart';
import 'package:goldenprizma/helpers/logger.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/screens/check_auth_screen.dart';
import 'package:goldenprizma/presentation/views/screens/otp_screen.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:ionicons/ionicons.dart';
import 'package:provider/provider.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  static const String routeName = '/login';
  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  bool _passwordIsVisible = false;
  final TextEditingController _usernameController =
      TextEditingController(text: kDebugMode ? "**********" : "");
  final TextEditingController _passwordController =
      TextEditingController(text: kDebugMode ? "**********" : "");
  final double kHorizontalPadding = 24.0;
  @override
  Widget build(BuildContext context) {
    return BlocListener<LoginBloc, LoginState>(
      listener: (context, state) {
        if (state.hasServerErrror &&
            state.status.isSubmissionFailure &&
            state.serverErrorMessage.isNotEmpty) {
          showErrorSnackBar(
              context: context, message: state.serverErrorMessage);
        }

        if (state.status.isSubmissionSuccess) {
          context.read<AuthenticationBloc>().add(AuthenticationUserRequested());
          Navigator.of(context).pushNamedAndRemoveUntil(
              CheckAuthScreen.routeName, (route) => false);
        }
      },
      child: Scaffold(
        backgroundColor: Provider.of<AppProvider>(context).isDarkMode(context)
            ? AppColors.darkBodyColor
            : Colors.grey.shade50,
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          systemOverlayStyle: SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness:
                Provider.of<AppProvider>(context).isDarkMode(context)
                    ? Brightness.light
                    : Brightness.dark,
          ),
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: Navigator.of(context).canPop()
              ? Container(
                  margin: const EdgeInsets.only(left: 16, top: 16),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                      borderRadius: BorderRadius.circular(12),
                      child: Container(
                        width: 36,
                        height: 36,
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.2),
                            width: 1,
                          ),
                        ),
                        child: const Icon(
                          Icons.arrow_back,
                          size: 22,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                )
              : null,
        ),
        body: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: LayoutBuilder(
            builder: (context, constraints) {
              final isDarkMode =
                  Provider.of<AppProvider>(context).isDarkMode(context);
              final primaryColor = Theme.of(context).primaryColor;

              return SizedBox(
                height: MediaQuery.of(context).size.height,
                width: double.infinity,
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 40.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Flexible(
                        flex: 3,
                        child: SizedBox(
                          width: double.infinity,
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              // Decorative background elements
                              Positioned.fill(
                                child: Container(
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                      colors: isDarkMode
                                          ? [
                                              AppColors.appbarDarkColor,
                                              AppColors.primaryColor
                                                  .withOpacity(0.6),
                                              AppColors.boxDarkColor
                                                  .withOpacity(0.2),
                                            ]
                                          : [
                                              AppColors.primaryColor,
                                              AppColors.primaryColor
                                                  .withOpacity(0.6),
                                              Colors.white.withOpacity(0.2),
                                            ],
                                    ),
                                  ),
                                ),
                              ),

                              // Decorative shapes
                              Positioned(
                                top: 20,
                                right: -50,
                                child: Container(
                                  width: 150,
                                  height: 150,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.white.withOpacity(0.1),
                                  ),
                                ),
                              ),
                              Positioned(
                                top: 80,
                                left: -30,
                                child: Container(
                                  width: 100,
                                  height: 100,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.white.withOpacity(0.1),
                                  ),
                                ),
                              ),
                              // Logo with animation
                              Center(
                                child: TweenAnimationBuilder<double>(
                                  tween: Tween<double>(begin: 0.8, end: 1.0),
                                  duration: const Duration(milliseconds: 800),
                                  curve: Curves.easeOutQuad,
                                  builder: (context, value, child) {
                                    return Transform.scale(
                                      scale: value,
                                      child: Container(
                                        padding: EdgeInsets.all(
                                            MediaQuery.of(context).size.height >
                                                    850
                                                ? 30
                                                : 12),
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: Colors.white.withOpacity(0.3),
                                          border: Border.all(
                                            color:
                                                Colors.white.withOpacity(0.4),
                                            width: 1,
                                          ),
                                        ),
                                        child: Image(
                                          image: const AssetImage(
                                              'assets/images/logo.png'),
                                          width: MediaQuery.of(context)
                                                      .size
                                                      .height >
                                                  850
                                              ? 100
                                              : 40,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Welcome text with animation
                      TweenAnimationBuilder<double>(
                        tween: Tween<double>(begin: 0, end: 1),
                        duration: const Duration(milliseconds: 600),
                        builder: (context, value, child) {
                          return Opacity(
                            opacity: value,
                            child: Transform.translate(
                              offset: Offset(0, 20 * (1 - value)),
                              child: Text(
                                context.loc.signIn,
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  foreground: Paint()
                                    ..shader = LinearGradient(
                                      colors: [
                                        primaryColor,
                                        primaryColor.withOpacity(0.8),
                                      ],
                                    ).createShader(
                                        const Rect.fromLTWH(0, 0, 200, 70)),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 24),

                      // Form container with subtle background
                      TweenAnimationBuilder<double>(
                        tween: Tween<double>(begin: 0, end: 1),
                        duration: const Duration(milliseconds: 800),
                        curve: Curves.easeOutQuint,
                        builder: (context, value, child) {
                          return Opacity(
                              opacity: value,
                              child: Transform.translate(
                                offset: Offset(0, 30 * (1 - value)),
                                child: Padding(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: kHorizontalPadding,
                                  ),
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Provider.of<AppProvider>(context)
                                              .isDarkMode(context)
                                          ? AppColors.boxDarkColor
                                              .withOpacity(0.6)
                                          : Colors.white,
                                      borderRadius: BorderRadius.circular(16),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.05),
                                          blurRadius: 20,
                                          spreadRadius: 0,
                                          offset: const Offset(0, 5),
                                        ),
                                      ],
                                    ),
                                    padding: const EdgeInsets.all(24.0),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        _usernameField(),
                                        const SizedBox(height: 24),
                                        _passwordField(),
                                        const SizedBox(height: 0),
                                        Directionality(
                                          textDirection: TextDirection.ltr,
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.end,
                                            children: [
                                              TextButton(
                                                onPressed: () {
                                                  Navigator.of(context)
                                                      .pushNamed(
                                                          OTPScreen.routeName,
                                                          arguments: true);
                                                },
                                                style: TextButton.styleFrom(
                                                  foregroundColor:
                                                      Theme.of(context)
                                                          .primaryColor,
                                                  padding: const EdgeInsets
                                                      .symmetric(horizontal: 8),
                                                  visualDensity:
                                                      VisualDensity.compact,
                                                ),
                                                child: Text(
                                                  context.loc.forgetPassword,
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w500,
                                                    fontSize: 14,
                                                    color: Theme.of(context)
                                                        .primaryColor
                                                        .withOpacity(0.9),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        const SizedBox(height: 24),
                                        _signInButton(),
                                      ],
                                    ),
                                  ),
                                ),
                              ));
                        },
                      ),

                      const SizedBox(height: 40),

                      // Register button section
                      TweenAnimationBuilder<double>(
                        tween: Tween<double>(begin: 0, end: 1),
                        duration: const Duration(milliseconds: 800),
                        curve: Curves.easeOut,
                        builder: (context, value, child) {
                          return Opacity(
                            opacity: value,
                            child: Column(
                              children: [
                                _registerButton(),
                                const SizedBox(height: 12),
                              ],
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  final PhoneNumber _phoneNumber = PhoneNumber(isoCode: 'IQ');
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  Widget _usernameField() {
    final isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    return BlocBuilder<LoginBloc, LoginState>(
      builder: (context, state) {
        return Form(
          key: formKey,
          child: Directionality(
            textDirection: TextDirection.ltr,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 4, bottom: 8),
                  child: Row(
                    children: [
                      Icon(
                        Ionicons.call_outline,
                        size: 16,
                        color:
                            isDarkMode ? Colors.white70 : Colors.grey.shade700,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Phone Number',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: isDarkMode
                              ? Colors.white70
                              : Colors.grey.shade700,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.03),
                        blurRadius: 10,
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: InternationalPhoneNumberInput(
                    onInputChanged: (PhoneNumber number) async {
                      logger(
                          "number ${_phoneNumber.dialCode} ${_phoneNumber.phoneNumber}");
                      context.read<LoginBloc>().add(LoginUsernameChanged(
                          duialCode: number.dialCode ?? "",
                          phone: (number.phoneNumber ?? "")
                              .toString()
                              .replaceAll(number.dialCode ?? "", '')));
                    },
                    selectorConfig: const SelectorConfig(
                      selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                      setSelectorButtonAsPrefixIcon: true,
                      leadingPadding: 16.0,
                    ),
                    ignoreBlank: true,
                    hintText: '7xx-xxx-xxxx',
                    spaceBetweenSelectorAndTextField: 0,
                    autoValidateMode: AutovalidateMode.onUserInteraction,
                    initialValue: _phoneNumber,
                    formatInput: false,
                    keyboardType: TextInputType.phone,
                    inputBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: isDarkMode
                            ? Colors.grey.shade700
                            : Colors.grey.shade300,
                        width: 1.5,
                      ),
                    ),
                    maxLength: 10,
                    textFieldController: _usernameController,
                    inputDecoration: InputDecoration(
                      errorText: state.phone.validationErrorMessage(context),
                      hintText: '7xx-xxx-xxxx',
                      hintStyle: TextStyle(
                        fontSize: 14,
                        color: isDarkMode
                            ? Colors.grey.shade500
                            : Colors.grey.shade400,
                      ),
                      filled: true,
                      fillColor: isDarkMode
                          ? Colors.grey.shade900.withOpacity(0.3)
                          : Colors.grey.shade50,
                      contentPadding: const EdgeInsets.symmetric(
                          vertical: 16, horizontal: 12),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: Theme.of(context).primaryColor,
                          width: 1.5,
                        ),
                      ),
                      errorBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(
                          color: Colors.red,
                          width: 1.5,
                        ),
                      ),
                      focusedErrorBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(
                          color: Colors.red,
                          width: 1.5,
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: isDarkMode
                              ? Colors.grey.shade700
                              : Colors.grey.shade300,
                          width: 1.5,
                        ),
                      ),
                    ),
                    selectorTextStyle: TextStyle(
                      color: isDarkMode ? Colors.white : Colors.black87,
                      fontSize: 14,
                    ),
                    textStyle: TextStyle(
                      color: isDarkMode ? Colors.white : Colors.black87,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
  // Widget _usernameField() {
  //   return BlocBuilder<LoginBloc, LoginState>(
  //     builder: (context, state) {
  //       return TextFormField(
  //         controller: _usernameController,
  //         keyboardType: TextInputType.emailAddress,
  //         decoration: InputDecoration(
  //           prefixIcon: const Icon(
  //             Icons.phone,
  //             color: Colors.grey,
  //           ),
  //           hintText: "7xxxxxxxxx",
  //           filled: true,
  //           errorText: state.phone.validationErrorMessage(context),
  //         ),
  //         onChanged: (value) => context
  //             .read<LoginBloc>()
  //             .add(LoginUsernameChanged(username: value)),
  //       );
  //     },
  //   );
  // }

  Widget _passwordField() {
    final isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    return BlocBuilder<LoginBloc, LoginState>(
      builder: (context, state) {
        return Directionality(
          textDirection: TextDirection.ltr,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 4, bottom: 8),
                child: Row(
                  children: [
                    Icon(
                      Ionicons.lock_closed_outline,
                      size: 16,
                      color: isDarkMode ? Colors.white70 : Colors.grey.shade700,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Password',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color:
                            isDarkMode ? Colors.white70 : Colors.grey.shade700,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.03),
                      blurRadius: 10,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: TextFormField(
                  keyboardType: TextInputType.visiblePassword,
                  controller: _passwordController,
                  obscureText: !_passwordIsVisible,
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black87,
                    fontSize: 14,
                  ),
                  decoration: InputDecoration(
                    prefixIcon: Icon(
                      Ionicons.lock_closed,
                      color: isDarkMode
                          ? Colors.grey.shade500
                          : Colors.grey.shade600,
                      size: 18,
                    ),
                    suffixIcon: IconButton(
                      splashColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      tooltip: 'Toggle password visibility',
                      icon: _passwordIsVisible
                          ? Icon(
                              Ionicons.eye_off,
                              color: isDarkMode
                                  ? Colors.grey.shade500
                                  : Colors.grey.shade600,
                              size: 18,
                            )
                          : Icon(
                              Ionicons.eye,
                              color: isDarkMode
                                  ? Colors.grey.shade500
                                  : Colors.grey.shade600,
                              size: 18,
                            ),
                      onPressed: () {
                        setState(() {
                          _passwordIsVisible = !_passwordIsVisible;
                        });
                      },
                    ),
                    hintText: context.loc.passwordPlacholder,
                    hintStyle: TextStyle(
                      fontSize: 14,
                      color: isDarkMode
                          ? Colors.grey.shade500
                          : Colors.grey.shade400,
                    ),
                    filled: true,
                    fillColor: isDarkMode
                        ? Colors.grey.shade900.withOpacity(0.3)
                        : Colors.grey.shade50,
                    contentPadding: const EdgeInsets.symmetric(
                        vertical: 16, horizontal: 12),
                    errorText: state.password.validationErrorMessage(context),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Theme.of(context).primaryColor,
                        width: 1.5,
                      ),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Colors.red,
                        width: 1.5,
                      ),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Colors.red,
                        width: 1.5,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: isDarkMode
                            ? Colors.grey.shade700
                            : Colors.grey.shade300,
                        width: 1.5,
                      ),
                    ),
                  ),
                  onChanged: (value) => context
                      .read<LoginBloc>()
                      .add(LoginPasswordChanged(password: value)),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _signInButton() {
    return BlocBuilder<LoginBloc, LoginState>(
      builder: (context, state) {
        final isDarkMode =
            Provider.of<AppProvider>(context).isDarkMode(context);
        final primaryColor = Theme.of(context).primaryColor;

        return Container(
          width: double.infinity,
          height: 55,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(14),
            boxShadow: [
              BoxShadow(
                color: primaryColor.withOpacity(0.4),
                blurRadius: 12,
                spreadRadius: 0,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: state.status.isSubmissionInProgress
              ? Center(
                  child: SizedBox(
                    width: 30,
                    height: 30,
                    child: CircularProgressIndicator.adaptive(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        isDarkMode ? Colors.white : primaryColor,
                      ),
                      strokeWidth: 2.5,
                    ),
                  ),
                )
              : ElevatedButton(
                  onPressed: () {
                    if (formKey.currentState!.validate() != true) {
                      return;
                    }
                    context.read<LoginBloc>()
                      ..add(LoginUsernameChanged(
                        duialCode: state.dialCode,
                        phone: _usernameController.text,
                      ))
                      ..add(LoginPasswordChanged(
                          password: _passwordController.text))
                      ..add(const LoginSubmitted());
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: primaryColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(14),
                    ),
                    elevation: 0,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Ionicons.log_in_outline,
                        size: 18,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        context.loc.signIn,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ],
                  ),
                ),
        );
      },
    );
  }

  Widget _registerButton() {
    return BlocBuilder<LoginBloc, LoginState>(
      builder: (context, state) {
        final isDarkMode =
            Provider.of<AppProvider>(context).isDarkMode(context);
        final primaryColor = Theme.of(context).primaryColor;

        return Container(
          margin: EdgeInsets.symmetric(horizontal: kHorizontalPadding),
          decoration: BoxDecoration(
            color: isDarkMode
                ? Colors.grey.shade900.withOpacity(0.3)
                : Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade300,
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.03),
                blurRadius: 10,
                spreadRadius: 0,
              ),
            ],
          ),
          child: TextButton(
            onPressed: state.status.isSubmissionInProgress
                ? null
                : () {
                    Navigator.of(context).pushNamed('/otp', arguments: false);
                  },
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              foregroundColor: primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  context.loc.dontHaveAccount,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: isDarkMode
                        ? Colors.grey.shade300
                        : Colors.grey.shade700,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  context.loc.register,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: primaryColor,
                  ),
                ),
                const SizedBox(width: 4),
                Icon(
                  Ionicons.arrow_forward_circle_outline,
                  size: 16,
                  color: primaryColor,
                )
              ],
            ),
          ),
        );
      },
    );
  }
}

class OvalCliper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    var path = Path();
    path.lineTo(0, size.height - 50);
    path.quadraticBezierTo(
        size.width / 2, size.height + 50, size.width + 50, size.height - 60);
    path.lineTo(size.width, 0);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return true;
  }
}
