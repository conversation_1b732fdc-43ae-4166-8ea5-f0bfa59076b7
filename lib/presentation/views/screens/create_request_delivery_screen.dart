import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/request_deliveries/bloc/request_delivery_bloc.dart';
import 'package:goldenprizma/domain/request_deliveries/bloc/request_delivery_form_bloc.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/presentation/views/forms/request_delivery/request_delivery_form.dart';
import 'package:ionicons/ionicons.dart';

class CreateRequestDeliveryScreen extends StatelessWidget {
  const CreateRequestDeliveryScreen(this._bloc, {super.key});

  final RequestDeliveryBloc _bloc;

  static PageRouteBuilder pageRoute(RequestDeliveryBloc bloc) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) =>
          CreateRequestDeliveryScreen(bloc),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(0.0, 1.0);
        const end = Offset.zero;
        var curve = Curves.ease;
        var tween =
            Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
        final offsetAnimation = animation.drive(tween);
        return SlideTransition(
          position: offsetAnimation,
          child: child,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.loc.createRequestDelivery),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Ionicons.close),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (_) =>
                RequestDeliveryFormBloc(repository: _bloc.repository),
          ),
          BlocProvider<RequestDeliveryBloc>.value(value: _bloc),
        ],
        child: RequestDeliveryForm(),
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: Colors.red,
        elevation: 0,
        onPressed: () {
          Navigator.of(context).pop();
        },
        child: const Icon(
          Ionicons.close,
          color: Colors.white,
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }
}
