import 'dart:ui' as ui;

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/domain/support/bloc/reply_bloc.dart';
import 'package:goldenprizma/domain/support/bloc/reply_form_bloc.dart';
import 'package:goldenprizma/domain/support/models/reply.dart';
import 'package:goldenprizma/domain/support/models/ticket.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/main.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/forms/support/reply_form.dart';
import 'package:goldenprizma/repositories/support_repository.dart';
import 'package:ionicons/ionicons.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';

class TicketDetailScreen extends StatelessWidget {
  TicketDetailScreen({required this.ticket, super.key});

  static MaterialPageRoute pageRoute({required Ticket ticket}) {
    return MaterialPageRoute(
      builder: (context) => BlocProvider(
        create: (context) =>
            ReplyBloc(supportRepository: getIt.get<SupportRepository>())
              ..add(ReplyRequestLoad(ticketId: ticket.id)),
        child: TicketDetailScreen(ticket: ticket),
      ),
    );
  }

  final SupportRepository supportRepository = getIt.get<SupportRepository>();

  final Ticket ticket;

  @override
  Widget build(BuildContext context) {
    var t = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(t!.ticketWithId("${ticket.id}")),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Ionicons.close),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: Column(
        children: [
          _buildHeader(ticket, context),
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(12.0),
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    context.loc.replies,
                    style: const TextStyle(
                        fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 12),
                  BlocBuilder<ReplyBloc, ReplyState>(
                    builder: (context, ReplyState state) {
                      if (state.status == ReplyStatus.initial) {
                        return Expanded(child: _shimmerList(context));
                      }
                      if (state.status == ReplyStatus.failure) {
                        return Expanded(child: _buildListError(context));
                      }
                      return Expanded(
                          child: _buildReplyList(state.replies, context));
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(context),
    );
  }

  Widget _buildHeader(Ticket ticket, context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Card(
        child: Container(
          padding: const EdgeInsets.all(8.8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    ticket.subject,
                    style: const TextStyle(
                        fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const Spacer(),
                  _buildStatusUI(ticket, context),
                ],
              ),
              const SizedBox(height: 8.0),
              Text(
                ticket.description,
                overflow: TextOverflow.clip,
              ),
              const SizedBox(height: 12.0),
              Text(
                ticket.createdAt,
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Container _buildStatusUI(Ticket ticket, context) {
    final String status = ticket.getStatus(context);

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      decoration: BoxDecoration(
        color:
            ticket.status == 'resolved' ? Colors.green : AppColors.primaryColor,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        status,
        style: const TextStyle(fontSize: 10, color: Colors.white),
      ),
    );
  }

  Widget _buildListError(context) {
    return Center(
        child: Text(AppLocalizations.of(context)!.generalErrorMessage));
  }

  Widget _buildReplyList(List<TicketReply> replies, BuildContext context) {
    return ListView.separated(
      itemBuilder: (context, int index) {
        return _replyCard(replies[index], context);
      },
      separatorBuilder: (_, __) => const SizedBox(height: 4),
      itemCount: replies.length,
    );
  }

  Widget _replyCard(TicketReply reply, BuildContext context) {
    bool isMe = reply.author.toLowerCase() == 'me';
    final isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        textDirection: isMe ? ui.TextDirection.rtl : ui.TextDirection.ltr,
        children: [
          _circularAvatar(reply.authorAvatar),
          const SizedBox(width: 8.0),
          Flexible(
            child: Card(
              color: isMe
                  ? isDarkMode
                      ? Colors.transparent
                      : Colors.grey[400]
                  : Theme.of(context).cardColor,
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
                child: Column(
                  crossAxisAlignment:
                      isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
                  children: [
                    Text(
                      isMe ? "Me" : "Support",
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      reply.createdAt,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    const SizedBox(height: 8),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4.0),
                      child: Text(reply.content, overflow: TextOverflow.clip),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _circularAvatar(image) {
    if (kDebugMode) {
      return Container(
        width: 40,
        height: 40,
        decoration: const BoxDecoration(
          shape: BoxShape.circle,
        ),
        child: const Icon(Icons.error),
      );
    }
    return CachedNetworkImage(
      imageUrl: image,
      imageBuilder: (context, imageProvider) => Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          image: DecorationImage(
            image: imageProvider,
            fit: BoxFit.cover,
          ),
        ),
      ),
      placeholder: (context, url) => Container(
        width: 40,
        height: 40,
        decoration: const BoxDecoration(
          shape: BoxShape.circle,
        ),
        child: const Center(
          child: CircularProgressIndicator.adaptive(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
          ),
        ),
      ),
      errorWidget: (context, url, error) => Container(
        width: 40,
        height: 40,
        decoration: const BoxDecoration(
          shape: BoxShape.circle,
        ),
        child: const Icon(Icons.error),
      ),
    );
  }

  FloatingActionButton _buildFloatingActionButton(BuildContext context) {
    return FloatingActionButton(
      onPressed: () {
        if (!ticket.canReply) {
          ScaffoldMessenger.of(context).hideCurrentSnackBar();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(context.loc.waitSupportMessage),
              backgroundColor: Colors.blue,
            ),
          );
          return;
        }
        showModalBottomSheet(
            context: context,
            useRootNavigator: true,
            backgroundColor: Colors.transparent,
            isScrollControlled: true,
            builder: (BuildContext context) {
              return SafeArea(
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 64),
                  width: double.infinity,
                  height: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    color: Provider.of<AppProvider>(context).isDarkMode(context)
                        ? AppColors.boxDarkColor
                        : Colors.white,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const Icon(
                        Ionicons.chevron_down,
                        size: 28,
                      ),
                      const SizedBox(height: 16),
                      BlocProvider(
                        create: (context) => ReplyFormBloc(
                          supportRepository: getIt.get<SupportRepository>(),
                        ),
                        child: ReplyForm(ticketId: ticket.id),
                      )
                    ],
                  ),
                ),
              );
            });
      },
      tooltip: "Add a new reply",
      child: const Icon(
        Ionicons.add,
        color: Colors.white,
      ),
    );
  }

  Widget _shimmerList(context) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    return Padding(
      padding: const EdgeInsets.only(top: 16.0),
      child: Shimmer.fromColors(
        baseColor: isDarkMode ? AppColors.boxDarkColor : Colors.grey.shade300,
        highlightColor: isDarkMode ? Colors.black45 : Colors.grey.shade100,
        enabled: true,
        child: ListView.separated(
          itemBuilder: (_, __) => const Card(
              child: SizedBox(
            width: double.infinity,
            height: 80,
          )),
          itemCount: 8,
          separatorBuilder: (BuildContext context, int index) {
            return const Divider(height: 4);
          },
        ),
      ),
    );
  }
}
