import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/orders/bloc/order_bloc.dart';
import 'package:goldenprizma/presentation/widgets/order_filter_sheet.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';

class TestOrderScreen extends StatelessWidget {
  const TestOrderScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Orders with filters'),
        elevation: 0,
      ),
      body: Center(
        child: BlocBuilder<OrderBloc, OrderState>(
          builder: (blocContext, state) {
            return MaterialButton(
              onPressed: () => showCupertinoModalBottomSheet(
                expand: false,
                barrierColor: Colors.black.withOpacity(0.6),
                useRootNavigator: true,
                context: context,
                builder: (context) => BlocProvider.value(
                  value: BlocProvider.of<OrderBloc>(blocContext),
                  child: const OrderFilterSheet(),
                ),
              ),
              child: const Text('Filters'),
            );
          },
        ),
      ),
    );
  }
}
