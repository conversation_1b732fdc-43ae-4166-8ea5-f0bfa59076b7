// ignore_for_file: library_private_types_in_public_api
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/auth/bloc/authentication_bloc.dart';
import 'package:goldenprizma/domain/orders/bloc/order_bloc.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/presentation/views/screens/home_screen.dart';
import 'package:goldenprizma/presentation/views/screens/notifications_screen.dart';
import 'package:goldenprizma/presentation/views/screens/order_detail_screen.dart';
import 'package:goldenprizma/presentation/views/screens/support_screen.dart';
import 'package:goldenprizma/presentation/views/screens/wallet_screen.dart';
import 'package:provider/provider.dart';

class CheckAuthScreen extends StatefulWidget {
  const CheckAuthScreen({super.key});
  static const routeName = '/check_auth';
  @override
  _CheckAuthScreenState createState() => _CheckAuthScreenState();
}

class _CheckAuthScreenState extends State<CheckAuthScreen> {
  Future<void> setupInteractedMessage() async {
    // Get any messages which caused the application to open from
    // a terminated state.
    RemoteMessage? initialMessage =
        await FirebaseMessaging.instance.getInitialMessage();

    if (initialMessage != null) {
      _handleMessage(initialMessage);
    }

    // Also handle any interaction when the app is in the background via a
    // Stream listener
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessage);

    // FirebaseMessaging.onMessage.listen(_handleMessage);
  }

  void _handleMessage(RemoteMessage message) {
    if (message.data['type'] == 'order') {
      if (message.data.containsKey('id')) {
        Navigator.push(
            context,
            OrderDetailScreen.routePage(
              int.parse(message.data['id']),
              orderBloc: context.read<OrderBloc>(),
            ));
      } else {
        Navigator.of(context).push(PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              const HomeScreen(initialPage: 3), // MyOrdersPage
        ));
      }
    } else if (message.data['type'] == 'ticket') {
      Navigator.of(context).push(SupportScreen.pageRoute());
    } else if (message.data['type'] == 'offer') {
      Navigator.of(context).pushNamed(Notifications.routeName);
    } else if (message.data['type'] == 'deposit_declined' ||
        message.data['type'] == 'deposit_received') {
      Navigator.of(context).pushNamed(WalletScreen.routeName);
    }
  }

  initTopicSubscribtion() async {
    final locale = Provider.of<AppProvider>(context).locale;
    if (kDebugMode) {
      FirebaseMessaging.instance.subscribeToTopic('test');
    }
    FirebaseMessaging.instance.subscribeToTopic(locale.languageCode);
  }

  @override
  void initState() {
    super.initState();
    setupInteractedMessage();
  }

  @override
  void didChangeDependencies() {
    initTopicSubscribtion();
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthenticationBloc, AuthenticationState>(
      builder: (context, state) {
        return const HomeScreen();
      },
    );
  }
}
