import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/store/models/product.dart';
import 'package:goldenprizma/domain/store/bloc/product_detail_bloc.dart';
import 'package:goldenprizma/presentation/views/pages/store/product_detail_screen.dart';
import 'package:goldenprizma/repositories/products_repository.dart';
import 'package:goldenprizma/main.dart';

class ProductDetailScreen extends StatelessWidget {
  final int? productId;
  final Product? product;

  const ProductDetailScreen({
    super.key,
    this.productId,
    this.product,
  }) : assert(productId != null || product != null,
            'Either productId or product must be provided');

  @override
  Widget build(BuildContext context) {
    // If product is already provided, show it directly
    if (product != null) {
      return _buildProductDetail(context, product!);
    }

    // If only productId is provided, load from API
    return BlocProvider(
      create: (context) => ProductDetailBloc(
        productsRepository: getIt.get<ProductsRepository>(),
      )..add(ProductDetailRequested(productId: productId!)),
      child: BlocBuilder<ProductDetailBloc, ProductDetailState>(
        builder: (context, state) {
          if (state is ProductDetailLoading) {
            return Scaffold(
              appBar: AppBar(
                title: const Text('Product Details'),
                elevation: 0,
              ),
              body: const Center(
                child: CircularProgressIndicator(),
              ),
            );
          }

          if (state is ProductDetailError) {
            return Scaffold(
              appBar: AppBar(
                title: const Text('Product Details'),
                elevation: 0,
              ),
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.grey,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Failed to load product',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      state.message,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey,
                          ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton(
                      onPressed: () {
                        context.read<ProductDetailBloc>().add(
                              ProductDetailRequested(productId: productId!),
                            );
                      },
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            );
          }

          if (state is ProductDetailLoaded) {
            return _buildProductDetail(context, state.product);
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildProductDetail(BuildContext context, Product product) {
    // Show the product detail as a bottom sheet and return empty scaffold
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ProductDetailPage.showAsBottomSheet(
        context: context,
        product: product,
        heroTag: 'product_${product.id}_detail',
      );
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('Product Details'),
        elevation: 0,
      ),
      body: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}
