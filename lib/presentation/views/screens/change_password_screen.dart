import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/auth/bloc/change_password_bloc.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/main.dart';
import 'package:goldenprizma/repositories/auth_repository.dart';
import 'package:hugeicons/hugeicons.dart';

class ChangePasswordScreen extends StatefulWidget {
  static PageRouteBuilder pageRoute() {
    return PageRouteBuilder(pageBuilder: (BuildContext context,
        Animation<double> animation, Animation<double> secondaryAnimation) {
      return BlocProvider(
          create: (_) => ChangePasswordBloc(getIt.get<AuthRepository>()),
          child: const ChangePasswordScreen());
    });
  }

  const ChangePasswordScreen({super.key});

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  final TextEditingController oldPasswordController =
      TextEditingController(text: '');
  final TextEditingController newPasswordController =
      TextEditingController(text: '');
  final TextEditingController confirmPasswordController =
      TextEditingController(text: '');

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  bool _hidePassword = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        title: Text(context.loc.changePassword),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Center(
          child: BlocListener<ChangePasswordBloc, ChangePasswordState>(
            listener: (context, state) {
              if (state.status == ChangePasswordStatus.success) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: state.successMessage.isNotEmpty
                        ? Text(state.successMessage)
                        : Text(context.loc.passwordChangedSuccessfully),
                    backgroundColor: Colors.green,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
                oldPasswordController.clear();
                newPasswordController.clear();
                confirmPasswordController.clear();
              }

              if (state.status == ChangePasswordStatus.failure) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.errorMessage),
                    backgroundColor: Colors.red,
                    behavior: SnackBarBehavior.floating,
                    showCloseIcon: true,
                  ),
                );
              }
            },
            child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    const SizedBox(height: 32),
                    TextFormField(
                      controller: oldPasswordController,
                      decoration: InputDecoration(
                        labelText: context.loc.oldPassword,
                        suffix: IconButton(
                            visualDensity: const VisualDensity(
                                vertical: -4, horizontal: -2),
                            padding: const EdgeInsets.all(0),
                            splashRadius: 1,
                            onPressed: () {
                              setState(() {
                                _hidePassword = !_hidePassword;
                              });
                            },
                            icon: Icon(
                              _hidePassword
                                  ? HugeIcons.strokeRoundedView
                                  : HugeIcons.strokeRoundedViewOffSlash,
                            )),
                      ),
                      obscureText: _hidePassword,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return context.loc.pleaseEnterCurrentPassword;
                        }

                        return null;
                      },
                    ),
                    const SizedBox(height: 18),
                    TextFormField(
                      controller: newPasswordController,
                      decoration: InputDecoration(
                        labelText: context.loc.newPassword,
                        suffix: IconButton(
                            visualDensity: const VisualDensity(
                                vertical: -4, horizontal: -2),
                            padding: const EdgeInsets.all(0),
                            splashRadius: 1,
                            onPressed: () {
                              setState(() {
                                _hidePassword = !_hidePassword;
                              });
                            },
                            icon: Icon(
                              _hidePassword
                                  ? HugeIcons.strokeRoundedView
                                  : HugeIcons.strokeRoundedViewOffSlash,
                            )),
                      ),
                      obscureText: _hidePassword,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return context.loc.pleaseEnterPassword;
                        }
                        if (value.length < 8) {
                          return context.loc.passwordMustBeAtLeast8Characters;
                        }

                        if (value == oldPasswordController.text) {
                          return context.loc.newPasswordMustBeDifferent;
                        }

                        return null;
                      },
                    ),
                    const SizedBox(height: 18),
                    TextFormField(
                      controller: confirmPasswordController,
                      decoration: InputDecoration(
                        labelText: context.loc.confirmPassword,
                        suffix: IconButton(
                            visualDensity: const VisualDensity(
                                vertical: -4, horizontal: -2),
                            padding: const EdgeInsets.all(0),
                            splashRadius: 1,
                            onPressed: () {
                              setState(() {
                                _hidePassword = !_hidePassword;
                              });
                            },
                            icon: Icon(
                              _hidePassword
                                  ? HugeIcons.strokeRoundedView
                                  : HugeIcons.strokeRoundedViewOffSlash,
                            )),
                      ),
                      obscureText: _hidePassword,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return context.loc.pleaseEnterPassword;
                        }
                        if (value.length < 8) {
                          return context.loc.passwordMustBeAtLeast8Characters;
                        }

                        if (value != newPasswordController.text) {
                          return context.loc.passwordsDoNotMatch;
                        }

                        return null;
                      },
                    ),
                    const SizedBox(height: 32),
                    SizedBox(
                      width: double.infinity,
                      height: 48,
                      child:
                          BlocBuilder<ChangePasswordBloc, ChangePasswordState>(
                        builder: (context, state) {
                          return ElevatedButton(
                            onPressed: () {
                              if (_formKey.currentState!.validate()) {
                                context.read<ChangePasswordBloc>().add(
                                      ChangePasswordSubmitted(
                                        oldPassword: oldPasswordController.text,
                                        newPassword: newPasswordController.text,
                                        confirmPassword:
                                            confirmPasswordController.text,
                                      ),
                                    );
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              elevation: 0,
                            ),
                            child: state.status == ChangePasswordStatus.loading
                                ? const SizedBox(
                                    height: 24,
                                    width: 24,
                                    child: CircularProgressIndicator(
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                          Colors.white),
                                    ),
                                  )
                                : Text(context.loc.changePassword),
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: 32),
                    Text(
                      context.loc.forgotPasswordMessage,
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Theme.of(context).hintColor),
                    )
                  ],
                )),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    oldPasswordController.dispose();
    newPasswordController.dispose();
    confirmPasswordController.dispose();
    super.dispose();
  }
}
