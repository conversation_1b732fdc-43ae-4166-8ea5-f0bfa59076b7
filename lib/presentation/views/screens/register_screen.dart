import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/domain/auth/bloc/authentication_bloc.dart';
import 'package:goldenprizma/domain/auth/bloc/register_bloc.dart';
import 'package:goldenprizma/domain/models/city.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/helpers/custom_dialogs.dart';
import 'package:goldenprizma/helpers/custom_snackbars.dart';
import 'package:goldenprizma/main.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/auth/login_screen.dart';
import 'package:goldenprizma/presentation/views/forms/auth/city_input.dart';
import 'package:goldenprizma/presentation/views/forms/auth/fullname_input.dart';
import 'package:goldenprizma/presentation/views/forms/auth/password_input.dart';
import 'package:goldenprizma/presentation/views/forms/auth/phone_code_input.dart';
import 'package:goldenprizma/presentation/views/forms/auth/phone_number_input.dart';
import 'package:goldenprizma/presentation/views/screens/check_auth_screen.dart';
import 'package:goldenprizma/repositories/city_repository.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:ionicons/ionicons.dart';
import 'package:provider/provider.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen(
      {super.key,
      required this.phoneNumber,
      required this.dialCode,
      required this.countryCode});
  final String phoneNumber;
  final String dialCode;
  final String countryCode;
  static const String routeName = '/register';
  @override
  _RegisterScreenState createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final TextEditingController _fullNameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  late TextEditingController _phoneController;

  late PhoneNumber _phoneNumber;
  String? _confirmPasswordValidationMessage;
  final double kHorizontalPadding = 24.0;
  bool _passwordIsVisible = false;

  @override
  void initState() {
    _phoneNumber = PhoneNumber(isoCode: widget.countryCode);
    _phoneController = TextEditingController(text: widget.phoneNumber);
    SchedulerBinding.instance.addPostFrameCallback((_) {
      context.read<RegisterBloc>().add(RegisterFormChanged(
            form: context.read<RegisterBloc>().state.form.copyWith(
                  phoneNumberInput: PhoneNumberInput.dirty(
                      widget.phoneNumber.replaceAll(widget.dialCode, '')),
                  phoneCodeInput: PhoneCodeInput.dirty(widget.dialCode),
                ),
          ));
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Locale locale = Provider.of<AppProvider>(context).locale;
    return Scaffold(
      backgroundColor: Provider.of<AppProvider>(context).isDarkMode(context)
          ? AppColors.darkBodyColor
          : Colors.grey.shade50,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness:
              Provider.of<AppProvider>(context).isDarkMode(context)
                  ? Brightness.light
                  : Brightness.dark,
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: Navigator.of(context).canPop()
            ? Container(
                margin: const EdgeInsets.only(left: 16, top: 8),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Provider.of<AppProvider>(context).isDarkMode(context)
                      ? Colors.black.withOpacity(0.5)
                      : Colors.white.withOpacity(0.9),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: IconButton(
                  icon: Icon(
                    locale.languageCode == "en"
                        ? Ionicons.chevron_back
                        : Ionicons.chevron_forward,
                    size: 18,
                    color: Provider.of<AppProvider>(context).isDarkMode(context)
                        ? Colors.white
                        : Colors.black87,
                  ),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              )
            : null,
      ),
      body: BlocListener<RegisterBloc, RegisterState>(
        listener: (context, state) {
          if (state.status.isSubmissionFailure && !state.hasServerError) {
            showErrorSnackBar(
              context: context,
              message: context.loc.generalErrorMessage,
            );
          }

          if (state.status.isSubmissionFailure && state.hasServerError) {
            showErrorSnackBar(
              context: context,
              message: state.apiErrorMessage,
            );
          }

          if (state.status.isSubmissionSuccess) {
            showSuccessDialogWithAutoHide(
              context: context,
              message: state.apiSuccessMessage,
            ).then((value) {
              if (context.mounted) {
                context
                    .read<AuthenticationBloc>()
                    .add(AuthenticationUserRequested());
                Navigator.of(context)
                    .pushReplacementNamed(CheckAuthScreen.routeName);
              }
            });

            //reset all fields
            _resetFields();
          }
        },
        child: LayoutBuilder(
          builder: (BuildContext context, BoxConstraints viewportConstraints) {
            final isDarkMode =
                Provider.of<AppProvider>(context).isDarkMode(context);
            final primaryColor = Theme.of(context).primaryColor;

            return Stack(
              children: [
                // Full-screen gradient background
                Container(
                  height: MediaQuery.of(context).size.height,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      stops: const [0.0, 0.3, 0.7, 1.0],
                      colors: isDarkMode
                          ? [
                              AppColors.appbarDarkColor,
                              AppColors.primaryColor.withOpacity(0.7),
                              AppColors.boxDarkColor.withOpacity(0.5),
                              AppColors.darkBodyColor,
                            ]
                          : [
                              AppColors.primaryColor,
                              AppColors.primaryColor.withOpacity(0.7),
                              Colors.white.withOpacity(0.8),
                              Colors.white,
                            ],
                    ),
                  ),
                ),

                // Main content
                SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: viewportConstraints.maxHeight,
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: double.infinity,
                          height: MediaQuery.of(context).size.height * 0.20,
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              // Decorative shape
                              Positioned(
                                top: 10,
                                right: -30,
                                child: Container(
                                  width: 90,
                                  height: 90,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.white.withOpacity(0.1),
                                  ),
                                ),
                              ),

                              // Another decorative circle
                              Positioned(
                                bottom: 10,
                                left: -20,
                                child: Container(
                                  width: 60,
                                  height: 60,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.white.withOpacity(0.15),
                                  ),
                                ),
                              ),

                              // Logo with animation
                              Positioned(
                                top: MediaQuery.of(context).size.height * 0.09,
                                left: 24,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(15),
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: isDarkMode
                                            ? Colors.black.withOpacity(0.6)
                                            : Colors.white.withOpacity(0.9),
                                        boxShadow: [
                                          BoxShadow(
                                            color:
                                                primaryColor.withOpacity(0.2),
                                            blurRadius: 15,
                                            spreadRadius: 0,
                                          ),
                                        ],
                                      ),
                                      child: Image(
                                        image: const AssetImage(
                                            'assets/images/logo.png'),
                                        width:
                                            MediaQuery.of(context).size.height *
                                                0.05,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Text(
                                      context.loc.createNewAccount,
                                      style: const TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 6),

                        // Form container
                        Padding(
                          padding: EdgeInsets.symmetric(
                            horizontal: kHorizontalPadding,
                          ),
                          child: Container(
                            decoration: BoxDecoration(
                              color: Provider.of<AppProvider>(context)
                                      .isDarkMode(context)
                                  ? AppColors.boxDarkColor.withOpacity(0.7)
                                  : Colors.white.withOpacity(0.95),
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: isDarkMode
                                      ? Colors.black.withOpacity(0.15)
                                      : primaryColor.withOpacity(0.08),
                                  blurRadius: 20,
                                  spreadRadius: 0,
                                  offset: const Offset(0, 5),
                                ),
                              ],
                            ),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 24, vertical: 24),
                            margin: const EdgeInsets.symmetric(vertical: 5),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _nameField(),
                                const SizedBox(height: 16),
                                IgnorePointer(
                                    ignoring: true, child: _phoneField()),
                                const SizedBox(height: 16),
                                CityField(),
                                const SizedBox(height: 16),
                                _passwordField(),
                                const SizedBox(height: 16),
                                _confirmPasswordField(),
                                const SizedBox(height: 24),
                                _registerButton(),
                                const SizedBox(height: 20),
                                _signInButton(),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Widget _nameField() {
    return BlocBuilder<RegisterBloc, RegisterState>(
      builder: (context, state) {
        final isDarkMode =
            Provider.of<AppProvider>(context).isDarkMode(context);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 4, bottom: 8),
              child: Row(
                children: [
                  Icon(
                    Ionicons.person_outline,
                    size: 16,
                    color: isDarkMode ? Colors.white70 : Colors.grey.shade700,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    context.loc.fullName,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: isDarkMode ? Colors.white70 : Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.03),
                    blurRadius: 10,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: TextField(
                autocorrect: false,
                controller: _fullNameController,
                style: TextStyle(
                  color: isDarkMode ? Colors.white : Colors.black87,
                  fontSize: 14,
                ),
                decoration: InputDecoration(
                  prefixIcon: Icon(
                    Ionicons.person,
                    color: isDarkMode
                        ? Colors.grey.shade500
                        : Colors.grey.shade600,
                    size: 18,
                  ),
                  hintText: context.loc.fullName,
                  hintStyle: TextStyle(
                    fontSize: 14,
                    color: isDarkMode
                        ? Colors.grey.shade500
                        : Colors.grey.shade400,
                  ),
                  filled: true,
                  fillColor: isDarkMode
                      ? Colors.grey.shade900.withOpacity(0.3)
                      : Colors.grey.shade50,
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
                  errorText:
                      state.form.fullNameInput.validationErrorMessage(context),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).primaryColor,
                      width: 1.5,
                    ),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Colors.red,
                      width: 1.5,
                    ),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Colors.red,
                      width: 1.5,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: isDarkMode
                          ? Colors.grey.shade700
                          : Colors.grey.shade300,
                      width: 1.5,
                    ),
                  ),
                ),
                onChanged: (value) {
                  context.read<RegisterBloc>().add(RegisterFormChanged(
                        form: state.form.copyWith(
                          fullNameInput: FullNameInput.dirty(value),
                        ),
                      ));
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _phoneField() {
    return BlocBuilder<RegisterBloc, RegisterState>(
      builder: (context, state) {
        final isDarkMode =
            Provider.of<AppProvider>(context).isDarkMode(context);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 4, bottom: 8),
              child: Row(
                children: [
                  Icon(
                    Ionicons.call_outline,
                    size: 16,
                    color: isDarkMode ? Colors.white70 : Colors.grey.shade700,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Phone Number',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: isDarkMode ? Colors.white70 : Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.03),
                    blurRadius: 10,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Directionality(
                textDirection: TextDirection.ltr,
                child: InternationalPhoneNumberInput(
                  onInputChanged: (PhoneNumber number) async {
                    final RegExp code = RegExp('[+]${number.dialCode}');
                    context.read<RegisterBloc>().add(RegisterFormChanged(
                          form: state.form.copyWith(
                            phoneNumberInput: PhoneNumberInput.dirty(number
                                .phoneNumber
                                .toString()
                                .replaceAll(code, '')),
                            phoneCodeInput:
                                PhoneCodeInput.dirty(number.dialCode ?? '+964'),
                          ),
                        ));
                  },
                  selectorConfig: const SelectorConfig(
                    selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                    setSelectorButtonAsPrefixIcon: true,
                    leadingPadding: 16.0,
                  ),
                  ignoreBlank: true,
                  autoValidateMode: AutovalidateMode.disabled,
                  initialValue: _phoneNumber,
                  textFieldController: _phoneController,
                  formatInput: false,
                  keyboardType: TextInputType.phone,
                  inputBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: isDarkMode
                          ? Colors.grey.shade700
                          : Colors.grey.shade300,
                      width: 1.5,
                    ),
                  ),
                  maxLength: 10,
                  inputDecoration: InputDecoration(
                    hintText: '7xx-xxx-xxxx',
                    hintStyle: TextStyle(
                      fontSize: 14,
                      color: isDarkMode
                          ? Colors.grey.shade500
                          : Colors.grey.shade400,
                    ),
                    filled: true,
                    fillColor: isDarkMode
                        ? Colors.grey.shade900.withOpacity(0.3)
                        : Colors.grey.shade50,
                    contentPadding: const EdgeInsets.symmetric(
                        vertical: 16, horizontal: 12),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Theme.of(context).primaryColor,
                        width: 1.5,
                      ),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Colors.red,
                        width: 1.5,
                      ),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Colors.red,
                        width: 1.5,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: isDarkMode
                            ? Colors.grey.shade700
                            : Colors.grey.shade300,
                        width: 1.5,
                      ),
                    ),
                    errorText: state.form.phoneNumberInput
                        .validationErrorMessage(context),
                  ),
                  selectorTextStyle: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black87,
                    fontSize: 14,
                  ),
                  textStyle: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black87,
                    fontSize: 14,
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _passwordField() {
    return BlocBuilder<RegisterBloc, RegisterState>(
      builder: (context, state) {
        final isDarkMode =
            Provider.of<AppProvider>(context).isDarkMode(context);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 4, bottom: 8),
              child: Row(
                children: [
                  Icon(
                    Ionicons.lock_closed_outline,
                    size: 16,
                    color: isDarkMode ? Colors.white70 : Colors.grey.shade700,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Password',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: isDarkMode ? Colors.white70 : Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.03),
                    blurRadius: 10,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: TextField(
                controller: _passwordController,
                obscureText: !_passwordIsVisible,
                style: TextStyle(
                  color: isDarkMode ? Colors.white : Colors.black87,
                  fontSize: 14,
                ),
                decoration: InputDecoration(
                  prefixIcon: Icon(
                    Ionicons.lock_closed,
                    color: isDarkMode
                        ? Colors.grey.shade500
                        : Colors.grey.shade600,
                    size: 18,
                  ),
                  suffixIcon: IconButton(
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    tooltip: 'Toggle password visibility',
                    icon: _passwordIsVisible
                        ? Icon(
                            Ionicons.eye_off,
                            color: isDarkMode
                                ? Colors.grey.shade500
                                : Colors.grey.shade600,
                            size: 18,
                          )
                        : Icon(
                            Ionicons.eye,
                            color: isDarkMode
                                ? Colors.grey.shade500
                                : Colors.grey.shade600,
                            size: 18,
                          ),
                    onPressed: () {
                      setState(() {
                        _passwordIsVisible = !_passwordIsVisible;
                      });
                    },
                  ),
                  hintText: context.loc.passwordPlacholder,
                  hintStyle: TextStyle(
                    fontSize: 14,
                    color: isDarkMode
                        ? Colors.grey.shade500
                        : Colors.grey.shade400,
                  ),
                  filled: true,
                  fillColor: isDarkMode
                      ? Colors.grey.shade900.withOpacity(0.3)
                      : Colors.grey.shade50,
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
                  errorText:
                      state.form.passwordInput.validationErrorMessage(context),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).primaryColor,
                      width: 1.5,
                    ),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Colors.red,
                      width: 1.5,
                    ),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Colors.red,
                      width: 1.5,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: isDarkMode
                          ? Colors.grey.shade700
                          : Colors.grey.shade300,
                      width: 1.5,
                    ),
                  ),
                ),
                onChanged: (value) {
                  context.read<RegisterBloc>().add(RegisterFormChanged(
                        form: state.form.copyWith(
                          passwordInput: PasswordInput.dirty(value),
                        ),
                      ));
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _confirmPasswordField() {
    return BlocBuilder<RegisterBloc, RegisterState>(
      builder: (context, state) {
        final isDarkMode =
            Provider.of<AppProvider>(context).isDarkMode(context);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 4, bottom: 8),
              child: Row(
                children: [
                  Icon(
                    Ionicons.shield_checkmark_outline,
                    size: 16,
                    color: isDarkMode ? Colors.white70 : Colors.grey.shade700,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Confirm Password',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: isDarkMode ? Colors.white70 : Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.03),
                    blurRadius: 10,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: TextField(
                controller: _confirmPasswordController,
                obscureText: !_passwordIsVisible,
                style: TextStyle(
                  color: isDarkMode ? Colors.white : Colors.black87,
                  fontSize: 14,
                ),
                decoration: InputDecoration(
                  prefixIcon: Icon(
                    Ionicons.lock_closed,
                    color: isDarkMode
                        ? Colors.grey.shade500
                        : Colors.grey.shade600,
                    size: 18,
                  ),
                  suffixIcon: IconButton(
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    tooltip: 'Toggle password visibility',
                    icon: _passwordIsVisible
                        ? Icon(
                            Ionicons.eye_off,
                            color: isDarkMode
                                ? Colors.grey.shade500
                                : Colors.grey.shade600,
                            size: 18,
                          )
                        : Icon(
                            Ionicons.eye,
                            color: isDarkMode
                                ? Colors.grey.shade500
                                : Colors.grey.shade600,
                            size: 18,
                          ),
                    onPressed: () {
                      setState(() {
                        _passwordIsVisible = !_passwordIsVisible;
                      });
                    },
                  ),
                  hintText: context.loc.confirmPasswordPlacholder,
                  hintStyle: TextStyle(
                    fontSize: 14,
                    color: isDarkMode
                        ? Colors.grey.shade500
                        : Colors.grey.shade400,
                  ),
                  filled: true,
                  fillColor: isDarkMode
                      ? Colors.grey.shade900.withOpacity(0.3)
                      : Colors.grey.shade50,
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
                  errorText: state.form.confirmPasswordInput.invalid
                      ? state.form.confirmPasswordInput
                          .validationErrorMessage(context)
                      : _confirmPasswordValidationMessage,
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).primaryColor,
                      width: 1.5,
                    ),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Colors.red,
                      width: 1.5,
                    ),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Colors.red,
                      width: 1.5,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: isDarkMode
                          ? Colors.grey.shade700
                          : Colors.grey.shade300,
                      width: 1.5,
                    ),
                  ),
                ),
                onChanged: (value) {
                  context.read<RegisterBloc>().add(RegisterFormChanged(
                        form: state.form.copyWith(
                          confirmPasswordInput: PasswordInput.dirty(value),
                        ),
                      ));
                  if (state.form.passwordInput.value != value.trim()) {
                    setState(() {
                      _confirmPasswordValidationMessage =
                          'It doesn\'t match the password';
                    });
                  } else {
                    setState(() {
                      _confirmPasswordValidationMessage = null;
                    });
                  }
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _registerButton() {
    return BlocBuilder<RegisterBloc, RegisterState>(
      builder: (context, state) {
        final isDarkMode =
            Provider.of<AppProvider>(context).isDarkMode(context);
        final primaryColor = Theme.of(context).primaryColor;

        return Container(
          width: double.infinity,
          height: 55,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              colors: [
                primaryColor,
                primaryColor.withOpacity(0.8),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: primaryColor.withOpacity(0.5),
                blurRadius: 15,
                spreadRadius: 0,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: state.status.isSubmissionInProgress
              ? Center(
                  child: SizedBox(
                    width: 30,
                    height: 30,
                    child: CircularProgressIndicator.adaptive(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        isDarkMode
                            ? Colors.white
                            : Colors.white.withOpacity(0.9),
                      ),
                      strokeWidth: 2.5,
                    ),
                  ),
                )
              : ElevatedButton(
                  onPressed: () {
                    context
                        .read<RegisterBloc>()
                        .add(const RegisterFormSubmitted());
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    elevation: 0,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Ionicons.person_add_outline,
                        size: 18,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        context.loc.register,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0.5,
                          shadows: [
                            Shadow(
                              color: Colors.black26,
                              offset: Offset(0, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
        );
      },
    );
  }

  Widget _signInButton() {
    return BlocBuilder<RegisterBloc, RegisterState>(
      builder: (context, state) {
        final isDarkMode =
            Provider.of<AppProvider>(context).isDarkMode(context);
        final primaryColor = Theme.of(context).primaryColor;

        return Container(
          margin: EdgeInsets.symmetric(horizontal: kHorizontalPadding / 2),
          decoration: BoxDecoration(
            color: isDarkMode
                ? Colors.grey.shade900.withOpacity(0.4)
                : Colors.white.withOpacity(0.9),
          ),
          child: TextButton(
            onPressed: state.status.isSubmissionInProgress
                ? null
                : () {
                    Navigator.pushNamed(context, LoginScreen.routeName);
                  },
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 16),
              foregroundColor: primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  context.loc.alreadyHaveAccount,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: isDarkMode
                        ? Colors.grey.shade300
                        : Colors.grey.shade700,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  context.loc.signIn,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: primaryColor,
                    shadows: [
                      Shadow(
                        color: primaryColor.withOpacity(0.3),
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 4),
                Icon(
                  Ionicons.arrow_forward_circle_outline,
                  size: 16,
                  color: primaryColor,
                )
              ],
            ),
          ),
        );
      },
    );
  }

  void _resetFields() {
    _fullNameController.clear();
    _passwordController.clear();
    _confirmPasswordController.clear();
    _phoneController.clear();
    context.read<RegisterBloc>().add(const RegisterFormReset());
  }
}

class OvalCliper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    var path = Path();
    path.lineTo(0, size.height - 50);
    path.quadraticBezierTo(
        size.width / 2, size.height + 50, size.width + 50, size.height - 60);
    path.lineTo(size.width, 0);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return true;
  }
}

class CityField extends StatelessWidget {
  CityField({super.key});
  final TextEditingController _sizeEditTextController = TextEditingController();
  final CityRepository _cityRepository = getIt.get<CityRepository>();
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<RegisterBloc, RegisterState>(
      builder: (context, state) {
        final isDarkMode =
            Provider.of<AppProvider>(context).isDarkMode(context);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 4, bottom: 8),
              child: Row(
                children: [
                  Icon(
                    Ionicons.location_outline,
                    size: 16,
                    color: isDarkMode ? Colors.white70 : Colors.grey.shade700,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'City',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: isDarkMode ? Colors.white70 : Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.03),
                    blurRadius: 10,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: DropdownSearch<City>(
                key: UniqueKey(),
                popupProps: PopupProps.modalBottomSheet(
                  scrollbarProps: const ScrollbarProps(
                    thickness: 4,
                  ),
                  showSearchBox: true,
                  showSelectedItems: true,
                  isFilterOnline: true,
                  title: Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 16),
                    margin: const EdgeInsets.only(bottom: 12),
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? Colors.grey.shade800
                          : Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Ionicons.location,
                          size: 18,
                          color: isDarkMode
                              ? Colors.white
                              : Theme.of(context).primaryColor,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Select City',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: isDarkMode
                                ? Colors.white
                                : Colors.grey.shade700,
                          ),
                        ),
                      ],
                    ),
                  ),
                  searchFieldProps: TextFieldProps(
                    controller: _sizeEditTextController,
                    decoration: InputDecoration(
                      hintText: 'Search cities...',
                      hintStyle: TextStyle(
                        color: isDarkMode
                            ? Colors.grey.shade500
                            : Colors.grey.shade400,
                        fontSize: 14,
                      ),
                      prefixIcon: Icon(
                        Ionicons.search,
                        color: isDarkMode
                            ? Colors.grey.shade500
                            : Colors.grey.shade600,
                        size: 18,
                      ),
                      filled: true,
                      fillColor: isDarkMode
                          ? Colors.grey.shade900.withOpacity(0.3)
                          : Colors.grey.shade50,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: isDarkMode
                              ? Colors.grey.shade700
                              : Colors.grey.shade300,
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: isDarkMode
                              ? Colors.grey.shade700
                              : Colors.grey.shade300,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ),
                  ),
                  itemBuilder: _customPopupItemBuilder,
                ),
                filterFn: (City? city, filter) {
                  return city != null && city.filterByName(filter);
                },
                asyncItems: (String? filter) async {
                  List<City> cities =
                      await _cityRepository.getAll(search: filter);
                  return cities;
                },
                onChanged: (City? data) {
                  context.read<RegisterBloc>().add(RegisterFormChanged(
                        form: state.form
                            .copyWith(cityInput: CityInput.dirty(data)),
                      ));
                },
                selectedItem: state.form.cityInput.value,
                compareFn: (item, selectedItem) => item.id == selectedItem.id,
                validator: (u) => u == null ? "City is required" : null,
                dropdownDecoratorProps: DropDownDecoratorProps(
                  dropdownSearchDecoration: InputDecoration(
                    hintText: "Select a city",
                    hintStyle: TextStyle(
                      fontSize: 14,
                      color: isDarkMode
                          ? Colors.grey.shade500
                          : Colors.grey.shade400,
                    ),
                    filled: true,
                    fillColor: isDarkMode
                        ? Colors.grey.shade900.withOpacity(0.3)
                        : Colors.grey.shade50,
                    errorText:
                        state.form.cityInput.validationErrorMessage(context),
                    prefixIcon: Icon(
                      Ionicons.location,
                      color: isDarkMode
                          ? Colors.grey.shade500
                          : Colors.grey.shade600,
                      size: 18,
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                        vertical: 16, horizontal: 12),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Theme.of(context).primaryColor,
                        width: 1.5,
                      ),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Colors.red,
                        width: 1.5,
                      ),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Colors.red,
                        width: 1.5,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: isDarkMode
                            ? Colors.grey.shade700
                            : Colors.grey.shade300,
                        width: 1.5,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _customPopupItemBuilder(
      BuildContext context, City? item, bool isSelected) {
    final isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);
    final primaryColor = Theme.of(context).primaryColor;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isSelected
            ? (isDarkMode
                ? primaryColor.withOpacity(0.2)
                : primaryColor.withOpacity(0.1))
            : (isDarkMode
                ? Colors.grey.shade800.withOpacity(0.3)
                : Colors.white),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected
              ? primaryColor
              : isDarkMode
                  ? Colors.grey.shade700
                  : Colors.grey.shade200,
          width: 1.5,
        ),
        boxShadow: isSelected
            ? [
                BoxShadow(
                  color: primaryColor.withOpacity(0.1),
                  blurRadius: 8,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        selected: isSelected,
        leading: Icon(
          Ionicons.location_outline,
          color: isSelected
              ? primaryColor
              : isDarkMode
                  ? Colors.grey.shade400
                  : Colors.grey.shade700,
          size: 20,
        ),
        title: Text(
          item?.name ?? '',
          style: TextStyle(
            fontSize: 15,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            color: isSelected
                ? primaryColor
                : isDarkMode
                    ? Colors.white
                    : Colors.grey.shade800,
          ),
        ),
        trailing: isSelected
            ? Icon(
                Ionicons.checkmark_circle,
                color: primaryColor,
                size: 20,
              )
            : null,
      ),
    );
  }
}
