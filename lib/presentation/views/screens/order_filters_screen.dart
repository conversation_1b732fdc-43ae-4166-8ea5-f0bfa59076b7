import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:provider/provider.dart';

import '../../../domain/orders/models/size_model.dart';
import '../../../domain/providers/app_provider.dart';

class OrderFiltersScreen extends StatefulWidget {
  const OrderFiltersScreen({super.key});

  @override
  State<OrderFiltersScreen> createState() => _OrderFiltersScreenState();
}

class _OrderFiltersScreenState extends State<OrderFiltersScreen> {
  final TextEditingController _websiteController = TextEditingController();
  // final TextEditingController _brandController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        title: const Text("Filter"),
        centerTitle: false,
      ),
      body: Stack(
        children: [
          Positioned(
            top: 0,
            left: 0,
            child: SizedBox(
              width: size.width,
              height: size.height,
              child: SingleChildScrollView(
                  child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _searchField(context),
                    const SizedBox(height: 24.0),
                    _buildWebsiteInput(isDarkMode),
                    const SizedBox(height: 24.0),
                    _buildWebsiteInput(isDarkMode),
                  ],
                ),
              )),
            ),
          ),
          Positioned(
              width: size.width - 32,
              left: 16,
              bottom: 0,
              child: SafeArea(
                child: SizedBox(
                  child: Column(
                    children: [
                      SizedBox(
                        width: size.width,
                        child: ElevatedButton(
                          onPressed: () {},
                          style: ElevatedButton.styleFrom(
                            elevation: 0,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                          child: const Text('Filter'),
                        ),
                      ),
                      const SizedBox(height: 8),
                      SizedBox(
                        width: size.width,
                        child: OutlinedButton(
                          onPressed: () {},
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                          child: const Text('Clear Filters'),
                        ),
                      ),
                    ],
                  ),
                ),
              ))
        ],
      ),
    );
  }

  Widget _searchField(BuildContext context) {
    return TextField(
      controller: _searchController,
      autofocus: false,
      decoration: InputDecoration(
        contentPadding: EdgeInsets.zero,
        isDense: true,
        prefixIcon: const Icon(
          Icons.search,
          color: Colors.grey,
          size: 24,
        ),
        hintText: context.loc.search,
      ),
      onChanged: (value) {
        // context.read<WebsiteBloc>().add(WebsiteSearchChanged(search: value));
      },
    );
  }

  Widget _buildWebsiteInput(bool isDarkMode) {
    return DropdownSearch<SizeModel>(
      popupProps: PopupProps.modalBottomSheet(
        scrollbarProps: const ScrollbarProps(thickness: 4),
        showSearchBox: true,
        showSelectedItems: true,
        isFilterOnline: true,
        searchFieldProps: TextFieldProps(
          controller: _websiteController,
          autofocus: true,
        ),
        modalBottomSheetProps: ModalBottomSheetProps(
          backgroundColor: Theme.of(context).colorScheme.surface,
        ),
        itemBuilder: _customPopupItemBuilder,
      ),
      autoValidateMode: AutovalidateMode.always,
      asyncItems: (String? filter) async {
        // List<SizeModel> sizes =
        //     await _sizeRepository.getAll(filter: "$filter");

        return [];
      },
      onChanged: (SizeModel? data) {},
      selectedItem: null,
      compareFn: (item, selectedItem) => item.id == selectedItem.id,
      dropdownDecoratorProps: const DropDownDecoratorProps(
        dropdownSearchDecoration: InputDecoration(
          labelText: "Website",
          isCollapsed: true,
          contentPadding: EdgeInsetsDirectional.fromSTEB(16, 14, 1, 0),
        ),
      ),
    );
  }

  Widget _customPopupItemBuilder(
      BuildContext context, SizeModel? item, bool isSelected) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: !isSelected
          ? null
          : BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(5),
            ),
      child: ListTile(
        selected: isSelected,
        title: Text(item?.name ?? ''),
      ),
    );
  }
}
