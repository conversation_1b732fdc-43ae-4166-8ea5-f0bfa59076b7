import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_verification_code/flutter_verification_code.dart';
import 'package:goldenprizma/domain/auth/bloc/otp_bloc.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/helpers/logger.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/auth/update_password_screen.dart';
import 'package:goldenprizma/presentation/views/screens/register_screen.dart';

class Verification extends StatefulWidget {
  const Verification(
      {super.key,
      required this.phoneNumber,
      required this.countryCode,
      required this.isForgetPassword,
      required this.dialCode});
  final String phoneNumber, countryCode, dialCode;
  final bool isForgetPassword;
  static PageRouteBuilder pageRoute(
      {required OtpBloc otpBloc,
      required String phoneNumber,
      required bool isForgetPassword,
      required String countryCode,
      required String dialCode}) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) =>
          MultiBlocProvider(
        providers: [BlocProvider.value(value: otpBloc)],
        child: Verification(
          phoneNumber: phoneNumber,
          countryCode: countryCode,
          isForgetPassword: isForgetPassword,
          dialCode: dialCode,
        ),
      ),
    );
  }

  @override
  _VerificationState createState() => _VerificationState();
}

class _VerificationState extends State<Verification> {
  String otpCode = "";
  @override
  Widget build(BuildContext context) {
    logger(
        "phoneNumber ${widget.phoneNumber} ${widget.countryCode} ${widget.dialCode}}");
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        leading: Container(
          margin: const EdgeInsets.only(left: 16, top: 8),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.white.withOpacity(0.9),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                spreadRadius: 1,
              ),
            ],
          ),
          child: IconButton(
            icon: const Icon(
              Icons.arrow_back_ios_new,
              size: 16,
              color: Colors.black87,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.white,
        child: Stack(
          children: [
            // Top gradient similar to login_page
            Positioned(
              top: -MediaQuery.of(context).size.height * 0.1,
              child: Container(
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height * 0.4,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppColors.primaryColor,
                      AppColors.primaryColor.withOpacity(0.6),
                      Colors.white.withOpacity(0.2),
                    ],
                  ),
                ),
              ),
            ),

            // Decorative elements with gradients
            Positioned(
              top: MediaQuery.of(context).size.height * 0.15,
              right: -40,
              child: Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.primaryColor.withOpacity(0.2),
                      AppColors.primaryColor.withOpacity(0.05),
                    ],
                  ),
                ),
              ),
            ),
            Positioned(
              top: MediaQuery.of(context).size.height * 0.3,
              left: -60,
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    begin: Alignment.topRight,
                    end: Alignment.bottomLeft,
                    colors: [
                      AppColors.primaryColor.withOpacity(0.2),
                      AppColors.primaryColor.withOpacity(0.05),
                    ],
                  ),
                ),
              ),
            ),
            Positioned(
              bottom: -80,
              right: -80,
              child: Container(
                width: 200,
                height: 200,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppColors.primaryColor.withOpacity(0.15),
                      AppColors.primaryColor.withOpacity(0.05),
                    ],
                  ),
                ),
              ),
            ),

            // Main content
            SafeArea(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: SizedBox(
                  height: MediaQuery.of(context).size.height -
                      MediaQuery.of(context).padding.top -
                      MediaQuery.of(context).padding.bottom,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    width: double.infinity,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Flexible(
                          flex: 2,
                          child: Column(
                            children: [
                              const SizedBox(height: 4),
                              // Animated Image with Effects
                              TweenAnimationBuilder(
                                duration: const Duration(milliseconds: 800),
                                tween: Tween<double>(begin: 0.0, end: 1.0),
                                builder: (context, double value, child) {
                                  return Transform.scale(
                                    scale: value,
                                    child: const SizedBox(
                                      width: 80,
                                      height: 80,
                                      child: Image(
                                        image: AssetImage(
                                            'assets/images/otp_icon.png'),
                                      ),
                                    ),
                                  );
                                },
                              ),

                              const SizedBox(height: 8),

                              // Title Animation
                              TweenAnimationBuilder(
                                duration: const Duration(milliseconds: 1000),
                                tween: Tween<double>(begin: 0.0, end: 1.0),
                                builder: (context, double value, child) {
                                  return Opacity(
                                    opacity: value,
                                    child: const Text(
                                      'Verification',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        letterSpacing: 0.4,
                                        color: AppColors.primaryColor,
                                      ),
                                    ),
                                  );
                                },
                              ),

                              const SizedBox(height: 4),

                              // Subtitle with Phone Number
                              TweenAnimationBuilder(
                                duration: const Duration(milliseconds: 1200),
                                tween: Tween<double>(begin: 0.0, end: 1.0),
                                builder: (context, double value, child) {
                                  return Opacity(
                                    opacity: value,
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16, vertical: 8),
                                      child: BlocConsumer<OtpBloc, OtpState>(
                                        listener: (context, state) {
                                          if (state.status ==
                                              OtpStatus.request) {
                                            showLoadingAlert(context: context);
                                          } else if (state.status ==
                                              OtpStatus.verify) {
                                            if (!widget.isForgetPassword) {
                                              Navigator.of(context)
                                                  .pushNamedAndRemoveUntil(
                                                      RegisterScreen.routeName,
                                                      arguments: {
                                                        'phoneNumber':
                                                            state.phoneNumber,
                                                        'dialCode':
                                                            state.dialCode,
                                                        'countryCode':
                                                            state.countryCode
                                                      },
                                                      (route) => false);
                                            } else {
                                              Navigator.pushAndRemoveUntil(
                                                  context,
                                                  MaterialPageRoute(
                                                      builder: (_) =>
                                                          UpdatePasswordScreen(
                                                            otpCode: otpCode,
                                                            countryCode:
                                                                state.dialCode,
                                                            phoneNumber: state
                                                                .phoneNumber,
                                                            token: state.token,
                                                          )),
                                                  (route) => false);
                                            }
                                          } else if (state.status ==
                                              OtpStatus.failure) {
                                            Navigator.of(context).pop();
                                          }
                                        },
                                        builder: (context, state) {
                                          return RichText(
                                            textAlign: TextAlign.center,
                                            text: TextSpan(
                                              children: [
                                                const TextSpan(
                                                  text:
                                                      "Enter the OTP code sent to\n",
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                    color: Colors.black87,
                                                    height: 1.4,
                                                    fontWeight: FontWeight.w400,
                                                  ),
                                                ),
                                                TextSpan(
                                                  text:
                                                      "${widget.dialCode}${widget.phoneNumber}",
                                                  style: const TextStyle(
                                                    fontSize: 15,
                                                    fontWeight: FontWeight.w600,
                                                    color: Colors.black87,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                        Flexible(
                          flex: 4,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              // OTP Input Field with Animation
                              TweenAnimationBuilder(
                                duration: const Duration(milliseconds: 1400),
                                tween: Tween<double>(begin: 0.0, end: 1.0),
                                builder: (context, double value, child) {
                                  return Opacity(
                                    opacity: value,
                                    child: Transform.translate(
                                      offset: Offset(0, 20 * (1 - value)),
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 12, vertical: 0),
                                        child: Column(
                                          children: [
                                            Directionality(
                                              textDirection: TextDirection.ltr,
                                              child: VerificationCode(
                                                length: 4,
                                                fullBorder: true,
                                                digitsOnly: true,
                                                textStyle: const TextStyle(
                                                  fontSize: 24,
                                                  fontWeight: FontWeight.bold,
                                                  color: AppColors.primaryColor,
                                                ),
                                                underlineColor:
                                                    AppColors.primaryColor,
                                                cursorColor:
                                                    AppColors.primaryColor,
                                                keyboardType:
                                                    TextInputType.number,
                                                underlineWidth: 2.5,
                                                margin:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 6),
                                                underlineUnfocusedColor:
                                                    Colors.grey.shade300,
                                                fillColor: Colors.white,
                                                onCompleted: (value) {
                                                  otpCode = value;
                                                  log('completed');
                                                },
                                                onEditing: (value) {
                                                  otpCode = "";
                                                  log(value.toString());
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),

                              const SizedBox(height: 8),

                              // Resend OTP
                              TweenAnimationBuilder(
                                duration: const Duration(milliseconds: 1600),
                                tween: Tween<double>(begin: 0.0, end: 1.0),
                                builder: (context, double value, child) {
                                  return Opacity(
                                    opacity: value,
                                    child: const ResendOTP(),
                                  );
                                },
                              ),

                              const SizedBox(height: 8),

                              // Verify Button with Animation
                              TweenAnimationBuilder(
                                duration: const Duration(milliseconds: 1800),
                                tween: Tween<double>(begin: 0.0, end: 1.0),
                                builder: (context, double value, child) {
                                  return Opacity(
                                    opacity: value,
                                    child: Transform.translate(
                                      offset: Offset(0, 20 * (1 - value)),
                                      child: Container(
                                        width: double.infinity,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          boxShadow: [
                                            BoxShadow(
                                              color: AppColors.primaryColor
                                                  .withOpacity(0.3),
                                              blurRadius: 20,
                                              spreadRadius: 2,
                                              offset: const Offset(0, 5),
                                            ),
                                          ],
                                          gradient: const LinearGradient(
                                            begin: Alignment.topLeft,
                                            end: Alignment.bottomRight,
                                            colors: [
                                              AppColors.primaryColor,
                                              AppColors.primaryBrightColor,
                                            ],
                                          ),
                                        ),
                                        child: Material(
                                          color: Colors.transparent,
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          child: InkWell(
                                            borderRadius:
                                                BorderRadius.circular(12),
                                            onTap: () {
                                              if (widget.isForgetPassword) {
                                                context.read<OtpBloc>().add(
                                                      PasswordOTPVerificationRequested(
                                                        code: otpCode,
                                                        dialCode:
                                                            widget.dialCode,
                                                        phoneNumber:
                                                            widget.phoneNumber,
                                                        countryCode:
                                                            widget.countryCode,
                                                      ),
                                                    );
                                              } else {
                                                context.read<OtpBloc>().add(
                                                      OTPVerificationRequested(
                                                        code: otpCode,
                                                        dialCode:
                                                            widget.dialCode,
                                                        phoneNumber:
                                                            widget.phoneNumber,
                                                        countryCode:
                                                            widget.countryCode,
                                                      ),
                                                    );
                                              }
                                            },
                                            child: const Padding(
                                              padding: EdgeInsets.symmetric(
                                                  vertical: 16),
                                              child: Center(
                                                child: Text(
                                                  "Verify",
                                                  style: TextStyle(
                                                    color: Colors.white,
                                                    fontWeight: FontWeight.w600,
                                                    fontSize: 16,
                                                    letterSpacing: 0.5,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),

                              const SizedBox(height: 16),

                              // Back Button
                              TweenAnimationBuilder(
                                duration: const Duration(milliseconds: 2000),
                                tween: Tween<double>(begin: 0.0, end: 1.0),
                                builder: (context, double value, child) {
                                  return Opacity(
                                    opacity: value,
                                    child: Container(
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                            color:
                                                Colors.grey.withOpacity(0.3)),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: TextButton(
                                        style: ButtonStyle(
                                          padding: WidgetStateProperty.all(
                                            const EdgeInsets.symmetric(
                                                vertical: 14),
                                          ),
                                          overlayColor: WidgetStateProperty.all(
                                            AppColors.primaryColor
                                                .withOpacity(0.1),
                                          ),
                                        ),
                                        onPressed: () {
                                          Navigator.of(context).pop();
                                        },
                                        child: Text(
                                          context.loc.back,
                                          style: const TextStyle(
                                            color: Colors.black87,
                                            fontWeight: FontWeight.w500,
                                            fontSize: 15,
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ResendOTP extends StatefulWidget {
  const ResendOTP({
    super.key,
  });

  @override
  State<ResendOTP> createState() => _ResendOTPState();
}

class _ResendOTPState extends State<ResendOTP> {
  int counter = 120;

  @override
  void initState() {
    if (mounted) {
      startTimer();
    }
    super.initState();
  }

  Timer? timer;
  void startTimer() {
    timer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (counter <= 0) {
        timer?.cancel();
        return;
      }
      if (mounted) {
        setState(() {
          counter = counter - 1;
        });
      }
    });
  }

  @override
  void dispose() {
    timer?.cancel();
    super.dispose();
  }

  String get timerString {
    final int minutes = counter ~/ 60;
    final int seconds = counter % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Column(
        children: [
          if (counter > 0)
            Column(
              children: [
                const Text(
                  "Resend code in",
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor.withOpacity(0.15),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    counter == 120 ? '02:00' : timerString,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
          if (counter <= 0)
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  context.loc.didntReceiveTheOTP,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    setState(() {
                      counter = 120;
                      startTimer();
                      BlocProvider.of<OtpBloc>(context).add(OTPRequested(
                        phoneNumber:
                            BlocProvider.of<OtpBloc>(context).state.phoneNumber,
                        countryCode:
                            BlocProvider.of<OtpBloc>(context).state.countryCode,
                        dialCode:
                            BlocProvider.of<OtpBloc>(context).state.dialCode,
                        scope: 'forget_password',
                      ));
                    });
                  },
                  style: ButtonStyle(
                    padding: WidgetStateProperty.all(
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    ),
                    backgroundColor: WidgetStateProperty.all(
                      AppColors.primaryColor.withOpacity(0.1),
                    ),
                    shape: WidgetStateProperty.all(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                  ),
                  child: Text(
                    context.loc.resend,
                    style: const TextStyle(
                      color: AppColors.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }
}
