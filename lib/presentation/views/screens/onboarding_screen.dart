// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/helpers/app_settings.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/screens/check_auth_screen.dart';
import 'package:goldenprizma/presentation/views/sliders/onboard_slider_page.dart';
import 'package:ionicons/ionicons.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});
  static const String routeName = '/onboarding';
  @override
  _OnboardingScreenState createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  int _currentPage = 0;
  final PageController _pageController = PageController();

  static const double _horizontalSpace = 15;

  @override
  Widget build(BuildContext context) {
    // Configure system UI overlay style for a cleaner look
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Theme.of(context).brightness == Brightness.dark
          ? Brightness.light
          : Brightness.dark,
    ));

    Color primaryColor = Theme.of(context).primaryColor;
    List<Widget> pages = _pageList(context);
    bool isLastPage = _currentPage == pages.length - 1;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDarkMode ? AppColors.darkBodyColor : Colors.white,
      extendBodyBehindAppBar: true,
      body: SafeArea(
        child: Stack(
          children: <Widget>[
            // Backdrop design elements
            Positioned(
              top: -120,
              right: -100,
              child: Container(
                width: 300,
                height: 300,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: primaryColor.withOpacity(0.05),
                ),
              ),
            ),
            Positioned(
              bottom: -150,
              left: -100,
              child: Container(
                width: 300,
                height: 300,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: primaryColor.withOpacity(0.05),
                ),
              ),
            ),

            // Main content
            PageView.builder(
              scrollDirection: Axis.horizontal,
              controller: _pageController,
              physics: const BouncingScrollPhysics(),
              itemCount: pages.length,
              onPageChanged: _onPageChange,
              itemBuilder: (context, int index) =>
                  _pageBuilder(context, pages, index),
            ),

            // Navigation elements
            _backButton(),
            _pageFooter(context, primaryColor, pages, isLastPage),
          ],
        ),
      ),
    );
  }

  Widget _pageFooter(BuildContext context, Color primaryColor,
      List<Widget> pages, bool isLastPage) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: <Widget>[
        Container(
          margin: const EdgeInsets.only(
              bottom: 40, left: _horizontalSpace, right: _horizontalSpace),
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
          decoration: BoxDecoration(
            color: isDarkMode
                ? AppColors.boxDarkColor.withOpacity(0.7)
                : Colors.white.withOpacity(0.9),
            borderRadius: BorderRadius.circular(30),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                spreadRadius: 0,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              _skipButton(context),
              _generateDots(primaryColor, pages),
              _actionButton(isLastPage, primaryColor, context)
            ],
          ),
        )
      ],
    );
  }

  Widget _skipButton(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return TextButton(
      onPressed: () {
        _done(context);
      },
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        foregroundColor: isDarkMode ? Colors.white70 : Colors.grey.shade700,
      ),
      child: Text(
        context.loc.skip,
        style: const TextStyle(
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
        ),
      ),
    );
  }

  Widget _generateDots(Color primaryColor, pages) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List<Widget>.generate(pages.length, (int index) {
          final isActive = index == _currentPage;
          return AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            margin: const EdgeInsets.symmetric(horizontal: 4),
            height: 8,
            width: isActive ? 24 : 8,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: isActive ? primaryColor : primaryColor.withOpacity(0.3),
              boxShadow: isActive
                  ? [
                      BoxShadow(
                        color: primaryColor.withOpacity(0.3),
                        blurRadius: 4,
                        spreadRadius: 1,
                      ),
                    ]
                  : null,
            ),
          );
        }),
      ),
    );
  }

  Widget _actionButton(
      bool isLastPage, Color primaryColor, BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor:
              isLastPage ? primaryColor : primaryColor.withOpacity(0.1),
          foregroundColor: isLastPage ? Colors.white : primaryColor,
          elevation: isLastPage ? 8 : 0,
          shadowColor:
              isLastPage ? primaryColor.withOpacity(0.4) : Colors.transparent,
          padding: EdgeInsets.symmetric(
            horizontal: isLastPage ? 24 : 16,
            vertical: 12,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(isLastPage ? 24 : 20),
          ),
        ),
        onPressed: () {
          setState(() {
            if (isLastPage) {
              _done(context);
              return;
            }
            _currentPage = _currentPage + 1;
            _pageController.animateToPage(
              _currentPage,
              duration: const Duration(milliseconds: 500),
              curve: Curves.easeInOutCubic,
            );
          });
        },
        child: isLastPage ? _getStardedButton() : _nextButton(),
      ),
    );
  }

  Widget _nextButton() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          context.loc.next,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            letterSpacing: 0.5,
          ),
        ),
        const SizedBox(width: 6),
        const Icon(
          Ionicons.chevron_forward,
          size: 16,
        ),
      ],
    );
  }

  Widget _getStardedButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            context.loc.getStarted,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              letterSpacing: 0.5,
            ),
          ),
          const SizedBox(width: 8),
          const Icon(
            Ionicons.arrow_forward,
            size: 16,
          ),
        ],
      ),
    );
  }

  void _done(BuildContext context) async {
    await AppSettings.setOnboarded(true);
    Navigator.pushReplacementNamed(context, CheckAuthScreen.routeName);
  }

  Widget _pageBuilder(context, pages, int index) {
    // Add a fade transition between pages
    return AnimatedOpacity(
      duration: const Duration(milliseconds: 250),
      opacity: index == _currentPage ? 1.0 : 0.8,
      child: pages[index],
    );
  }

  void _onPageChange(int index) {
    setState(() {
      _currentPage = index;
    });
  }

  Widget _backButton() {
    bool isRtl = AppSettings.isRtl(context);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Positioned(
      top: 16,
      left: isRtl ? null : _horizontalSpace,
      right: isRtl ? _horizontalSpace : null,
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: isDarkMode
              ? AppColors.boxDarkColor.withOpacity(0.8)
              : Colors.white.withOpacity(0.9),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              spreadRadius: 0,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: IconButton(
          onPressed: () {
            Navigator.pop(context);
          },
          icon: Icon(
            isRtl ? Ionicons.chevron_forward : Ionicons.chevron_back,
            size: 20,
            color: isDarkMode ? Colors.white : Colors.black87,
          ),
          splashRadius: 24,
          tooltip: 'Back',
        ),
      ),
    );
  }

  List<Widget> _pageList(context) {
    return <Widget>[
      OnboardSliderPage(
          title: AppLocalizations.of(context)!.firstSlideTitle,
          description: AppLocalizations.of(context)!.firstSlideDescription,
          image: 'assets/images/png/page_1.png'),
      OnboardSliderPage(
          title: AppLocalizations.of(context)!.secondSlideTitle,
          description: AppLocalizations.of(context)!.secondSlideDescription,
          image: 'assets/images/png/page_2.png'),
      OnboardSliderPage(
          title: AppLocalizations.of(context)!.thirdSlideTitle,
          description: AppLocalizations.of(context)!.thirdSlideDescription,
          image: 'assets/images/png/page_3.png'),
    ];
  }
}
