import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/notifications/bloc/notification_bloc.dart';
import 'package:goldenprizma/domain/notifications/models/notification.dart';
import 'package:goldenprizma/domain/orders/bloc/order_bloc.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/env.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/helpers/is_arabic_kurdish.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/screens/order_detail_screen.dart';
import 'package:goldenprizma/presentation/views/screens/support_screen.dart';
import 'package:goldenprizma/presentation/widgets/auth_wrapper.dart';
import 'package:goldenprizma/presentation/widgets/scroll_loader.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:ionicons/ionicons.dart';
import 'package:provider/provider.dart';

class Notifications extends StatefulWidget {
  const Notifications({super.key, this.notificationModel});

  static const routeName = '/notifications';

  static pageRoute({NotificationModel? notificationModel}) {
    return MaterialPageRoute<void>(
      builder: (BuildContext context) => AuthWrapper(
          child: Notifications(
        notificationModel: notificationModel,
      )),
    );
  }

  final NotificationModel? notificationModel;

  @override
  _NotificationsState createState() => _NotificationsState();
}

class _NotificationsState extends State<Notifications> {
  final ScrollController _scrollController = ScrollController();
  bool isScreenOpen = false;

  void _debouncedListener() {
    EasyDebounce.debounce(
      'scroll-listener',
      const Duration(milliseconds: 300),
      _scrollListener,
    );
  }

  void _scrollListener() {
    if (isBottom) {
      context.read<NotificationBloc>().add(NotificationRequestLoad());
    }
  }

  @override
  void initState() {
    super.initState();
    isScreenOpen = true;
    _scrollController.addListener(_debouncedListener);
    if (widget.notificationModel != null) {
      showNotificationDialog(context, widget.notificationModel!);
    }
    Future.delayed(const Duration(seconds: Env.notificationDelay))
        .then((value) {
      if (isScreenOpen && mounted) {
        context.read<NotificationBloc>().add(const NotificationMarkAllAsRead());
      }
    });
  }

  @override
  void deactivate() {
    isScreenOpen = false;
    super.deactivate();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        title: Text(context.loc.notifications),
        centerTitle: true,
      ),
      body: BlocBuilder(
        bloc: BlocProvider.of<NotificationBloc>(context),
        builder: (context, NotificationState state) {
          //initial ui state
          if (state.status == NotificationStatus.initial) {
            return const Center(child: CircularProgressIndicator.adaptive());
          }

          //failure ui state
          if (state.status == NotificationStatus.failure) {
            return Center(child: Text(context.loc.generalErrorMessage));
          }

          //success ui state
          return RefreshIndicator(
            onRefresh: () async {
              context
                  .read<NotificationBloc>()
                  .add(NotificationRequestRefresh());
            },
            child: ListView.separated(
              controller: _scrollController,
              physics: const AlwaysScrollableScrollPhysics(),
              itemBuilder: (_, index) {
                if (index < state.notifications.length) {
                  NotificationModel notification = state.notifications[index];
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: _buildNotificationRow(notification),
                  );
                }
                Timer(const Duration(milliseconds: 0), () {
                  _scrollController
                      .jumpTo(_scrollController.position.maxScrollExtent - 10);
                });
                return scrollLoaderIndicator();
              },
              separatorBuilder: (_, __) => const Divider(
                thickness: 0.5,
                height: 2,
              ),
              itemCount: state.notifications.length +
                  (state.status == NotificationStatus.loading ? 1 : 0),
            ),
          );
        },
      ),
    );
  }

  Widget _buildNotificationRow(NotificationModel notification) {
    return ListTile(
      tileColor: notification.isRead
          ? Theme.of(context).listTileTheme.tileColor
          : Provider.of<AppProvider>(context).isDarkMode(context)
              ? Colors.grey.shade900
              : Colors.grey.shade100,
      onTap: () {
        if (notification.isType('order')) {
          Navigator.of(context).push(OrderDetailScreen.routePage(
            notification.modelId,
            orderBloc: context.read<OrderBloc>(),
          ));
          context
              .read<NotificationBloc>()
              .add(NotificationMarkAsRead(id: notification.id));
        } else if (notification.isType('ticket')) {
          Navigator.of(context).push(SupportScreen.pageRoute());
          context
              .read<NotificationBloc>()
              .add(NotificationMarkAsRead(id: notification.id));
        } else if (notification.isType('offer')) {
          showNotificationDialog(context, notification);
        }
      },
      title: Text(
        notification.title,
        textDirection: isArabicOrKurdish(notification.title)
            ? TextDirection.rtl
            : TextDirection.ltr,
        style: Theme.of(context)
            .textTheme
            .titleSmall
            ?.copyWith(fontWeight: FontWeight.bold, fontFamily: "NotoSans"),
      ),
      subtitle: Padding(
        padding: const EdgeInsets.only(top: 4.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              notification.body,
              textDirection: isArabicOrKurdish(notification.body)
                  ? TextDirection.rtl
                  : TextDirection.ltr,
              style: Theme.of(context)
                  .textTheme
                  .bodySmall
                  ?.copyWith(fontFamily: "NotoSans"),
            ),
            Row(
              mainAxisAlignment: isArabicOrKurdish(notification.createdAt)
                  ? MainAxisAlignment.start
                  : MainAxisAlignment.spaceBetween,
              children: [
                const SizedBox(width: 4),
                Text(
                  notification.createdAt,
                  textDirection: isArabicOrKurdish(notification.createdAt)
                      ? TextDirection.rtl
                      : TextDirection.ltr,
                  style: Theme.of(context)
                      .textTheme
                      .bodySmall
                      ?.copyWith(fontFamily: "NotoSans", fontSize: 11),
                )
              ],
            )
          ],
        ),
      ),
      leading: notification.image != null
          ? NotificationImage(notification: notification)
          : Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: notification.isType('offer')
                    ? AppColors.primaryColor
                    : Theme.of(context).cardColor,
              ),
              child: Center(
                child: Icon(
                  notification.isType('news')
                      ? Ionicons.notifications
                      : notification.isType('offer')
                          ? Icons.discount
                          : Ionicons.notifications,
                  color: notification.isType('offer')
                      ? Colors.white
                      : AppColors.primaryColor,
                  size: 20.0,
                ),
              ),
            ),
    );
  }

  @override
  void dispose() {
    _scrollController
      ..removeListener(_debouncedListener)
      ..dispose();
    EasyDebounce.cancel('scroll-listener');

    super.dispose();
  }

  bool get isBottom =>
      _scrollController.position.atEdge &&
      _scrollController.position.pixels != 0;
}

class NotificationImage extends StatelessWidget {
  final NotificationModel notification;
  const NotificationImage({
    required this.notification,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (kDebugMode && notification.image == '') {
      return Container(
        width: 48,
        height: 48,
        color: Theme.of(context).cardColor,
      );
    }
    return CachedNetworkImage(
      imageUrl: "${notification.image}",
      width: 48,
      height: 48,
      fit: BoxFit.cover,
      progressIndicatorBuilder: (context, url, progress) => Container(
        width: 48,
        height: 48,
        color: Theme.of(context).cardColor,
        child: const CircularProgressIndicator.adaptive(),
      ),
      errorWidget: (context, url, error) => Container(
        width: 48,
        height: 48,
        color: Theme.of(context).cardColor,
      ),
    );
  }
}

void showNotificationDialog(
    BuildContext context, NotificationModel notification) {
  showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          contentPadding: EdgeInsets.zero,
          content: SingleChildScrollView(
              child: Stack(
            alignment: Alignment.bottomCenter,
            clipBehavior: Clip.none,
            children: [
              Column(children: [
                if (notification.image != null && notification.image != '')
                  CachedNetworkImage(
                    placeholder: (context, url) {
                      return Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: const Center(
                          child: CircularProgressIndicator.adaptive(
                            valueColor: AlwaysStoppedAnimation<Color>(
                                AppColors.primaryColor),
                          ),
                        ),
                      );
                    },
                    imageUrl: notification.image ??
                        '', //'https://cdn.pixabay.com/photo/2023/03/28/09/29/tulip-7882705_1280.jpg'
                    imageBuilder: (context, imageProvider) {
                      return Container(
                        width: double.infinity,
                        height: 200,
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(16),
                            topRight: Radius.circular(16),
                          ),
                          image: DecorationImage(
                            image: imageProvider,
                            fit: BoxFit.cover,
                          ),
                        ),
                      );
                    },
                    errorWidget: (context, url, error) {
                      return const SizedBox(
                        width: double.infinity,
                        height: 0,
                      );
                    },
                  ),
                Padding(
                  padding: const EdgeInsets.only(
                      top: 16, bottom: 8, left: 16, right: 16),
                  child: Text(
                    notification.title,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold, fontFamily: "NotoSans"),
                  ),
                ),
                if (notification.image != null && notification.image != '')
                  const SizedBox(height: 12),
                Padding(
                  padding: const EdgeInsets.only(
                      bottom: 24, left: 24, right: 24, top: 8),
                  child: Text(
                    textDirection: isArabicOrKurdish(notification.body)
                        ? TextDirection.rtl
                        : TextDirection.ltr,
                    notification.body,
                    style: Theme.of(context)
                        .textTheme
                        .bodyMedium
                        ?.copyWith(fontFamily: "NotoSans"),
                  ),
                ),
              ]),
              Positioned(
                  bottom: -64,
                  child: GestureDetector(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: SizedBox(
                      height: 48,
                      width: 48,
                      // close button
                      child: Container(
                        padding: const EdgeInsets.all(5),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.white, width: 1.2),
                          shape: BoxShape.rectangle,
                          borderRadius: BorderRadius.circular(48),
                        ),
                        child: const Icon(HugeIcons.strokeRoundedCancel01,
                            color: Colors.white),
                      ),
                    ),
                  ))
            ],
          )),
        );
      });
}
