import 'dart:async';

import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/accounting/models/transaction.dart';
import 'package:goldenprizma/domain/accounting/bloc/transaction_bloc.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/widgets/scroll_loader.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:ionicons/ionicons.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import 'package:intl/intl.dart';

class TransactionsScreen extends StatefulWidget {
  static const String routeName = '/transactions';

  const TransactionsScreen({super.key});

  @override
  State<TransactionsScreen> createState() => _TransactionsScreenState();
}

class _TransactionsScreenState extends State<TransactionsScreen>
    with TickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  final String _debounceID = 'transactions_scroll_listener';
  late TabController _tabController;

  // Filter states
  DateTimeRange? _selectedDateRange;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _scrollController.addListener(_debouncedListener);
    context.read<TransactionBloc>().add(const TransactionRequestRefresh());
  }

  @override
  Widget build(BuildContext context) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    return Scaffold(
      backgroundColor:
          isDarkMode ? AppColors.boxDarkColor : Colors.grey.shade50,
      appBar: _buildAppBar(context, isDarkMode),
      body: Column(
        children: [
          _buildSearchAndFilters(context, isDarkMode),
          _buildTabBar(context, isDarkMode),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildTransactionsList(context, 'All'),
                _buildTransactionsList(context, 'Cash-In'),
                _buildTransactionsList(context, 'Cash-Out'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context, bool isDarkMode) {
    return AppBar(
      backgroundColor: isDarkMode ? AppColors.boxDarkColor : Colors.white,
      elevation: 0,
      leading: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => Navigator.of(context).pop(),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? Colors.white.withOpacity(0.1)
                  : Colors.black.withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.arrow_back,
              color: isDarkMode ? Colors.white : Colors.black,
              size: 20,
            ),
          ),
        ),
      ),
      title: Text(
        context.loc.transactionHistory,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.black,
            ),
      ),
    );
  }

  Widget _buildSearchAndFilters(BuildContext context, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: isDarkMode ? AppColors.boxDarkColor : Colors.white,
      child: Column(
        children: [
          // Date Range Picker
          GestureDetector(
            onTap: () async {
              DateTimeRange? dateRange = await showDateRangePicker(
                context: context,
                initialDateRange: _selectedDateRange,
                firstDate: DateTime(2021),
                lastDate: DateTime.now(),
                builder: (context, child) {
                  return Theme(
                    data: Theme.of(context).copyWith(
                      colorScheme: Theme.of(context).colorScheme.copyWith(
                            primary: AppColors.primaryColor,
                          ),
                    ),
                    child: child!,
                  );
                },
              );

              if (dateRange != null) {
                setState(() {
                  _selectedDateRange = dateRange;
                });
              }
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
              decoration: BoxDecoration(
                color: isDarkMode ? Colors.grey[800] : Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: _selectedDateRange != null
                      ? AppColors.primaryColor.withOpacity(0.3)
                      : Colors.transparent,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Ionicons.calendar_outline,
                    color: isDarkMode ? Colors.white60 : Colors.grey[600],
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _selectedDateRange != null
                          ? '${DateFormat('MMM dd, yyyy').format(_selectedDateRange!.start)} - ${DateFormat('MMM dd, yyyy').format(_selectedDateRange!.end)}'
                          : context.loc.selectDateRange,
                      style: TextStyle(
                        color: _selectedDateRange != null
                            ? (isDarkMode ? Colors.white : Colors.black87)
                            : (isDarkMode ? Colors.white60 : Colors.grey[600]),
                        fontSize: 15,
                        fontWeight: _selectedDateRange != null
                            ? FontWeight.w500
                            : FontWeight.normal,
                      ),
                    ),
                  ),
                  if (_selectedDateRange != null) ...[
                    const SizedBox(width: 8),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedDateRange = null;
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: AppColors.primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Ionicons.close,
                          color: AppColors.primaryColor,
                          size: 16,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar(BuildContext context, bool isDarkMode) {
    return Container(
      color: isDarkMode ? AppColors.boxDarkColor : Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primaryColor,
        unselectedLabelColor: isDarkMode ? Colors.white60 : Colors.grey[600],
        indicatorColor: AppColors.primaryColor,
        indicatorWeight: 3,
        labelStyle: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
        tabs: [
          Tab(text: context.loc.all),
          Tab(text: context.loc.cashIn),
          Tab(text: context.loc.cashOut),
        ],
      ),
    );
  }

  Widget _buildTransactionsList(BuildContext context, String filter) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<TransactionBloc>().add(const TransactionRequestRefresh());
      },
      child: BlocBuilder<TransactionBloc, TransactionState>(
        builder: (context, state) {
          if (state.status == TransactionStatus.initial) {
            return _shimmerList(context);
          }

          if (state.status == TransactionStatus.failure) {
            return _buildErrorState(context);
          }

          List<Transaction> filteredTransactions = _filterTransactions(
            state.transactions,
            filter,
          );

          if (filteredTransactions.isEmpty) {
            return _buildEmptyState(context, filter);
          }

          return ListView.separated(
            controller: _scrollController,
            padding: const EdgeInsets.all(16),
            itemBuilder: (context, index) {
              if (index < filteredTransactions.length) {
                return _transactionRow(context, filteredTransactions[index]);
              }

              Timer(const Duration(milliseconds: 0), () {
                _scrollController
                    .jumpTo(_scrollController.position.maxScrollExtent - 10);
              });
              return scrollLoaderIndicator();
            },
            separatorBuilder: (_, __) => const SizedBox(height: 8),
            itemCount: filteredTransactions.length +
                (state.status == TransactionStatus.loading ? 1 : 0),
          );
        },
      ),
    );
  }

  List<Transaction> _filterTransactions(
      List<Transaction> transactions, String filter) {
    List<Transaction> filtered = transactions;

    // Apply transaction type filter
    if (filter == 'Cash-In') {
      filtered = filtered.where((t) => t.type == 'CRE').toList();
    } else if (filter == 'Cash-Out') {
      filtered = filtered.where((t) => t.type == 'DEB').toList();
    }

    // Apply date range filter
    if (_selectedDateRange != null) {
      filtered = filtered.where((t) {
        if (t.createdAt.isEmpty) return false;

        try {
          DateTime transactionDate = DateTime.parse(t.createdAt.split(' ')[0]);
          return transactionDate.isAfter(_selectedDateRange!.start
                  .subtract(const Duration(days: 1))) &&
              transactionDate.isBefore(
                  _selectedDateRange!.end.add(const Duration(days: 1)));
        } catch (e) {
          return false;
        }
      }).toList();
    }

    return filtered;
  }

  Widget _transactionRow(BuildContext context, Transaction transaction) {
    final bool isCredit = transaction.type == "CRE";
    final bool isDeposit = transaction.paymentType.contains('Deposit') ||
        transaction.paymentType.contains('ZainCash') ||
        transaction.paymentType.contains('FIB');

    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);
    final Color cardColor = isDarkMode ? AppColors.boxDarkColor : Colors.white;
    final Color iconColor = isCredit ? Colors.green : Colors.red;

    // Different icons for different transaction types
    IconData typeIcon;
    if (isDeposit) {
      typeIcon = isCredit
          ? HugeIcons.strokeRoundedWalletAdd02
          : HugeIcons.strokeRoundedWalletRemove02;
    } else {
      typeIcon = isCredit ? Ionicons.caret_up : Ionicons.caret_down;
    }

    return Container(
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade200,
          width: 1,
        ),
      ),
      padding: const EdgeInsets.all(16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(typeIcon, color: iconColor, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        transaction.paymentType,
                        style: TextStyle(
                          color: isDarkMode ? Colors.white : Colors.grey[900],
                          fontWeight: FontWeight.w600,
                          fontSize: 15,
                        ),
                      ),
                    ),
                    if (isDeposit) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Text(
                          context.loc.deposit,
                          style: const TextStyle(
                            color: AppColors.primaryColor,
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 6),
                Text(
                  transaction.note,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: isDarkMode ? Colors.white60 : Colors.grey[600],
                        fontSize: 12,
                      ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
                const SizedBox(height: 6),
                Row(
                  children: [
                    Icon(
                      Ionicons.time_outline,
                      size: 12,
                      color: isDarkMode ? Colors.white38 : Colors.grey[500],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${transaction.dateOnly} • ${transaction.timeOnly}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color:
                                isDarkMode ? Colors.white38 : Colors.grey[500],
                            fontSize: 11,
                          ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '${isCredit ? '+' : '-'}${transaction.amount}',
                style: TextStyle(
                  color: iconColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: iconColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  isCredit ? context.loc.credit : context.loc.debit,
                  style: TextStyle(
                    color: iconColor,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, String filter) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    String emptyMessage;
    IconData emptyIcon;

    switch (filter) {
      case 'Cash-In':
        emptyMessage = context.loc.noCashInTransactionsFound;
        emptyIcon = HugeIcons.strokeRoundedWalletAdd02;
        break;
      case 'Cash-Out':
        emptyMessage = context.loc.noCashOutTransactionsFound;
        emptyIcon = HugeIcons.strokeRoundedWalletRemove02;
        break;
      default:
        emptyMessage = context.loc.noTransactionsFound;
        emptyIcon = Ionicons.receipt_outline;
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppColors.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                emptyIcon,
                size: 40,
                color: AppColors.primaryColor,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              emptyMessage,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: isDarkMode ? Colors.white70 : Colors.grey[700],
                    fontWeight: FontWeight.w600,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              _selectedDateRange != null
                  ? context.loc.noTransactionsFoundInSelectedDateRange
                  : context.loc.yourTransactionHistoryWillAppearHere,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: isDarkMode ? Colors.white60 : Colors.grey[600],
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Ionicons.warning_outline,
                size: 40,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              context.loc.somethingWentWrong,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: isDarkMode ? Colors.white70 : Colors.grey[700],
                    fontWeight: FontWeight.w600,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              context.loc.failedToLoadTransactionsTryAgain,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: isDarkMode ? Colors.white60 : Colors.grey[600],
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                context.read<TransactionBloc>().add(TransactionRequestLoad());
              },
              icon: const Icon(Ionicons.refresh),
              label: Text(context.loc.tryAgain),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _shimmerList(BuildContext context) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);
    final Color cardColor = isDarkMode ? AppColors.boxDarkColor : Colors.white;

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemBuilder: (_, __) => Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: Shimmer.fromColors(
          baseColor: isDarkMode ? AppColors.boxDarkColor : Colors.grey.shade300,
          highlightColor: isDarkMode ? Colors.black45 : Colors.grey.shade100,
          enabled: true,
          child: Container(
            decoration: BoxDecoration(
              color: cardColor,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade200,
                width: 1,
              ),
            ),
            padding: const EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 120,
                        height: 14.0,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(7),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: 180,
                        height: 12.0,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: 100,
                        height: 10.0,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(5),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Container(
                      width: 70.0,
                      height: 16.0,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: 50.0,
                      height: 12.0,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
      itemCount: 10,
    );
  }

  void _debouncedListener() {
    EasyDebounce.debounce(
      _debounceID,
      const Duration(milliseconds: 300),
      _onScroll,
    );
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<TransactionBloc>().add(TransactionRequestLoad());
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) {
      return false;
    }
    return _scrollController.position.atEdge &&
        _scrollController.position.pixels != 0;
  }

  @override
  void dispose() {
    _scrollController
      ..removeListener(_debouncedListener)
      ..dispose();
    _tabController.dispose();
    EasyDebounce.cancel(_debounceID);
    super.dispose();
  }
}
