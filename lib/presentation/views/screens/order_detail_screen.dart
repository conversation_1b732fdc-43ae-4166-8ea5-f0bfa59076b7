import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:goldenprizma/domain/orders/bloc/order_approval_bloc.dart';
import 'package:goldenprizma/domain/orders/bloc/order_bloc.dart';
import 'package:goldenprizma/domain/orders/bloc/reorder_bloc.dart';
import 'package:goldenprizma/domain/orders/models/order.dart';
import 'package:goldenprizma/domain/orders/models/order_query.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/helpers/custom_snackbars.dart';
import 'package:goldenprizma/helpers/extract_links.dart';
import 'package:goldenprizma/helpers/remove_links.dart';
import 'package:goldenprizma/main.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/screens/home_screen.dart';
import 'package:goldenprizma/presentation/views/screens/website_launcher.dart';
import 'package:goldenprizma/repositories/order_repository.dart';
import 'package:goldenprizma/repositories/products_repository.dart';
import 'package:goldenprizma/presentation/views/pages/store/product_detail_screen.dart';
import 'package:ionicons/ionicons.dart';

class OrderDetailScreen extends StatelessWidget {
  OrderDetailScreen({super.key, required this.id, this.order});

  static MaterialPageRoute routePage(int id, {required OrderBloc orderBloc}) =>
      MaterialPageRoute(builder: (BuildContext context) {
        return MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (context) => OrderApprovalBloc(),
            ),
            BlocProvider.value(value: orderBloc)
          ],
          child: OrderDetailScreen(id: id),
        );
      });

  final int id;
  final Order? order;
  final OrderRepository _orderRepository = getIt.get<OrderRepository>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.loc.orderDetails),
        elevation: 0,
        centerTitle: false,
        actions: const [
          // GestureDetector(
          //   onTap: () {
          //     Navigator.of(context).push(
          //       ChatSubjects.pageRoute(type: 'order', orderId: "$id"),
          //     );
          //   },
          //   child: Padding(
          //     padding: const EdgeInsetsDirectional.only(end: 16),
          //     child: Row(
          //       children: [
          //         const Icon(HugeIcons.strokeRoundedCustomerService01),
          //         const SizedBox(width: 8),
          //         Text(context.loc.support),
          //       ],
          //     ),
          //   ),
          // ),
        ],
      ),
      body: FutureBuilder(
        future: _orderRepository.getOrder(orderId: id),
        builder: _buildBody,
      ),
    );
  }

  Widget _buildBody(BuildContext context, AsyncSnapshot<Order> snapshot) {
    if (snapshot.connectionState == ConnectionState.waiting) {
      return const Center(
        child: CircularProgressIndicator.adaptive(),
      );
    }

    if (snapshot.hasError) {
      return Center(
        child: Text(context.loc.generalErrorMessage),
      );
    }
    Order? order = snapshot.data;
    bool isCreatedStatus = order!.status.toLowerCase() == 'created';
    String calculating = context.loc.calculating;

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(order, context),
          Row(
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
                child: Text(
                  context.loc.orderDetails,
                  style: Theme.of(context).textTheme.titleSmall,
                ),
              ),
              const Spacer(),
              if (order.canReorder)
                Padding(
                  padding:
                      const EdgeInsets.only(right: 16.0, top: 4.0, bottom: 4.0),
                  child: _buildReorderButton(context, order.id),
                ),
              if (order.isStoreOrder)
                Padding(
                  padding:
                      const EdgeInsets.only(right: 16.0, top: 4.0, bottom: 4.0),
                  child:
                      _buildViewProductButton(context, order.storeProductId!),
                ),
            ],
          ),
          if (order.showAcceptReject)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: _buildAceptOrRejectButton(
                order,
                context,
                order.totalPrice,
                order.id,
                context.read<OrderApprovalBloc>(),
                context.read<OrderBloc>(),
              ),
            ),
          const Divider(height: 8),
          _buildListRow(context.loc.idLable, "${order.id}", context),
          const Divider(height: 1),
          _buildStatusRow(context.loc.status, order.status, context),
          const Divider(height: 1),
          // button to show dialog
          if (order.canViewReason())
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
              child: Row(
                children: [
                  SizedBox(
                    width: 140,
                    child: Text(context.loc.reason,
                        style: Theme.of(context).textTheme.bodySmall),
                  ),
                  const SizedBox(width: 40),
                  Flexible(
                    child: OutlinedButton(
                      style: OutlinedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.surface,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 0, vertical: 0),
                        visualDensity: const VisualDensity(vertical: -2),
                        side: const BorderSide(color: Colors.transparent),
                      ),
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            icon: const Icon(Ionicons.help_circle_outline),
                            title: Text(context.loc.rejectionReasons),
                            elevation: 1,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            content: SingleChildScrollView(
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: order.rejectionReasons.map(
                                  (reason) {
                                    List<String> links =
                                        extractLinks(reason.reason);
                                    if (links.isNotEmpty) {
                                      String reasonWithoutLinks =
                                          removeLinks(reason.reason);
                                      List<Widget> children = List.of(
                                        [
                                          Text(reasonWithoutLinks.trim()),
                                        ],
                                      )..addAll(
                                          links
                                              .map(
                                                (link) => InkWell(
                                                  onTap: () {
                                                    Navigator.of(context).push(
                                                        MaterialPageRoute(
                                                            builder: (context) {
                                                      return WebsiteLauncher(
                                                        url: link,
                                                        websiteName: "Link",
                                                      );
                                                    }));
                                                  },
                                                  child: Text(
                                                    link,
                                                    style: const TextStyle(
                                                      color: Colors.blue,
                                                      decoration: TextDecoration
                                                          .underline,
                                                    ),
                                                  ),
                                                ),
                                              )
                                              .toList(),
                                        );
                                      return ListTile(
                                        title: Text(
                                          reason.date,
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyMedium
                                              ?.copyWith(
                                                color: AppColors.primaryColor,
                                              ),
                                        ),
                                        subtitle: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: children,
                                        ),
                                      );
                                    }

                                    return ListTile(
                                      title: Text(reason.date,
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyMedium
                                              ?.copyWith(
                                                color: AppColors.primaryColor,
                                              )),
                                      subtitle: Text(
                                        reason.reason,
                                        style: Theme.of(context)
                                            .textTheme
                                            .titleMedium,
                                      ),
                                    );
                                  },
                                ).toList(),
                              ),
                            ),
                            actions: [
                              TextButton(
                                onPressed: () {
                                  Navigator.of(context, rootNavigator: true)
                                      .pop();
                                },
                                child: Text(context.loc.close),
                              )
                            ],
                          ),
                        );
                      },
                      child: Text(
                        context.loc.viewReason,
                        style: const TextStyle(
                            color: AppColors.primaryColor,
                            fontSize: 13,
                            letterSpacing: 0.1),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          const Divider(height: 1),
          if (!order.isStoreOrder)
            _buildListRow(context.loc.link, order.link, context, link: true),
          if (!order.isStoreOrder) const Divider(height: 1),
          _buildListRow(context.loc.brand, "${order.brand}", context),
          const Divider(height: 1),
          _buildListRow(context.loc.size, "${order.size}", context),
          const Divider(height: 1),
          _buildListRow(context.loc.quantity, "${order.quantity}", context),
          const Divider(height: 1),
          _buildListRow(context.loc.itemPrice,
              isCreatedStatus ? calculating : order.itemPrice, context),
          const Divider(height: 1),
          _buildListRow(context.loc.itemPriceUSD,
              isCreatedStatus ? calculating : "${order.itemUsdPrice}", context),
          const Divider(height: 1),
          _buildListRow(context.loc.shippingPriceUSD,
              isCreatedStatus ? calculating : order.shippingPrice, context),
          const Divider(height: 1),
          _buildListRow(context.loc.internalPriceUSD,
              isCreatedStatus ? calculating : order.internalShipping, context),
          const Divider(height: 1),
          _buildListRow(context.loc.commissionUSD,
              isCreatedStatus ? calculating : "${order.commission}", context),
          const Divider(height: 1),
          _buildListRow(context.loc.tax,
              isCreatedStatus ? calculating : "${order.tax}", context),
          const Divider(height: 1),
          _buildListRow(context.loc.totalUSD,
              isCreatedStatus ? calculating : order.totalPrice, context,
              shippingPrice: order.shippingPriceRaw),
          const Divider(height: 1),
          _buildListRow(context.loc.description, order.description, context),
          const Divider(height: 1),
          _buildListRow(context.loc.createdAt, order.createdAt, context),
          const Divider(height: 24),
        ],
      ),
    );
  }

  Widget _buildListRow(label, data, context,
      {bool link = false, double shippingPrice = 0}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8.0),
      child: Row(
        children: [
          SizedBox(
            width: 140,
            child: Text(label, style: Theme.of(context).textTheme.bodySmall),
          ),
          const SizedBox(width: 40),
          Flexible(
              child: link
                  ? GestureDetector(
                      onTap: () async {
                        Navigator.of(context).push(MaterialPageRoute(
                            builder: (context) => WebsiteLauncher(
                                  url: data,
                                  websiteName: data,
                                  minimalDesign: true,
                                )));
                      },
                      onLongPress: () {
                        // Copy link to clipboard on long press
                        Clipboard.setData(ClipboardData(text: data));
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Link copied to clipboard'),
                            behavior: SnackBarBehavior.floating,
                            duration: Duration(seconds: 2),
                          ),
                        );
                      },
                      child: Text(
                        "$data",
                        style: const TextStyle(
                          fontSize: 13,
                          color: Colors.blue,
                          decoration: TextDecoration.underline,
                        ),
                        overflow: TextOverflow.fade,
                        maxLines: 1,
                        softWrap: false,
                      ))
                  : Text(
                      "$data",
                      style: const TextStyle(fontSize: 13),
                      overflow: TextOverflow.fade,
                      maxLines: 1,
                      softWrap: false,
                    )),
        ],
      ),
    );
  }

  Widget _buildStatusRow(label, status, BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8.0),
      child: Row(
        children: [
          SizedBox(
            width: 140,
            child: Text(label, style: Theme.of(context).textTheme.bodySmall),
          ),
          const SizedBox(width: 40),
          Flexible(
              child: Row(
            children: [
              Text(
                status,
                style: const TextStyle(fontSize: 13),
                overflow: TextOverflow.fade,
                maxLines: 1,
                softWrap: false,
              ),
              if (status.toLowerCase() == 'created') const SizedBox(width: 6),
              if (status.toLowerCase() == 'created')
                Text(
                  context.loc.orderProcessingHelper,
                  style: const TextStyle(fontSize: 10, color: Colors.red),
                  overflow: TextOverflow.fade,
                ),
            ],
          )),
        ],
      ),
    );
  }

  SizedBox _buildHeader(Order? order, BuildContext context) {
    return SizedBox(
      height: 250,
      child: AspectRatio(
        aspectRatio: 16 / 9,
        child: InteractiveViewer(
          constrained: true,
          child: Hero(
            tag: 'order_product_${order!.storeProductId}',
            child: Image(
              image: NetworkImage(order.image),
              fit: BoxFit.contain,
              errorBuilder: (context, error, stackTrace) {
                // Silently handle image loading errors
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.image_not_supported_outlined,
                          size: 48, color: Colors.grey),
                      const SizedBox(height: 16),
                      Text(
                        'Image not available',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                    ],
                  ),
                );
              },
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Center(
                  child: CircularProgressIndicator(
                    value: loadingProgress.expectedTotalBytes != null
                        ? loadingProgress.cumulativeBytesLoaded /
                            loadingProgress.expectedTotalBytes!
                        : null,
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAceptOrRejectButton(
    Order order,
    BuildContext context,
    price,
    int orderId,
    OrderApprovalBloc approvalBloc,
    OrderBloc orderBloc,
  ) {
    if (order.canConfirmAgain()) {
      return SizedBox(
        width: max(MediaQuery.of(context).size.width / 2, 170),
        height: 40,
        child: OutlinedButton(
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 0),
            backgroundColor: Theme.of(context).primaryColor,
            foregroundColor: Colors.white,
            visualDensity:
                const VisualDensity(vertical: VisualDensity.minimumDensity),
            side: BorderSide(color: Theme.of(context).primaryColor, width: 1),
          ),
          onPressed: () {
            final rejectionReason = order.rejectionReasons.first;
            showDialog(
              context: context,
              builder: (context) => _accepOrRejectDialog(
                reason: rejectionReason.reason,
                context: context,
                orderId: orderId,
                approvalBloc: approvalBloc,
                orderBloc: orderBloc,
                canReply: rejectionReason.canReply,
              ),
            );
          },
          child: Text(
            AppLocalizations.of(context)!.checkOrder.toUpperCase(),
            style: const TextStyle(
                color: Colors.white,
                fontSize: 11,
                letterSpacing: 0.5,
                fontWeight: FontWeight.w700),
          ),
        ),
      );
    }

    return Row(
      children: [
        Flexible(
          flex: 1,
          child: SizedBox(
            width: double.infinity,
            height: 36,
            child: TextButton(
              style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                backgroundColor: Theme.of(context).primaryColor,
              ),
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => _confirmDialog(
                    isAccept: true,
                    totalMoney: price,
                    context: context,
                    orderId: orderId,
                    approvalBloc: approvalBloc,
                    orderBloc: orderBloc,
                  ),
                );
              },
              child: Text(
                AppLocalizations.of(context)!.accept.toUpperCase(),
                style: const TextStyle(
                    color: Colors.white, fontSize: 11, letterSpacing: 0.5),
              ),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Flexible(
          flex: 1,
          child: SizedBox(
            width: double.infinity,
            height: 36,
            child: TextButton(
              style: TextButton.styleFrom(
                  padding: EdgeInsets.zero,
                  side: const BorderSide(color: Colors.red, width: 1)),
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => _confirmDialog(
                    isAccept: false,
                    totalMoney: price,
                    context: context,
                    orderId: orderId,
                    approvalBloc: approvalBloc,
                    orderBloc: orderBloc,
                  ),
                );
              },
              child: Text(
                AppLocalizations.of(context)!.reject.toUpperCase(),
                style: const TextStyle(
                    color: Colors.red, fontSize: 11, letterSpacing: 0.5),
              ),
            ),
          ),
        ),
      ],
    );
  }

  _confirmDialog({
    bool isAccept = false,
    required String totalMoney,
    required BuildContext context,
    required int orderId,
    required OrderApprovalBloc approvalBloc,
    required OrderBloc orderBloc,
  }) {
    TextEditingController reasonTextController = TextEditingController();
    String actionText = isAccept
        ? AppLocalizations.of(context)!.accept
        : AppLocalizations.of(context)!.reject;

    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: approvalBloc),
        BlocProvider.value(value: orderBloc),
      ],
      child: BlocListener<OrderApprovalBloc, OrderApprovalState>(
        bloc: approvalBloc,
        listener: (context, state) {
          if (state.status == OrderApprovalStatus.success) {
            showSuccessSnackBar(
                context: context, message: state.successMessage);
            context
                .read<OrderBloc>()
                .add(const OrderReload(query: OrderQuery()));
            Navigator.of(context).push(MaterialPageRoute(
              builder: (context) =>
                  const HomeScreen(initialPage: 3), // MyOrdersPage
            ));
          }

          if (state.status == OrderApprovalStatus.failure &&
              state.hasApiError) {
            Navigator.of(context).push(MaterialPageRoute(
              builder: (context) =>
                  const HomeScreen(initialPage: 3), // MyOrdersPage
            ));
            showErrorSnackBar(context: context, message: state.errorMessage);
          }
        },
        child: AlertDialog(
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          actionsPadding: const EdgeInsets.only(right: 16, bottom: 16),
          title: Text(isAccept
              ? AppLocalizations.of(context)!.acceptOrderDialogTitle
              : AppLocalizations.of(context)!.rejectOrderDialogTitle),
          content: Column(mainAxisSize: MainAxisSize.min, children: [
            Text(
              AppLocalizations.of(context)!
                  .orderDialogContent(actionText.toLowerCase(), totalMoney),
            ),
            const SizedBox(height: 24),
            // add a form field for rejection reason if isAccept is false
            if (!isAccept)
              DropdownButtonFormField<String>(
                items: [
                  context.loc.dontNeedIt,
                  context.loc.highShipping,
                  context.loc.highPrice,
                ]
                    .toList()
                    .map((reason) => DropdownMenuItem(
                          value: reason,
                          child: Text(reason),
                        ))
                    .toList(),
                onChanged: (value) {
                  reasonTextController.value = TextEditingValue(text: value!);
                },
                decoration: InputDecoration(
                  labelText: context.loc.rejectReason,
                  isDense: true,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            if (!isAccept) const SizedBox(height: 16),
            // show a textarea with height if Other is selected from above dropdown and isAccept is false
            if (!isAccept)
              TextFormField(
                maxLength: 500,
                maxLines: 3,
                controller: reasonTextController,
                decoration: InputDecoration(
                  labelText: "Other",
                  isDense: true,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
          ]),
          actions: [
            BlocBuilder<OrderApprovalBloc, OrderApprovalState>(
              builder: (context, state) {
                return MaterialButton(
                  elevation: 0,
                  padding: EdgeInsets.zero,
                  visualDensity: const VisualDensity(
                      vertical: VisualDensity.minimumDensity),
                  color: isAccept ? Theme.of(context).primaryColor : Colors.red,
                  onPressed: () {
                    if (isAccept) {
                      context
                          .read<OrderApprovalBloc>()
                          .add(AcceptOrder(orderId: orderId));
                    } else {
                      context.read<OrderApprovalBloc>().add(RejectOrder(
                            orderId: orderId,
                            reason: reasonTextController.text,
                          ));
                    }
                  },
                  child: state.status == OrderApprovalStatus.loading
                      ? const CircularProgressIndicator.adaptive()
                      : Text(
                          actionText,
                          style: const TextStyle(color: Colors.white),
                        ),
                );
              },
            ),
            BlocBuilder<OrderApprovalBloc, OrderApprovalState>(
              builder: (context, state) {
                return MaterialButton(
                  elevation: 0,
                  visualDensity: const VisualDensity(
                      vertical: VisualDensity.minimumDensity),
                  onPressed: state.status == OrderApprovalStatus.loading
                      ? null
                      : () {
                          Navigator.of(context, rootNavigator: true).pop();
                        },
                  child: Text(
                    AppLocalizations.of(context)!.notNow,
                  ),
                );
              },
            )
          ],
        ),
      ),
    );
  }

  Widget _buildReorderButton(BuildContext context, int orderId) {
    return SizedBox(
      height: 36,
      child: ElevatedButton.icon(
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).primaryColor,
            foregroundColor: Colors.white,
            elevation: 0, // Remove shadow
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16), // Increased radius
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
          ),
          onPressed: () {
            // Create a new instance of ReorderBloc
            final reorderBloc = ReorderBloc();

            showDialog(
              context: context,
              builder: (context) => _reorderConfirmDialog(
                context: context,
                orderId: orderId,
                reorderBloc: reorderBloc,
                orderBloc: context.read<OrderBloc>(),
              ),
            );
          },
          icon: const Icon(Icons.refresh, size: 16),
          label: const Padding(
            padding:
                EdgeInsets.only(left: 4), // Add spacing between icon and text
            child: Text(
              'Reorder',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          )),
    );
  }

  Widget _reorderConfirmDialog({
    required BuildContext context,
    required int orderId,
    required ReorderBloc reorderBloc,
    required OrderBloc orderBloc,
  }) {
    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: reorderBloc),
        BlocProvider.value(value: orderBloc),
      ],
      child: BlocListener<ReorderBloc, ReorderState>(
        bloc: reorderBloc,
        listener: (context, state) {
          if (state.status == ReorderStatus.success) {
            showSuccessSnackBar(
              context: context,
              message: state.successMessage,
            );
            context
                .read<OrderBloc>()
                .add(const OrderReload(query: OrderQuery()));
            Navigator.of(context, rootNavigator: true).pop();
            Navigator.of(context).push(MaterialPageRoute(
              builder: (context) =>
                  const HomeScreen(initialPage: 3), // MyOrdersPage
            ));
          }

          if (state.status == ReorderStatus.failure && state.hasApiError) {
            Navigator.of(context, rootNavigator: true).pop();
            showErrorSnackBar(
              context: context,
              message: state.errorMessage,
            );
          }
        },
        child: AlertDialog(
          elevation: 0, // Remove shadow
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24), // Increased radius
          ),
          title: const Text('Reorder Confirmation'),
          content: const Text('Are you sure you want to reorder this item?'),
          actions: [
            BlocBuilder<ReorderBloc, ReorderState>(
              builder: (context, state) {
                return TextButton(
                  style: TextButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.circular(16), // Increased radius
                    ),
                  ),
                  onPressed: state.status == ReorderStatus.loading
                      ? null
                      : () {
                          Navigator.of(context, rootNavigator: true).pop();
                        },
                  child: Text(context.loc.cancel),
                );
              },
            ),
            BlocBuilder<ReorderBloc, ReorderState>(
              builder: (context, state) {
                return ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                    elevation: 0, // Remove shadow
                    shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.circular(16), // Increased radius
                    ),
                  ),
                  onPressed: state.status == ReorderStatus.loading
                      ? null
                      : () {
                          context
                              .read<ReorderBloc>()
                              .add(ReorderSubmitted(orderId: orderId));
                        },
                  child: state.status == ReorderStatus.loading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text('Reorder'),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  _accepOrRejectDialog({
    required String reason,
    required BuildContext context,
    required int orderId,
    required OrderApprovalBloc approvalBloc,
    required OrderBloc orderBloc,
    bool canReply = false,
  }) {
    TextEditingController reasonTextController = TextEditingController();
    final formKey = GlobalKey<FormState>();
    // Use formKey instead of _formKey

    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: approvalBloc),
        BlocProvider.value(value: orderBloc),
      ],
      child: BlocListener<OrderApprovalBloc, OrderApprovalState>(
        bloc: approvalBloc,
        listener: (context, state) {
          if (state.status == OrderApprovalStatus.success) {
            showSuccessSnackBar(
                context: context, message: state.successMessage);
            context
                .read<OrderBloc>()
                .add(OrderReload(query: context.read<OrderBloc>().state.query));
            Navigator.of(context, rootNavigator: true).pop();
          }

          if (state.status == OrderApprovalStatus.failure &&
              state.hasApiError) {
            Navigator.of(context, rootNavigator: true).pop();
            showErrorSnackBar(context: context, message: state.errorMessage);
          }
        },
        child: AlertDialog(
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          actionsPadding: const EdgeInsets.only(right: 16, bottom: 16),
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Align(
                alignment: Alignment.centerLeft,
                child: Text(context.loc.checkOrder),
              ),
              Align(
                alignment: Alignment.centerRight,
                child: IconButton(
                  icon: const Icon(Icons.close),
                  padding: const EdgeInsets.all(0),
                  visualDensity:
                      const VisualDensity(vertical: -4, horizontal: -4),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ),
            ],
          ),
          content: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(reason),
                const SizedBox(height: 24),
                if (canReply)
                  TextFormField(
                    maxLength: 500,
                    maxLines: 3,
                    controller: reasonTextController,
                    validator: (value) {
                      if (canReply && (value == null || value.isEmpty)) {
                        return 'Please enter your comment';
                      }
                      return null;
                    },
                    decoration: InputDecoration(
                      labelText: "Comment",
                      isDense: true,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          actions: [
            BlocBuilder<OrderApprovalBloc, OrderApprovalState>(
              builder: (context, state) {
                return MaterialButton(
                  elevation: 0,
                  padding: EdgeInsets.zero,
                  visualDensity: const VisualDensity(
                      vertical: VisualDensity.minimumDensity),
                  color: Theme.of(context).primaryColor,
                  onPressed: () {
                    if (formKey.currentState!.validate()) {
                      context.read<OrderApprovalBloc>().add(AcceptOrder(
                            orderId: orderId,
                            reason: reasonTextController.text,
                          ));
                    }
                  },
                  child: state.status == OrderApprovalStatus.loading
                      ? const CircularProgressIndicator.adaptive()
                      : Text(
                          AppLocalizations.of(context)!.accept,
                          style: const TextStyle(color: Colors.white),
                        ),
                );
              },
            ),
            BlocBuilder<OrderApprovalBloc, OrderApprovalState>(
              builder: (context, state) {
                return MaterialButton(
                  elevation: 0,
                  visualDensity: const VisualDensity(
                      vertical: VisualDensity.minimumDensity),
                  onPressed: state.status == OrderApprovalStatus.loading
                      ? null
                      : () {
                          if (formKey.currentState!.validate()) {
                            context.read<OrderApprovalBloc>().add(RejectOrder(
                                  orderId: orderId,
                                  reason: reasonTextController.text,
                                ));
                          }
                        },
                  child: Text(
                    AppLocalizations.of(context)!.reject,
                  ),
                );
              },
            )
          ],
        ),
      ),
    );
  }

  _buildViewProductButton(BuildContext context, int productId) {
    return _ViewProductButton(
      productId: productId,
      onPressed: () => _showProductDetailBottomSheet(context, productId),
    );
  }

  Future<void> _showProductDetailBottomSheet(
      BuildContext context, int productId) async {
    try {
      final productsRepository = getIt.get<ProductsRepository>();
      final product =
          await productsRepository.getProductDetail(productId: productId);

      // Check if the widget is still mounted before using context
      if (!context.mounted) return;

      // Show the actual product detail bottom sheet
      ProductDetailPage.showAsBottomSheet(
        context: context,
        product: product,
        heroTag: 'order_product_$productId',
      );
    } catch (e) {
      // Check if the widget is still mounted before using context
      if (!context.mounted) return;

      // Close the loading bottom sheet
      Navigator.of(context).pop();

      // Show error snackbar
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Failed to load product details'),
          backgroundColor: Colors.red,
          action: SnackBarAction(
            label: context.loc.retry,
            textColor: Colors.white,
            onPressed: () => _showProductDetailBottomSheet(context, productId),
          ),
        ),
      );
    }
  }
}

class _ViewProductButton extends StatefulWidget {
  final int productId;
  final Future<void> Function() onPressed;

  const _ViewProductButton({
    required this.productId,
    required this.onPressed,
  });

  @override
  State<_ViewProductButton> createState() => _ViewProductButtonState();
}

class _ViewProductButtonState extends State<_ViewProductButton> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: _isLoading ? null : _handlePress,
      child: _isLoading
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor:
                    AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
              ),
            )
          : Text(
              context.loc.viewProduct,
              style: const TextStyle(
                  color: AppColors.primaryColor,
                  fontSize: 13,
                  letterSpacing: 0.1),
            ),
    );
  }

  Future<void> _handlePress() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await widget.onPressed();
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
