import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/auth/bloc/otp_bloc.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/auth/login_screen.dart';
import 'package:goldenprizma/presentation/views/auth/update_password_screen.dart';
import 'package:goldenprizma/presentation/views/forms/auth/phone_number_input.dart';
import 'package:goldenprizma/presentation/views/screens/verification.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';

class OTPScreen extends StatefulWidget {
  const OTPScreen({super.key, required this.isForgetPassword});
  static const String routeName = '/otp';
  final bool isForgetPassword;

  @override
  _OTPScreenState createState() => _OTPScreenState();
}

class _OTPScreenState extends State<OTPScreen> {
  final PhoneNumber _phoneNumber = PhoneNumber(isoCode: 'IQ');
  final TextEditingController _phoneController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        leading: Navigator.of(context).canPop()
            ? Padding(
                padding: const EdgeInsets.only(left: 16, top: 8),
                child: Material(
                  elevation: 2,
                  shadowColor: Colors.black.withOpacity(0.1),
                  shape: const CircleBorder(),
                  color: Colors.white.withOpacity(0.8),
                  child: InkWell(
                    customBorder: const CircleBorder(),
                    onTap: () {
                      if (Navigator.of(context).canPop()) {
                        Navigator.of(context).pop();
                      } else {
                        Navigator.of(context)
                            .pushReplacementNamed(LoginScreen.routeName);
                      }
                    },
                    child: const Padding(
                      padding: EdgeInsets.all(8.0),
                      child: Icon(
                        Icons.arrow_back_ios_new,
                        size: 16,
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ),
                ),
              )
            : null,
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.white,
        child: Stack(
          children: [
            // Top gradient background like login page
            Positioned(
              top: -MediaQuery.of(context).size.height * 0.1,
              child: Container(
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height * 0.4,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppColors.primaryColor,
                      AppColors.primaryColor.withOpacity(0.6),
                      Colors.white.withOpacity(0.2),
                    ],
                  ),
                ),
              ),
            ),
            // Background decorative elements
            Positioned(
              top: -50,
              right: -50,
              child: Container(
                width: 150,
                height: 150,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      Colors.white.withOpacity(0.3),
                      Colors.white.withOpacity(0.1),
                    ],
                  ),
                ),
              ),
            ),
            Positioned(
              bottom: -80,
              left: -80,
              child: Container(
                width: 200,
                height: 200,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      AppColors.primaryColor.withOpacity(0.1),
                      AppColors.primaryColor.withOpacity(0.05),
                    ],
                  ),
                ),
              ),
            ),
            // Main content
            SafeArea(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: SizedBox(
                  height: MediaQuery.of(context).size.height -
                      MediaQuery.of(context).padding.top -
                      MediaQuery.of(context).padding.bottom -
                      40,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 28.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Flexible(
                          flex: 2,
                          child: Column(
                            children: [
                              const SizedBox(height: 28),
                              // Animated Logo Container
                              TweenAnimationBuilder(
                                duration: const Duration(milliseconds: 800),
                                tween: Tween<double>(begin: 0.0, end: 1.0),
                                builder: (context, double value, child) {
                                  return Transform.scale(
                                    scale: value,
                                    child: SizedBox(
                                      width: MediaQuery.of(context).size.width *
                                          0.4,
                                      child: Image.asset(
                                          'assets/images/text_logo.png'),
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Title with animation
                        TweenAnimationBuilder(
                          duration: const Duration(milliseconds: 1000),
                          tween: Tween<double>(begin: 0.0, end: 1.0),
                          builder: (context, double value, child) {
                            return Opacity(
                              opacity: value,
                              child: Text(
                                widget.isForgetPassword
                                    ? context.loc.forgetPassword
                                    : context.loc.register,
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primaryColor,
                                  letterSpacing: 0.5,
                                ),
                              ),
                            );
                          },
                        ),

                        const SizedBox(height: 12),

                        // WhatsApp Icon with animation
                        TweenAnimationBuilder(
                          duration: const Duration(milliseconds: 1200),
                          tween: Tween<double>(begin: 0.0, end: 1.0),
                          builder: (context, double value, child) {
                            return Opacity(
                              opacity: value,
                              child: Container(
                                padding: const EdgeInsets.all(12),
                                decoration: const BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.white,
                                ),
                                child: const SizedBox(
                                  width: 32,
                                  height: 32,
                                  child: Image(
                                    image: AssetImage(
                                        'assets/images/png/whatsapp.png'),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),

                        // Description Text
                        TweenAnimationBuilder(
                          duration: const Duration(milliseconds: 1400),
                          tween: Tween<double>(begin: 0.0, end: 1.0),
                          builder: (context, double value, child) {
                            return Opacity(
                              opacity: value,
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 16),
                                child: RichText(
                                  textAlign: TextAlign.center,
                                  text: TextSpan(children: [
                                    TextSpan(
                                      text: widget.isForgetPassword
                                          ? context.loc
                                              .enterYourPhoneNumberToReceiveVerificationCode
                                          : context.loc
                                              .enterYourPhoneNumberToReceiveVerificationCodeIdentity,
                                      style: TextStyle(
                                        fontSize: 14.5,
                                        color: AppColors.textBodyColor,
                                        height: 1.5,
                                        letterSpacing: 0.3,
                                      ),
                                    ),
                                  ]),
                                ),
                              ),
                            );
                          },
                        ),

                        Flexible(
                          flex: 4,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // Phone Field
                              TweenAnimationBuilder(
                                duration: const Duration(milliseconds: 1600),
                                tween: Tween<double>(begin: 0.0, end: 1.0),
                                builder: (context, double value, child) {
                                  return Opacity(
                                    opacity: value,
                                    child: Transform.translate(
                                      offset: Offset(0, 20 * (1 - value)),
                                      child: Material(
                                        color: Colors.transparent,
                                        borderRadius: BorderRadius.circular(14),
                                        elevation: 0,
                                        child: _phoneField(),
                                      ),
                                    ),
                                  );
                                },
                              ),

                              const SizedBox(height: 24),

                              // Get OTP Button
                              TweenAnimationBuilder(
                                duration: const Duration(milliseconds: 1800),
                                tween: Tween<double>(begin: 0.0, end: 1.0),
                                builder: (context, double value, child) {
                                  return Opacity(
                                    opacity: value,
                                    child: Transform.translate(
                                      offset: Offset(0, 20 * (1 - value)),
                                      child: _getOtpButton(),
                                    ),
                                  );
                                },
                              ),

                              const SizedBox(height: 16),

                              // Back Button
                              TweenAnimationBuilder(
                                duration: const Duration(milliseconds: 2000),
                                tween: Tween<double>(begin: 0.0, end: 1.0),
                                builder: (context, double value, child) {
                                  return Opacity(
                                    opacity: value,
                                    child: Container(
                                      width: double.infinity,
                                      height: 54,
                                      decoration: BoxDecoration(
                                        gradient: const LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors: [
                                            Colors.white,
                                            Color(0xFFF8F8F8),
                                          ],
                                        ),
                                        borderRadius: BorderRadius.circular(14),
                                        border: Border.all(
                                          color: const Color(0xFFE0E0E0),
                                          width: 1.0,
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color:
                                                Colors.black.withOpacity(0.03),
                                            blurRadius: 8,
                                            spreadRadius: 0,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: Material(
                                        color: Colors.transparent,
                                        child: InkWell(
                                          borderRadius:
                                              BorderRadius.circular(14),
                                          splashColor: AppColors.primaryColor
                                              .withOpacity(0.05),
                                          highlightColor: AppColors.primaryColor
                                              .withOpacity(0.05),
                                          onTap: () {
                                            if (Navigator.of(context)
                                                .canPop()) {
                                              Navigator.of(context).pop();
                                            } else {
                                              Navigator.of(context)
                                                  .pushReplacementNamed(
                                                      LoginScreen.routeName);
                                            }
                                          },
                                          child: Center(
                                            child: Text(
                                              context.loc.back,
                                              style: TextStyle(
                                                color: AppColors.textBodyColor,
                                                fontWeight: FontWeight.w600,
                                                fontSize: 15,
                                                letterSpacing: 0.3,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 24),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _getOtpButton() {
    return BlocConsumer<OtpBloc, OtpState>(
      listener: (context, state) {
        if (state.status == OtpStatus.request) {
          showLoadingAlert(context: context);
        } else if (state.status == OtpStatus.success) {
          if (widget.isForgetPassword) {
            Navigator.of(context).pushReplacement(Verification.pageRoute(
                otpBloc: context.read<OtpBloc>(),
                phoneNumber: _phoneController.text,
                countryCode: _phoneNumber.isoCode ?? "IQ",
                isForgetPassword: widget.isForgetPassword,
                dialCode: _phoneNumber.dialCode ?? "+964"));
          } else {
            Navigator.of(context).pushReplacement(Verification.pageRoute(
                otpBloc: context.read<OtpBloc>(),
                phoneNumber: _phoneController.text,
                isForgetPassword: widget.isForgetPassword,
                countryCode: _phoneNumber.isoCode ?? "IQ",
                dialCode: _phoneNumber.dialCode ?? "+964"));
          }
        } else if (state.status == OtpStatus.failure) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Icon(
                      Icons.error_outline,
                      color: Colors.white,
                      size: 18,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      state.errorMessage,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 14.5,
                        letterSpacing: 0.2,
                      ),
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.red.shade700,
              behavior: SnackBarBehavior.floating,
              elevation: 6,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              margin: const EdgeInsets.fromLTRB(20, 0, 20, 20),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
              duration: const Duration(seconds: 4),
              action: SnackBarAction(
                label: 'OK',
                textColor: Colors.white,
                onPressed: () {
                  ScaffoldMessenger.of(context).hideCurrentSnackBar();
                },
              ),
            ),
          );
        }
      },
      builder: (context, state) {
        final bool isLoading = state.status == OtpStatus.request;

        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          width: double.infinity,
          height: 56,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(14),
            boxShadow: isValidated && error == null
                ? [
                    BoxShadow(
                      color: AppColors.primaryColor.withOpacity(0.25),
                      blurRadius: 12,
                      spreadRadius: 0,
                      offset: const Offset(0, 4),
                    ),
                  ]
                : [],
            gradient: isValidated && error == null
                ? const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.primaryColor,
                      AppColors.primaryBrightColor,
                    ],
                  )
                : null,
          ),
          child: Material(
            color: isValidated && error == null
                ? Colors.transparent
                : Colors.grey.shade200,
            borderRadius: BorderRadius.circular(14),
            child: InkWell(
              borderRadius: BorderRadius.circular(14),
              splashColor: isValidated && error == null
                  ? Colors.white.withOpacity(0.1)
                  : Colors.transparent,
              highlightColor: isValidated && error == null
                  ? Colors.white.withOpacity(0.05)
                  : Colors.transparent,
              onTap: (isValidated && error == null && !isLoading)
                  ? () {
                      HapticFeedback.mediumImpact(); // Add haptic feedback
                      context.read<OtpBloc>().add(OTPRequested(
                          countryCode: _phoneNumber.isoCode ?? "IQ",
                          phoneNumber: _phoneController.text,
                          scope: widget.isForgetPassword
                              ? 'forget_password'
                              : 'register',
                          dialCode: _phoneNumber.dialCode ?? "+964"));
                    }
                  : null,
              child: Center(
                child: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 200),
                  child: isLoading
                      ? const SizedBox(
                          key: ValueKey('loading'),
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 2.5,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(
                          context.loc.getOTP,
                          key: const ValueKey('text'),
                          style: TextStyle(
                            color: isValidated && error == null
                                ? Colors.white
                                : Colors.grey.shade600,
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                            letterSpacing: 0.5,
                          ),
                        ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  String? error;
  bool isValidated = false;
  Widget _phoneField() {
    return Form(
      key: _formKey,
      child: Directionality(
        textDirection: TextDirection.ltr,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(14),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                Colors.white.withOpacity(0.95),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.primaryColor.withOpacity(0.05),
                blurRadius: 15,
                spreadRadius: 0,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(14),
            child: InternationalPhoneNumberInput(
              onInputChanged: (PhoneNumber number) async {
                final RegExp code = RegExp('[+]${number.dialCode}');
                error = PhoneNumberInput.dirty(
                        number.phoneNumber.toString().replaceAll(code, ''))
                    .validationErrorMessage(context);
                isValidated = error == null;
                setState(() {});
              },
              selectorConfig: const SelectorConfig(
                selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                setSelectorButtonAsPrefixIcon: true,
                leadingPadding: 20.0,
              ),
              ignoreBlank: true,
              autoValidateMode: AutovalidateMode.onUserInteraction,
              initialValue: _phoneNumber,
              textFieldController: _phoneController,
              formatInput: false,
              keyboardType: TextInputType.phone,
              inputBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(14),
                borderSide: BorderSide.none,
              ),
              maxLength: 10,
              keyboardAction: TextInputAction.done,
              autoFocus: true,
              cursorColor: AppColors.primaryColor,
              inputDecoration: InputDecoration(
                errorText: error,
                errorStyle: const TextStyle(
                  color: Colors.redAccent,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
                contentPadding:
                    const EdgeInsets.symmetric(vertical: 18, horizontal: 16),
                hintText: '7xx-xxx-xxxx',
                hintStyle: TextStyle(
                  color: Colors.grey.shade400,
                  fontSize: 15.5,
                  letterSpacing: 0.5,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(14),
                  borderSide: const BorderSide(
                    color: AppColors.primaryColor,
                    width: 1.5,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(14),
                  borderSide: BorderSide.none,
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(14),
                  borderSide: const BorderSide(
                    color: Colors.redAccent,
                    width: 1.0,
                  ),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(14),
                  borderSide: const BorderSide(
                    color: Colors.redAccent,
                    width: 1.5,
                  ),
                ),
                filled: true,
                fillColor: Colors.transparent,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
