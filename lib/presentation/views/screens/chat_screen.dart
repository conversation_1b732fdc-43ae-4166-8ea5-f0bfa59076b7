import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/chat/bloc/message_bloc.dart';
import 'package:goldenprizma/domain/chat/models/message.dart';
import 'package:goldenprizma/domain/chat/models/sender.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';

class ChatScreen extends StatefulWidget {
  final int? conversationId;
  final Sender? agent;

  static pageRoute({int? conversationId, Sender? agent, String? subject}) {
    return MaterialPageRoute<void>(
      builder: (BuildContext context) =>
          ChatScreen(conversationId: conversationId, agent: agent),
    );
  }

  const ChatScreen({
    super.key,
    this.conversationId,
    this.agent,
  });

  @override
  _ChatScreenState createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    if (mounted && widget.conversationId != null) {
      context
          .read<MessageBloc>()
          .add(GetMessages(conversationId: widget.conversationId!));
    }

    // Scroll to the bottom after the widget is built
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   _scrollController.jumpTo(
    //     _scrollController.position.maxScrollExtent,
    //   );
    // });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: widget.agent != null
            ? Text(widget.agent!.name)
            : const Text('New Conversation'),
        centerTitle: true,
      ),
      body: Column(
        children: [
          BlocBuilder<MessageBloc, MessageState>(
            builder: (context, state) {
              if (state.status == MessageStatus.loading) {
                return const Expanded(
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                );
              }

              if ((state.status == MessageStatus.initial) ||
                  state.status == MessageStatus.success &&
                      state.messages.isEmpty) {
                return const Expanded(
                  child: Center(
                    child: Text('No messages'),
                  ),
                );
              }

              if (state.status == MessageStatus.failure) {
                return const Expanded(
                  child: Center(
                    child: Text('Failed to load messages'),
                  ),
                );
              }

              return Expanded(
                child: ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  itemCount: state.messages.length,
                  scrollDirection: Axis.vertical,
                  itemBuilder: (context, index) {
                    final Message message = state.messages[index];

                    return Align(
                      alignment: message.messageType == MessageType.outgoing
                          ? Alignment.centerRight
                          : Alignment.centerLeft,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8.0, horizontal: 16.0),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (message.messageType != MessageType.outgoing)
                              const CircleAvatar(
                                radius: 18,
                                child: Icon(Icons.person, size: 18),
                              ),
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 10.0, horizontal: 16.0),
                              decoration: BoxDecoration(
                                color:
                                    message.messageType == MessageType.outgoing
                                        ? AppColors.primaryColor
                                        : Colors.grey[200],
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    "${message.content}",
                                    style: TextStyle(
                                      color: message.messageType ==
                                              MessageType.outgoing
                                          ? Colors.white
                                          : Colors.black87,
                                      fontSize: 16,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Row(
                                    children: [
                                      Text(
                                        message.createdAt.toString(),
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: message.messageType ==
                                                  MessageType.outgoing
                                              ? Colors.white
                                              : Colors.black54,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              );
            },
          ),
          MessageInput(
            scrollController: _scrollController,
          ),
        ],
      ),
    );
  }
}

class MessageInput extends StatelessWidget {
  MessageInput({super.key, required this.scrollController});

  final ScrollController scrollController;
  final TextEditingController _textController = TextEditingController();

  void _scrollToBottom() {
    if (scrollController.hasClients) {
      scrollController.animateTo(
        scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            IconButton(
              icon: const Icon(HugeIcons.strokeRoundedAttachment),
              onPressed: () {},
              color: AppColors.primaryBrightColor,
              splashRadius: 24,
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Theme(
                data: Theme.of(context).copyWith(
                  inputDecorationTheme: InputDecorationTheme(
                    filled: true,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(48),
                      borderSide: BorderSide.none,
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(48),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(48),
                      borderSide: const BorderSide(
                        color: AppColors.primaryBrightColor,
                        width: 1.0,
                      ),
                    ),
                  ),
                ),
                child: TextFormField(
                  controller: _textController,
                  autocorrect: false,
                  maxLines: 3,
                  minLines: 1,
                  decoration: const InputDecoration(
                    hintText: 'Type a message',
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 10),
            sendButton(),
          ],
        ),
      ),
    );
  }

  Widget sendButton() {
    return Container(
      decoration: const BoxDecoration(
        color: AppColors.primaryColor,
        shape: BoxShape.circle,
      ),
      child: IconButton(
        visualDensity: const VisualDensity(horizontal: 0, vertical: 0),
        icon: const Icon(HugeIcons.strokeRoundedSent),
        onPressed: () {
          _scrollToBottom();
          _textController.clear();
        },
        color: Colors.white,
        splashRadius: 24,
      ),
    );
  }
}
