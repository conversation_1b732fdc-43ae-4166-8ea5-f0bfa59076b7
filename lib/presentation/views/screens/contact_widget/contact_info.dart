import 'dart:io';

import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';
import 'package:url_launcher/url_launcher.dart';

class ContactInfo extends StatelessWidget {
  final String phone;

  const ContactInfo({
    super.key,
    required this.phone,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 16),
      width: double.infinity,
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(15),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text(
            'Our service center is ready to help you for 24/7',
            style: TextStyle(fontWeight: FontWeight.w600),
            textAlign: TextAlign.center,
            softWrap: true,
          ),
          const SizedBox(height: 16),
          Text(
            phone,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              OutlinedButton(
                onPressed: () async {
                  await launchUrl(Uri.parse('tel:$phone'),
                      mode: LaunchMode.externalNonBrowserApplication);
                },
                child: const Icon(
                  Ionicons.call,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(width: 4),
              OutlinedButton(
                onPressed: () async {
                  var whatsappAndroidUrl =
                      "whatsapp://send?phone=$phone&text=hello";
                  var whatappiOSUrl =
                      "https://wa.me/$phone?text=${Uri.parse("Hello")}";
                  if (Platform.isIOS) {
                    await launchUrl(Uri.parse(whatappiOSUrl),
                        mode: LaunchMode.externalNonBrowserApplication);
                  } else {
                    await launchUrl(Uri.parse(whatsappAndroidUrl),
                        mode: LaunchMode.externalNonBrowserApplication);
                  }
                },
                child: const Icon(
                  Ionicons.logo_whatsapp,
                  color: Colors.green,
                ),
              ),
              const SizedBox(width: 4),
              OutlinedButton(
                onPressed: () async {
                  var viberUrl = "viber://chat?number=$phone";
                  await launchUrl(
                    Uri.parse(viberUrl),
                    mode: LaunchMode.externalNonBrowserApplication,
                  );
                },
                child: Image.asset(
                  'assets/images/png/logo_viber.png',
                  width: 24,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
