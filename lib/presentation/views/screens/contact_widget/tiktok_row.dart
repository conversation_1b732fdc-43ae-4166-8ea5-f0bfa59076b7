import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';
import 'package:url_launcher/url_launcher.dart';

class TikTokRow extends StatelessWidget {
  const TikTokRow({
    super.key,
    required this.tiktok,
  });

  final String? tiktok;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      textDirection: ui.TextDirection.ltr,
      children: [
        const Icon(
          Ionicons.logo_tiktok,
          color: Colors.black,
        ),
        const SizedBox(width: 2),
        TextButton(
          style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).textTheme.bodyLarge?.color),
          onPressed: () async {
            await launchUrl(Uri.parse("https://www.tiktok.com/$tiktok"),
                mode: LaunchMode.externalNonBrowserApplication);
          },
          child: Text("$tiktok"),
        ),
      ],
    );
  }
}
