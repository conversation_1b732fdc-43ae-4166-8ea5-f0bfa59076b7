import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';

class SnapchatRow extends StatelessWidget {
  const SnapchatRow({
    super.key,
    required this.snapchat,
  });

  final String? snapchat;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      textDirection: ui.TextDirection.ltr,
      children: [
        const Icon(
          Ionicons.logo_snapchat,
          color: Colors.yellow,
        ),
        const SizedBox(width: 2),
        TextButton(
          style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).textTheme.bodyLarge?.color),
          onPressed: () {},
          child: Text("$snapchat"),
        ),
      ],
    );
  }
}
