import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/screens/contact_widget/snapchat_row.dart';
import 'package:goldenprizma/presentation/views/screens/contact_widget/tiktok_row.dart';
import 'package:ionicons/ionicons.dart';
import 'package:url_launcher/url_launcher.dart';

class SocialInfo extends StatelessWidget {
  final String facebook;
  final String instagram;
  final String? snapchat;
  final String? tiktok;

  const SocialInfo({
    super.key,
    required this.facebook,
    required this.instagram,
    this.snapchat,
    this.tiktok,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: SingleChildScrollView(
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 16),
          width: double.infinity,
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(15),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                'You can also find us on Social Media & Website',
                style: TextStyle(fontWeight: FontWeight.w600),
                textAlign: TextAlign.center,
                softWrap: true,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                textDirection: ui.TextDirection.ltr,
                children: [
                  Icon(
                    Ionicons.logo_facebook,
                    color: Colors.blue.shade700,
                  ),
                  const SizedBox(width: 2),
                  TextButton(
                    style: TextButton.styleFrom(
                      foregroundColor:
                          Theme.of(context).textTheme.bodyLarge?.color,
                    ),
                    onPressed: () async {
                      await launchUrl(
                        Uri.parse('https://facebook.com/$facebook'),
                        mode: LaunchMode.externalNonBrowserApplication,
                      );
                    },
                    child: Text(facebook),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                textDirection: ui.TextDirection.ltr,
                children: [
                  Icon(
                    Ionicons.logo_instagram,
                    color: Colors.purple.shade400,
                  ),
                  const SizedBox(width: 2),
                  TextButton(
                    style: TextButton.styleFrom(
                        foregroundColor:
                            Theme.of(context).textTheme.bodyLarge?.color),
                    onPressed: () async {
                      await launchUrl(
                          Uri.parse('https://instagram.com/$instagram'),
                          mode: LaunchMode.externalNonBrowserApplication);
                    },
                    child: Text(instagram),
                  ),
                ],
              ),
              if (snapchat != null && snapchat!.isNotEmpty)
                SnapchatRow(snapchat: snapchat),
              if (tiktok != null && tiktok!.isNotEmpty)
                TikTokRow(tiktok: tiktok),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                textDirection: ui.TextDirection.ltr,
                children: [
                  const Icon(
                    Ionicons.globe,
                    color: AppColors.primaryColor,
                  ),
                  const SizedBox(width: 2),
                  TextButton(
                    style: TextButton.styleFrom(
                      foregroundColor:
                          Theme.of(context).textTheme.bodyLarge?.color,
                    ),
                    onPressed: () async {
                      await launchUrl(Uri.parse('https://goldenprizma.com'));
                    },
                    child: const Text('https://goldenprizma.com'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
