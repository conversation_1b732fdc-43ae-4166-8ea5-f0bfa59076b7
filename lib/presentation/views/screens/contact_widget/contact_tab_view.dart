import 'package:flutter/material.dart';
import 'package:goldenprizma/domain/support/models/contact.dart';
import 'package:goldenprizma/main.dart' show getIt;
import 'package:goldenprizma/presentation/views/screens/contact_widget/contact_info.dart';
import 'package:goldenprizma/presentation/views/screens/contact_widget/social_info.dart';
import 'package:goldenprizma/repositories/contact_repository.dart';

class ContactTabView extends StatelessWidget {
  final ContactRepository contactRepository = getIt.get<ContactRepository>();
  final int branchId;
  ContactTabView({
    super.key,
    required this.branchId,
  });

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: contactRepository.getContactDetails(branchId: branchId),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return _buildFailedUI(context);
        }
        if (snapshot.hasData) {
          Contact? contact = snapshot.data as Contact;
          return Column(
            children: [
              if (contact.phone != null && contact.phone!.isNotEmpty)
                ContactInfo(phone: "${contact.phone}"),
              const SizedBox(height: 32),
              SocialInfo(
                facebook: "${contact.facebookLink}",
                instagram: "${contact.instagramLink}",
                snapchat: contact.snapchat,
                tiktok: contact.tiktok,
              ),
            ],
          );
        }

        return const Center(
          child: CircularProgressIndicator.adaptive(),
        );
      },
    );
  }

  Center _buildFailedUI(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'An Error occured please try again',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
        ],
      ),
    );
  }
}
