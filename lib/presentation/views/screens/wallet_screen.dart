import 'dart:async';
import 'dart:math';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/accounting/bloc/account_bloc.dart';
import 'package:goldenprizma/domain/accounting/bloc/deposit_bloc.dart';
import 'package:goldenprizma/domain/accounting/bloc/pending_deposit_bloc.dart';
import 'package:goldenprizma/domain/accounting/bloc/transaction_bloc.dart';
import 'package:goldenprizma/domain/accounting/models/account.dart';
import 'package:goldenprizma/domain/accounting/models/transaction.dart';
import 'package:goldenprizma/domain/exchange_rates/bloc/exchange_rate_bloc.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/widgets/pending_deposits_widget.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:ionicons/ionicons.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import 'package:url_launcher/url_launcher.dart';

class WalletScreen extends StatefulWidget {
  const WalletScreen({super.key});
  static const routeName = '/wallet';

  @override
  State<WalletScreen> createState() => _WalletScreenState();
}

class _WalletScreenState extends State<WalletScreen> {
  StreamSubscription<RemoteMessage>? _firebaseSubscription;

  @override
  void initState() {
    super.initState();
    // Load initial data
    context.read<TransactionBloc>().add(TransactionRequestLoad());
    context.read<AccountBloc>().add(AccountRequestLoad());
    context.read<PendingDepositBloc>().add(PendingDepositRequestLoad());

    // Setup Firebase message listener for deposit notifications
    _setupFirebaseListener();
  }

  @override
  void dispose() {
    // Cancel Firebase subscription to prevent memory leaks
    _firebaseSubscription?.cancel();
    super.dispose();
  }

  void _setupFirebaseListener() {
    // Listen for messages when app is in foreground
    _firebaseSubscription =
        FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      _handleDepositMessage(message);
    });
  }

  void _handleDepositMessage(RemoteMessage message) {
    // Check if widget is still mounted before accessing context
    if (!mounted) return;

    final messageType = message.data['type'];

    // Handle deposit related messages
    if (messageType == 'deposit_received' ||
        messageType == 'deposit_declined') {
      // Refresh wallet balance
      context.read<AccountBloc>().add(AccountRequestLoad());

      // Refresh pending deposits
      context.read<PendingDepositBloc>().add(PendingDepositRequestLoad());

      // Refresh transaction history
      context.read<TransactionBloc>().add(const TransactionRequestRefresh());

      // Optional: Show a brief notification to user
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            messageType == 'deposit_received'
                ? context.loc.depositReceived
                : context.loc.depositStatusUpdated,
          ),
          backgroundColor:
              messageType == 'deposit_received' ? Colors.green : Colors.orange,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: 8),
            BlocBuilder<AccountBloc, AccountState>(
              builder: (context, state) {
                if (state.status == AccountStatus.initial) {
                  return const SizedBox(
                    height: 100,
                    child: Center(child: CircularProgressIndicator.adaptive()),
                  );
                }
                return _accountInfo(context, state.account);
              },
            ),
            const SizedBox(height: 16),
            const PendingDepositsWidget(),
            const SizedBox(height: 16),
            _buildRecentTransactionsSection(context),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);
    final double height = max(MediaQuery.of(context).size.height * 0.368, 351);
    return Stack(
      children: [
        Container(
          padding: const EdgeInsets.only(top: 100, bottom: 32),
          width: double.infinity,
          height: height,
          decoration: BoxDecoration(
            gradient: isDarkMode
                ? LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppColors.boxDarkColor,
                      AppColors.boxDarkColor.withOpacity(0.8),
                    ],
                  )
                : LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppColors.primaryColor.withOpacity(0.05),
                      Colors.grey.shade50,
                    ],
                  ),
          ),
          child: BlocBuilder<AccountBloc, AccountState>(
            builder: (context, state) {
              if (state.status == AccountStatus.initial) {
                return const SizedBox(
                  height: 100,
                  child: Center(child: CircularProgressIndicator.adaptive()),
                );
              }

              if (state.status == AccountStatus.failure) {
                return Center(child: Text(context.loc.anErrorOccurred));
              }
              return _buildBalanceCard(context, state.account, isDarkMode);
            },
          ),
        ),
        PositionedDirectional(
          top: 50,
          start: 16,
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                Navigator.of(context).pop();
              },
              borderRadius: BorderRadius.circular(12),
              child: Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: const Icon(
                  Icons.arrow_back,
                  size: 22,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _accountInfo(BuildContext context, Account account) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDarkMode ? AppColors.boxDarkColor : Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade200,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: _accountStatItem(
                context,
                icon: HugeIcons.strokeRoundedShoppingCart01,
                label: context.loc.totalPruchase,
                value: account.totalPurchase,
                color: AppColors.primaryColor,
                isDarkMode: isDarkMode,
              ),
            ),
            Container(
              width: 1,
              height: 40,
              color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade200,
            ),
            Expanded(
              child: _accountStatItem(
                context,
                icon: HugeIcons.strokeRoundedDeliveryTruck01,
                label: context.loc.totalDelivered,
                value: account.totalDelivered,
                color: AppColors.primaryBrightColor,
                isDarkMode: isDarkMode,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _accountStatItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    required Color color,
    required bool isDarkMode,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: isDarkMode ? Colors.white60 : Colors.grey[600],
                  fontSize: 11,
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.grey[900],
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceCard(
      BuildContext context, Account account, bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Material(
        elevation: 1,
        borderRadius: BorderRadius.circular(20),
        color: isDarkMode ? AppColors.boxDarkColor : Colors.white,
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: isDarkMode
                ? LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.boxDarkColor,
                      Colors.grey.shade900,
                    ],
                  )
                : LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      AppColors.primaryColor.withOpacity(0.02),
                    ],
                  ),
            border: Border.all(
              color: isDarkMode
                  ? Colors.grey.shade700
                  : AppColors.primaryColor.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      HugeIcons.strokeRoundedWallet01,
                      color: AppColors.primaryColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    context.loc.balance,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: isDarkMode ? Colors.white70 : Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    account.balance,
                    style: TextStyle(
                      fontSize: 40,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.white : Colors.grey[900],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _showTopUpBottomSheet(context),
                      icon: const Icon(HugeIcons.strokeRoundedWalletAdd02,
                          size: 18),
                      label: Text(context.loc.topUp),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () =>
                          Navigator.of(context).pushNamed('/transactions'),
                      icon: const Icon(Ionicons.time_outline, size: 18),
                      label: Text(context.loc.history),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.primaryColor,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        side: const BorderSide(color: AppColors.primaryColor),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showTopUpBottomSheet(BuildContext context) {
    showMaterialModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      expand: false,
      builder: (context) => MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => ExchangeRateBloc()
              ..add(const ExchangeRateRequestLoad(currency: 'IQD')),
          ),
          BlocProvider(
            create: (context) => DepositBloc(),
          ),
        ],
        child: const TopUpBottomSheet(),
      ),
    );
  }

  Widget _buildRecentTransactionsSection(BuildContext context) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    return BlocBuilder<TransactionBloc, TransactionState>(
      builder: (context, state) {
        if (state.status == TransactionStatus.initial) {
          return _recentTransactionsShimmer(context);
        }

        if (state.status == TransactionStatus.failure) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: isDarkMode ? AppColors.boxDarkColor : Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade200,
                width: 1,
              ),
            ),
            child: Column(
              children: [
                const Icon(
                  Ionicons.warning_outline,
                  color: Colors.orange,
                  size: 32,
                ),
                const SizedBox(height: 12),
                Text(
                  context.loc.failedToLoadTransactions,
                  style: TextStyle(
                    color: isDarkMode ? Colors.white70 : Colors.grey[700],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          );
        }

        final recentTransactions = state.transactions.take(5).toList();

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: isDarkMode ? AppColors.boxDarkColor : Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade200,
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 4, left: 16, right: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      context.loc.recentTransactions,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: isDarkMode ? Colors.white : Colors.grey[900],
                          ),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pushNamed('/transactions');
                      },
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            context.loc.seeAll,
                            style: const TextStyle(
                              color: AppColors.primaryColor,
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(width: 4),
                          const Icon(
                            Icons.arrow_forward_ios,
                            size: 12,
                            color: AppColors.primaryColor,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              if (recentTransactions.isEmpty) ...[
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 24),
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? Colors.grey.shade800.withOpacity(0.3)
                          : Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: AppColors.primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Ionicons.receipt_outline,
                            color: AppColors.primaryColor,
                            size: 24,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Text(
                          context.loc.noTransactionsYet,
                          style: TextStyle(
                            color:
                                isDarkMode ? Colors.white70 : Colors.grey[700],
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          context.loc.yourTransactionHistoryWillAppearHere,
                          style: TextStyle(
                            color:
                                isDarkMode ? Colors.white60 : Colors.grey[600],
                            fontSize: 12,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ] else ...[
                ...recentTransactions.asMap().entries.map((entry) {
                  final index = entry.key;
                  final transaction = entry.value;
                  return Column(
                    children: [
                      if (index > 0)
                        Divider(
                          height: 1,
                          color: isDarkMode
                              ? Colors.grey.shade700
                              : Colors.grey.shade200,
                        ),
                      _recentTransactionRow(context, transaction),
                    ],
                  );
                }),
                const SizedBox(height: 8),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _recentTransactionRow(BuildContext context, Transaction transaction) {
    final bool isCredit = transaction.type == "CRE";
    final bool isDeposit = transaction.paymentType.contains('Deposit') ||
        transaction.paymentType.contains('ZainCash') ||
        transaction.paymentType.contains('FIB');

    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);
    final Color iconColor = isCredit ? Colors.green : Colors.red;

    // Different icons for different transaction types
    IconData typeIcon;
    if (isDeposit) {
      typeIcon = isCredit
          ? HugeIcons.strokeRoundedWalletAdd02
          : HugeIcons.strokeRoundedWalletRemove02;
    } else {
      typeIcon = isCredit ? Ionicons.caret_up : Ionicons.caret_down;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(typeIcon, color: iconColor, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        transaction.paymentType,
                        style: TextStyle(
                          color: isDarkMode ? Colors.white : Colors.grey[900],
                          fontWeight: FontWeight.w600,
                          fontSize: 13,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (isDeposit) ...[
                      const SizedBox(width: 6),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 4,
                          vertical: 1,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          context.loc.deposit,
                          style: const TextStyle(
                            color: AppColors.primaryColor,
                            fontSize: 8,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 2),
                Text(
                  transaction.dateOnly,
                  style: TextStyle(
                    color: isDarkMode ? Colors.white60 : Colors.grey[600],
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${isCredit ? '+' : '-'}${transaction.amount}',
                style: TextStyle(
                  color: iconColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 2),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 1),
                decoration: BoxDecoration(
                  color: iconColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  isCredit ? context.loc.credit : context.loc.debit,
                  style: TextStyle(
                    color: iconColor,
                    fontSize: 8,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _recentTransactionsShimmer(BuildContext context) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);
    final Color cardColor = isDarkMode ? AppColors.boxDarkColor : Colors.white;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Shimmer.fromColors(
                  baseColor: isDarkMode
                      ? AppColors.boxDarkColor
                      : Colors.grey.shade300,
                  highlightColor:
                      isDarkMode ? Colors.black45 : Colors.grey.shade100,
                  child: Container(
                    width: 140,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                Shimmer.fromColors(
                  baseColor: isDarkMode
                      ? AppColors.boxDarkColor
                      : Colors.grey.shade300,
                  highlightColor:
                      isDarkMode ? Colors.black45 : Colors.grey.shade100,
                  child: Container(
                    width: 60,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
          ),
          ...List.generate(
            5,
            (index) => Column(
              children: [
                if (index > 0)
                  Divider(
                    height: 1,
                    color: isDarkMode
                        ? Colors.grey.shade700
                        : Colors.grey.shade200,
                  ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  child: Shimmer.fromColors(
                    baseColor: isDarkMode
                        ? AppColors.boxDarkColor
                        : Colors.grey.shade300,
                    highlightColor:
                        isDarkMode ? Colors.black45 : Colors.grey.shade100,
                    child: Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                width: 100,
                                height: 12,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(6),
                                ),
                              ),
                              const SizedBox(height: 4),
                              Container(
                                width: 60,
                                height: 10,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(5),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 8),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Container(
                              width: 50,
                              height: 12,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(6),
                              ),
                            ),
                            const SizedBox(height: 4),
                            Container(
                              width: 30,
                              height: 8,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }
}

class TopUpBottomSheet extends StatefulWidget {
  const TopUpBottomSheet({super.key});

  @override
  State<TopUpBottomSheet> createState() => _TopUpBottomSheetState();
}

class _TopUpBottomSheetState extends State<TopUpBottomSheet> {
  final TextEditingController _amountController = TextEditingController();
  String? _selectedPaymentMethod = 'fib';
  int? _selectedQuickAmount;

  final List<int> _quickAmounts = [50, 100, 200, 500];

  List<Map<String, String>> _getPaymentMethods(BuildContext context) => [
        {
          'image': 'assets/images/logos/fib_logo.png',
          'label': context.loc.fibBank,
          'value': 'fib',
        },
        // {
        //   'image': 'assets/images/logos/fib_logo.png',
        //   'label': 'ZainCash',
        //   'value': 'zaincash',
        // },
        // {
        //   'image': 'assets/images/logos/fib_logo.png',
        //   'label': 'Credit Card',
        //   'value': 'credit_card',
        // }
      ];

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  void _selectQuickAmount(int amount) {
    setState(() {
      _selectedQuickAmount = amount;
      _amountController.text = amount.toString();
    });
  }

  void _selectPaymentMethod(String method) {
    setState(() {
      _selectedPaymentMethod = method;
    });
  }

  bool get _canConfirm {
    return _selectedPaymentMethod != null &&
        _amountController.text.isNotEmpty &&
        double.tryParse(_amountController.text) != null &&
        double.parse(_amountController.text) > 0;
  }

  Widget _buildExchangeRateDisplay(
      BuildContext context, ExchangeRateState exchangeState) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    if (exchangeState.status == ExchangeRateStatus.loading) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDarkMode
              ? AppColors.primaryColor.withOpacity(0.1)
              : AppColors.primaryColor.withOpacity(0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.primaryColor.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor:
                    AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
              ),
            ),
            const SizedBox(width: 12),
            Text(
              context.loc.loadingExchangeRate,
              style: const TextStyle(
                color: AppColors.primaryColor,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    if (exchangeState.status == ExchangeRateStatus.failure) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDarkMode
              ? Colors.orange.withOpacity(0.1)
              : Colors.orange.withOpacity(0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.orange.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            const Icon(
              Icons.warning_amber_rounded,
              color: Colors.orange,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                context.loc.unableToLoadExchangeRate,
                style: const TextStyle(
                  color: Colors.orange,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                context.read<ExchangeRateBloc>().add(
                      const ExchangeRateRequestLoad(currency: 'IQD'),
                    );
              },
              child: Text(
                context.loc.retry,
                style: const TextStyle(
                  color: Colors.orange,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      );
    }

    if (exchangeState.status == ExchangeRateStatus.success) {
      final exchangeRate = exchangeState.exchangeRate;
      final usdAmount = double.tryParse(_amountController.text) ?? 0.0;
      final iqdAmount = usdAmount * exchangeRate.rate;

      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDarkMode
              ? AppColors.primaryColor.withOpacity(0.1)
              : AppColors.primaryColor.withOpacity(0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.primaryColor.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: const Icon(
                    Icons.currency_exchange,
                    color: AppColors.primaryColor,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 10),
                Text(
                  context.loc.exchangeRateUsdToIqd,
                  style: const TextStyle(
                    color: AppColors.primaryColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '1 USD = ${exchangeRate.formattedRate}',
                  style: TextStyle(
                    color: isDarkMode ? Colors.white70 : Colors.grey[700],
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (usdAmount > 0) ...[
                  Text(
                    '≈ ${iqdAmount.toStringAsFixed(0)} IQD',
                    style: const TextStyle(
                      color: AppColors.primaryColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ],
            ),
            if (usdAmount > 0) ...[
              const SizedBox(height: 8),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  context.loc
                      .youWillPayApproximately(iqdAmount.toStringAsFixed(0)),
                  style: const TextStyle(
                    color: AppColors.primaryColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ],
        ),
      );
    }

    return const SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    return BlocListener<DepositBloc, DepositState>(
      listener: (context, state) {
        if (state.status == DepositStatus.success) {
          // ❌ REMOVE: Navigator.pop(context); // Don't close here

          // Launch payment URL if available
          final paymentUrl = state.depositData?['payment_url'] as String?;
          if (paymentUrl != null && paymentUrl.isNotEmpty) {
            _launchPaymentUrl(paymentUrl); // This will close the bottom sheet
          } else {
            // ✅ Only close if no payment URL (shouldn't happen normally)
            Navigator.pop(context);
          }

          // Refresh account data after successful deposit
          context.read<AccountBloc>().add(AccountRequestLoad());
          context.read<PendingDepositBloc>().add(PendingDepositRequestLoad());

          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.successMessage),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      },
      child: Container(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: Container(
          decoration: BoxDecoration(
            color: isDarkMode ? AppColors.boxDarkColor : Colors.white,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Handle bar
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              const SizedBox(height: 20),

              // Title
              Text(
                context.loc.topUpWallet,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
              ),
              const SizedBox(height: 20),

              // Error display within the modal
              BlocBuilder<DepositBloc, DepositState>(
                builder: (context, state) {
                  if (state.status == DepositStatus.failure &&
                      state.errorMessage.isNotEmpty) {
                    return Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? Colors.red.shade900.withOpacity(0.3)
                            : Colors.red.shade50,
                        border: Border.all(
                            color: isDarkMode
                                ? Colors.red.shade700
                                : Colors.red.shade200),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: isDarkMode
                                ? Colors.red.shade300
                                : Colors.red.shade600,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              state.errorMessage,
                              style: TextStyle(
                                color: isDarkMode
                                    ? Colors.red.shade200
                                    : Colors.red.shade800,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),

              // Payment Methods Section
              Text(
                context.loc.selectPaymentMethod,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: isDarkMode ? Colors.white70 : Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
              ),
              const SizedBox(height: 12),

              // Payment Methods Horizontal Scroll
              SizedBox(
                height: 72, // Fixed height for the payment methods
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  child: Row(
                    children: _getPaymentMethods(context)
                        .asMap()
                        .entries
                        .map((entry) {
                      final index = entry.key;
                      final method = entry.value;
                      final isSelected =
                          _selectedPaymentMethod == method['value'];

                      return Padding(
                        padding: EdgeInsets.only(
                          left:
                              index == 0 ? 0 : 12, // Add spacing between items
                        ),
                        child: GestureDetector(
                          onTap: () => _selectPaymentMethod(method['value']!),
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 200),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? AppColors.primaryColor.withOpacity(0.1)
                                  : (isDarkMode
                                      ? Colors.grey[800]
                                      : Colors.grey[100]),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: isSelected
                                    ? AppColors.primaryColor
                                    : Colors.transparent,
                                width: 2,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(6),
                                  child: Image.asset(
                                    method['image']!,
                                    width: 28,
                                    height: 28,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Container(
                                        width: 28,
                                        height: 28,
                                        decoration: BoxDecoration(
                                          color: AppColors.primaryColor
                                              .withOpacity(0.2),
                                          borderRadius:
                                              BorderRadius.circular(6),
                                        ),
                                        child: const Icon(
                                          Icons.payment,
                                          size: 16,
                                          color: AppColors.primaryColor,
                                        ),
                                      );
                                    },
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  method['label']!,
                                  style: TextStyle(
                                    color: isSelected
                                        ? AppColors.primaryColor
                                        : (isDarkMode
                                            ? Colors.white
                                            : Colors.black87),
                                    fontWeight: isSelected
                                        ? FontWeight.w600
                                        : FontWeight.w500,
                                  ),
                                ),
                                if (isSelected) ...[
                                  const SizedBox(width: 8),
                                  const Icon(
                                    Icons.check_circle,
                                    size: 18,
                                    color: AppColors.primaryColor,
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Quick Amounts Section
              Text(
                context.loc.quickAmounts,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                children: _quickAmounts.map((amount) {
                  final isSelected = _selectedQuickAmount == amount;
                  return GestureDetector(
                    onTap: () => _selectQuickAmount(amount),
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? AppColors.primaryColor
                            : (isDarkMode
                                ? Colors.grey[800]
                                : Colors.grey[100]),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        '\$$amount',
                        style: TextStyle(
                          color: isSelected
                              ? Colors.white
                              : (isDarkMode ? Colors.white : Colors.black87),
                          fontWeight:
                              isSelected ? FontWeight.w600 : FontWeight.w500,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),

              const SizedBox(height: 20),

              // Amount Input
              TextField(
                controller: _amountController,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
                onChanged: (value) {
                  setState(() {
                    // Clear quick amount selection when typing manually
                    if (value != _selectedQuickAmount?.toString()) {
                      _selectedQuickAmount = null;
                    }
                  });
                },
                decoration: InputDecoration(
                  labelText: context.loc.enterAmount,
                  prefixText: '\$ ',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: isDarkMode ? Colors.grey[600]! : Colors.grey[300]!,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: AppColors.primaryColor,
                      width: 2,
                    ),
                  ),
                  filled: true,
                  fillColor: isDarkMode ? Colors.grey[800] : Colors.grey[50],
                  labelStyle: TextStyle(
                    color: isDarkMode ? Colors.white60 : Colors.grey[600],
                  ),
                ),
                style: TextStyle(
                  color: isDarkMode ? Colors.white : Colors.black,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),

              const SizedBox(height: 16),

              // Exchange Rate Display
              BlocBuilder<ExchangeRateBloc, ExchangeRateState>(
                builder: (context, exchangeState) {
                  return _buildExchangeRateDisplay(context, exchangeState);
                },
              ),

              const SizedBox(height: 24),

              // Confirm Button
              BlocBuilder<DepositBloc, DepositState>(
                builder: (context, depositState) {
                  final isLoading =
                      depositState.status == DepositStatus.loading;

                  return SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: (_canConfirm && !isLoading)
                          ? () {
                              final amount =
                                  double.tryParse(_amountController.text);
                              if (amount != null &&
                                  amount > 0 &&
                                  _selectedPaymentMethod != null) {
                                context.read<DepositBloc>().add(
                                      DepositSubmitted(
                                        amount: amount,
                                        gateway: _selectedPaymentMethod!,
                                      ),
                                    );
                              }
                            }
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryColor,
                        foregroundColor: Colors.white,
                        disabledBackgroundColor:
                            isDarkMode ? Colors.grey[700] : Colors.grey[300],
                        disabledForegroundColor:
                            isDarkMode ? Colors.grey[500] : Colors.grey[500],
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                      ),
                      child: isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text(
                              _canConfirm
                                  ? context.loc
                                      .confirmTopUp(_amountController.text)
                                  : context.loc.selectPaymentMethodToContinue,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ),
                  );
                },
              ),
              const SizedBox(height: 12),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _launchPaymentUrl(String paymentUrl) async {
    try {
      // Clean and properly encode the URL
      String cleanUrl = paymentUrl.trim();

      // Parse the URL to ensure it's valid
      final Uri url = Uri.parse(cleanUrl);

      bool launched = false;

      // Try external application first (banking apps)
      if (!launched) {
        try {
          launched = await launchUrl(
            url,
            mode: LaunchMode.externalApplication,
          );
        } catch (e) {
          debugPrint('External application failed: $e');
        }
      }

      // Try external browser as fallback
      if (!launched) {
        try {
          launched = await launchUrl(
            url,
            mode: LaunchMode.externalNonBrowserApplication,
          );
        } catch (e) {
          debugPrint('External browser failed: $e');
        }
      }

      // Try platform default as last resort
      if (!launched) {
        try {
          launched = await launchUrl(
            url,
            mode: LaunchMode.platformDefault,
          );
        } catch (e) {
          debugPrint('Platform default failed: $e');
        }
      }

      if (!launched) {
        throw Exception('Could not launch URL with any method');
      }

      // ✅ NEW: Close the bottom sheet immediately after launching URL
      if (mounted) {
        Navigator.of(context).pop(); // Close the top-up bottom sheet
      }
    } catch (e) {
      debugPrint('Error launching payment URL: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Could not open payment link'),
            backgroundColor: Colors.orange,
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: 'Copy Link',
              textColor: Colors.white,
              onPressed: () {
                Clipboard.setData(ClipboardData(text: paymentUrl));
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Payment link copied to clipboard'),
                    backgroundColor: Colors.green,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
            ),
          ),
        );
      }
    }
  }
}
