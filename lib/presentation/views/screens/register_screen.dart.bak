import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/domain/auth/bloc/authentication_bloc.dart';
import 'package:goldenprizma/domain/auth/bloc/register_bloc.dart';
import 'package:goldenprizma/domain/models/city.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/helpers/custom_dialogs.dart';
import 'package:goldenprizma/helpers/custom_snackbars.dart';
import 'package:goldenprizma/main.dart';
import 'package:goldenprizma/presentation/views/auth/login_screen.dart';
import 'package:goldenprizma/presentation/views/forms/auth/city_input.dart';
import 'package:goldenprizma/presentation/views/forms/auth/fullname_input.dart';
import 'package:goldenprizma/presentation/views/forms/auth/password_input.dart';
import 'package:goldenprizma/presentation/views/forms/auth/phone_code_input.dart';
import 'package:goldenprizma/presentation/views/forms/auth/phone_number_input.dart';
import 'package:goldenprizma/presentation/views/screens/check_auth_screen.dart';
import 'package:goldenprizma/repositories/city_repository.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:ionicons/ionicons.dart';
import 'package:provider/provider.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen(
      {super.key,
      required this.phoneNumber,
      required this.dialCode,
      required this.countryCode});
  final String phoneNumber;
  final String dialCode;
  final String countryCode;
  static const String routeName = '/register';
  @override
  _RegisterScreenState createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final TextEditingController _fullNameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  late TextEditingController _phoneController;

  late PhoneNumber _phoneNumber;
  String? _confirmPasswordValidationMessage;
  final double kHorizontalPadding = 24.0;
  bool _passwordIsVisible = false;

  @override
  void initState() {
    _phoneNumber = PhoneNumber(isoCode: widget.countryCode);
    _phoneController = TextEditingController(text: widget.phoneNumber);
    SchedulerBinding.instance.addPostFrameCallback((_) {
      context.read<RegisterBloc>().add(RegisterFormChanged(
            form: context.read<RegisterBloc>().state.form.copyWith(
                  phoneNumberInput: PhoneNumberInput.dirty(
                      widget.phoneNumber.replaceAll(widget.dialCode, '')),
                  phoneCodeInput: PhoneCodeInput.dirty(widget.dialCode),
                ),
          ));
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: LayoutBuilder(
          builder: (BuildContext context, BoxConstraints viewportConstraints) {
        return SingleChildScrollView(
          child: BlocListener<RegisterBloc, RegisterState>(
            listener: (context, state) {
              if (state.status.isSubmissionFailure && !state.hasServerError) {
                showErrorSnackBar(
                  context: context,
                  message: context.loc.generalErrorMessage,
                );
              }

              if (state.status.isSubmissionFailure && state.hasServerError) {
                showErrorSnackBar(
                  context: context,
                  message: state.apiErrorMessage,
                );
              }

              if (state.status.isSubmissionSuccess) {
                showSuccessDialogWithAutoHide(
                  context: context,
                  message: state.apiSuccessMessage,
                ).then((value) {
                  context
                      .read<AuthenticationBloc>()
                      .add(AuthenticationUserRequested());
                  Navigator.of(context)
                      .pushReplacementNamed(CheckAuthScreen.routeName);
                });

                //reset all fields
                _resetFields();
              }
            },
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: viewportConstraints.maxHeight,
              ),
              child: IntrinsicHeight(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: double.infinity,
                      height: 200,
                      child: Stack(
                        children: [
                          ClipPath(
                            clipper: OvalCliper(),
                            child: Container(
                              width: double.infinity,
                              color: Provider.of<AppProvider>(context)
                                      .isDarkMode(context)
                                  ? Colors.black
                                  : Colors.grey.shade900,
                              height: 200,
                            ),
                          ),
                          const Center(
                            child: Image(
                              image: AssetImage('assets/images/logo.png'),
                              width: 96,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 32),
                    Text(
                      context.loc.createNewAccount,
                      style: const TextStyle(
                          fontSize: 24, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 32),
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: kHorizontalPadding),
                        child: Column(
                          children: [
                            _nameField(),
                            const SizedBox(height: 16),
                            IgnorePointer(ignoring: true, child: _phoneField()),
                            const SizedBox(height: 16),
                            CityField(),
                            const SizedBox(height: 16),
                            _passwordField(),
                            const SizedBox(height: 16),
                            _confirmPasswordField(),
                            const SizedBox(height: 32),
                            _registerButton(),
                            const SizedBox(height: 32),
                            _signInButton(),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }),
    );
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Widget _nameField() {
    return BlocBuilder<RegisterBloc, RegisterState>(
      builder: (context, state) {
        return TextField(
          autocorrect: false,
          controller: _fullNameController,
          decoration: InputDecoration(
              prefixIcon: const Icon(
                Icons.person,
                color: Colors.grey,
              ),
              hintText: context.loc.fullName,
              filled: true,
              errorText:
                  state.form.fullNameInput.validationErrorMessage(context)),
          onChanged: (value) {
            context.read<RegisterBloc>().add(RegisterFormChanged(
                  form: state.form.copyWith(
                    fullNameInput: FullNameInput.dirty(value),
                  ),
                ));
          },
        );
      },
    );
  }

  Widget _phoneField() {
    return BlocBuilder<RegisterBloc, RegisterState>(
      builder: (context, state) {
        return Directionality(
          textDirection: TextDirection.ltr,
          child: InternationalPhoneNumberInput(
            onInputChanged: (PhoneNumber number) async {
              final RegExp code = RegExp('[+]${number.dialCode}');
              context.read<RegisterBloc>().add(RegisterFormChanged(
                    form: state.form.copyWith(
                      phoneNumberInput: PhoneNumberInput.dirty(
                          number.phoneNumber.toString().replaceAll(code, '')),
                      phoneCodeInput:
                          PhoneCodeInput.dirty(number.dialCode ?? '+964'),
                    ),
                  ));
            },
            selectorConfig: const SelectorConfig(
              selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
              setSelectorButtonAsPrefixIcon: true,
              leadingPadding: 16.0,
            ),
            ignoreBlank: true,
            autoValidateMode: AutovalidateMode.disabled,
            initialValue: _phoneNumber,
            textFieldController: _phoneController,
            formatInput: false,
            keyboardType: TextInputType.phone,
            inputBorder: const OutlineInputBorder(),
            maxLength: 10,
            inputDecoration: InputDecoration(
              errorText:
                  state.form.phoneNumberInput.validationErrorMessage(context),
            ),
          ),
        );
      },
    );
  }

  Widget _passwordField() {
    return BlocBuilder<RegisterBloc, RegisterState>(
      builder: (context, state) {
        return TextField(
          controller: _passwordController,
          obscureText: !_passwordIsVisible,
          decoration: InputDecoration(
            prefixIcon: const Icon(
              Ionicons.lock_closed,
              color: Colors.grey,
            ),
            suffixIcon: IconButton(
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              tooltip: 'Toggle password visibility',
              icon: _passwordIsVisible
                  ? const Icon(Ionicons.eye_off)
                  : const Icon(Ionicons.eye),
              onPressed: () {
                setState(() {
                  _passwordIsVisible = !_passwordIsVisible;
                });
              },
            ),
            hintText: context.loc.passwordPlacholder,
            filled: true,
            errorText: state.form.passwordInput.validationErrorMessage(context),
          ),
          onChanged: (value) {
            context.read<RegisterBloc>().add(RegisterFormChanged(
                  form: state.form.copyWith(
                    passwordInput: PasswordInput.dirty(value),
                  ),
                ));
          },
        );
      },
    );
  }

  Widget _confirmPasswordField() {
    return BlocBuilder<RegisterBloc, RegisterState>(
      builder: (context, state) {
        return TextField(
          controller: _confirmPasswordController,
          obscureText: !_passwordIsVisible,
          decoration: InputDecoration(
              prefixIcon: const Icon(
                Ionicons.lock_closed,
                color: Colors.grey,
              ),
              suffixIcon: IconButton(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                tooltip: 'Toggle password visibility',
                icon: _passwordIsVisible
                    ? const Icon(Ionicons.eye_off)
                    : const Icon(Ionicons.eye),
                onPressed: () {
                  setState(() {
                    _passwordIsVisible = !_passwordIsVisible;
                  });
                },
              ),
              hintText: context.loc.confirmPasswordPlacholder,
              filled: true,
              errorText: state.form.confirmPasswordInput.invalid
                  ? state.form.confirmPasswordInput
                      .validationErrorMessage(context)
                  : _confirmPasswordValidationMessage),
          onChanged: (value) {
            context.read<RegisterBloc>().add(RegisterFormChanged(
                  form: state.form.copyWith(
                    confirmPasswordInput: PasswordInput.dirty(value),
                  ),
                ));
            if (state.form.passwordInput.value != value.trim()) {
              setState(() {
                _confirmPasswordValidationMessage =
                    'It doesn\'t match the password';
              });
            } else {
              setState(() {
                _confirmPasswordValidationMessage = null;
              });
            }
          },
        );
      },
    );
  }

  Widget _registerButton() {
    return BlocBuilder<RegisterBloc, RegisterState>(
      builder: (context, state) {
        return MaterialButton(
          onPressed: state.status.isSubmissionInProgress
              ? null
              : () {
                  context
                      .read<RegisterBloc>()
                      .add(const RegisterFormSubmitted());
                },
          color: Theme.of(context).primaryColor,
          minWidth: MediaQuery.of(context).size.width / 2,
          elevation: 0,
          child: state.status.isSubmissionInProgress
              ? const CircularProgressIndicator.adaptive()
              : Text(
                  context.loc.register,
                  style: const TextStyle(color: Colors.white),
                ),
        );
      },
    );
  }

  Widget _signInButton() {
    return BlocBuilder<RegisterBloc, RegisterState>(
      builder: (context, state) {
        return TextButton(
          onPressed: state.status.isSubmissionInProgress
              ? null
              : () {
                  Navigator.pushNamed(context, LoginScreen.routeName);
                },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                context.loc.alreadyHaveAccount,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(width: 4),
              Text(context.loc.signIn),
            ],
          ),
        );
      },
    );
  }

  void _resetFields() {
    _fullNameController.clear();
    _passwordController.clear();
    _confirmPasswordController.clear();
    _phoneController.clear();
    context.read<RegisterBloc>().add(const RegisterFormReset());
  }
}

class OvalCliper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    var path = Path();
    path.lineTo(0, size.height - 50);
    path.quadraticBezierTo(
        size.width / 2, size.height + 50, size.width + 50, size.height - 60);
    path.lineTo(size.width, 0);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return true;
  }
}

class CityField extends StatelessWidget {
  CityField({super.key});
  final TextEditingController _sizeEditTextController = TextEditingController();
  final CityRepository _cityRepository = getIt.get<CityRepository>();
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<RegisterBloc, RegisterState>(
      builder: (context, state) {
        return DropdownSearch<City>(
          key: UniqueKey(),
          popupProps: PopupProps.modalBottomSheet(
            scrollbarProps: const ScrollbarProps(
              thickness: 4,
            ),
            showSearchBox: true,
            showSelectedItems: true,
            isFilterOnline: true,
            searchFieldProps:
                TextFieldProps(controller: _sizeEditTextController),
            itemBuilder: _customPopupItemBuilder,
          ),
          filterFn: (City? city, filter) {
            return city != null && city.filterByName(filter);
          },
          asyncItems: (String? filter) async {
            List<City> cities = await _cityRepository.getAll(search: filter);
            return cities;
          },
          onChanged: (City? data) {
            context.read<RegisterBloc>().add(RegisterFormChanged(
                  form: state.form.copyWith(cityInput: CityInput.dirty(data)),
                ));
          },
          selectedItem: state.form.cityInput.value,
          compareFn: (item, selectedItem) => item.id == selectedItem.id,
          validator: (u) => u == null ? "Citys is required " : null,
          dropdownDecoratorProps: DropDownDecoratorProps(
            dropdownSearchDecoration: InputDecoration(
              isCollapsed: true,
              contentPadding:
                  const EdgeInsetsDirectional.fromSTEB(16, 14, 1, 0),
              errorText: state.form.cityInput.validationErrorMessage(context),
              hintText: "City",
              prefixIcon: const Icon(Ionicons.map),
            ),
          ),
        );
      },
    );
  }

  Widget _customPopupItemBuilder(
      BuildContext context, City? item, bool isSelected) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: !isSelected
          ? null
          : BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(5),
            ),
      child: ListTile(
        selected: isSelected,
        title: Text(item?.name ?? ''),
      ),
    );
  }
}
