import 'package:flutter/material.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/helpers/branches.dart';
import 'package:goldenprizma/main.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/screens/contact_widget/contact_tab_view.dart';
import 'package:goldenprizma/repositories/contact_repository.dart';
import 'package:provider/provider.dart';

class ContactScreen extends StatefulWidget {
  const ContactScreen({super.key});

  static PageRouteBuilder pageRoute() {
    return PageRouteBuilder(pageBuilder: (BuildContext context,
        Animation<double> animation, Animation<double> secondaryAnimation) {
      return const ContactScreen();
    });
  }

  @override
  State<ContactScreen> createState() => _ContactScreenState();
}

class _ContactScreenState extends State<ContactScreen>
    with TickerProviderStateMixin {
  final ContactRepository contactRepository = getIt.get<ContactRepository>();
  late TabController tabController;
  @override
  void initState() {
    tabController = TabController(length: 3, vsync: this);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        titleSpacing: 4,
        title: SizedBox(
          height: 24,
          child: Image.asset('assets/images/text_logo.png'),
        ),
        elevation: 0,
        backgroundColor: Provider.of<AppProvider>(context).isDarkMode(context)
            ? Theme.of(context).colorScheme.surface
            : Colors.grey[300],
      ),
      backgroundColor: Provider.of<AppProvider>(context).isDarkMode(context)
          ? Theme.of(context).colorScheme.surface
          : Colors.grey[300],
      body: Column(
        children: [
          ContactTabBar(tabController: tabController),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              child: TabBarView(
                controller: tabController,
                children: [
                  ContactTabView(branchId: Branches.erbil),
                  ContactTabView(branchId: Branches.sulaimany),
                  ContactTabView(branchId: Branches.duhok),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ContactTabBar extends StatelessWidget {
  const ContactTabBar({
    super.key,
    required this.tabController,
  });

  final TabController tabController;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80,
      color: AppColors.primaryColor,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
        height: 80,
        color: Provider.of<AppProvider>(context).isDarkMode(context)
            ? Theme.of(context).colorScheme.surface
            : Colors.grey[300],
        child: TabBar(
          controller: tabController,
          labelColor: Provider.of<AppProvider>(context).isDarkMode(context)
              ? Colors.white
              : Colors.black87,
          indicator: ShapeDecoration(
              color: AppColors.primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
              )),
          tabs: const [
            Tab(
              icon: Text(
                'Erbil',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
            ),
            Tab(
              icon: Text(
                'Sulaimany',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
            ),
            Tab(
              icon: Text(
                'Duhok',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
            )
          ],
        ),
      ),
    );
  }
}
