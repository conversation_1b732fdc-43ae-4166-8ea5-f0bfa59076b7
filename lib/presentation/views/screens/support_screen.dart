import 'dart:async';

import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/domain/support/bloc/ticket_bloc.dart';
import 'package:goldenprizma/domain/support/models/ticket.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/helpers/custom_snackbars.dart';
import 'package:goldenprizma/main.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/forms/support/ticket_form.dart';
import 'package:goldenprizma/presentation/views/screens/ticket_detail_screen.dart';
import 'package:goldenprizma/presentation/widgets/scroll_loader.dart';
import 'package:goldenprizma/repositories/support_repository.dart';
import 'package:ionicons/ionicons.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';

class SupportScreen extends StatefulWidget {
  const SupportScreen({super.key});

  static MaterialPageRoute<SupportScreen> pageRoute() {
    return MaterialPageRoute(builder: (BuildContext context) {
      return MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (_) => TicketBloc(
              supportRepository: getIt.get<SupportRepository>(),
            )..add(TicketReqeustLoad()),
          ),
        ],
        child: const SupportScreen(),
      );
    });
  }

  @override
  _SupportScreenState createState() => _SupportScreenState();
}

class _SupportScreenState extends State<SupportScreen> {
  final ScrollController _scrollController = ScrollController();
  final String _debounceID = 'request_scroll_listener';

  @override
  void initState() {
    _scrollController.addListener(_debouncedListener);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Support"),
        elevation: 0,
      ),
      body: BlocBuilder<TicketBloc, TicketState>(
        builder: (context, state) {
          if (state.status == TicketStateStatus.initial) {
            return _shimmerList(context);
          }

          if (state.status == TicketStateStatus.failure ||
              state.status == TicketStateStatus.refresh) {
            return _buildErrorWidget(state, context);
          }

          return Padding(
            padding: const EdgeInsets.all(12.0),
            child: ListView.separated(
              controller: _scrollController,
              itemBuilder: (context, int index) {
                if (index < state.tickets.length) {
                  return _buildTicketRow(context, state.tickets[index]);
                }
                Timer(const Duration(milliseconds: 0), () {
                  _scrollController
                      .jumpTo(_scrollController.position.maxScrollExtent - 10);
                });
                return scrollLoaderIndicator();
              },
              separatorBuilder: (_, __) => const SizedBox(),
              itemCount: state.tickets.length +
                  (state.status == TicketStateStatus.loading ? 1 : 0),
            ),
          );
        },
      ),
      floatingActionButton: BlocBuilder<TicketBloc, TicketState>(
        builder: (context, state) {
          return FloatingActionButton(
            onPressed: () {
              Iterable<Ticket> tickets = state.tickets.where((ticket) {
                return ticket.status == 'created' &&
                    DateTime.parse(ticket.createdAt).isAfter(
                      DateTime.now().subtract(const Duration(hours: 24)),
                    );
              });
              // has open ticket show error
              if (tickets.isNotEmpty && tickets.length >= 2) {
                showErrorSnackBar(
                    context: context,
                    message: context.loc.createTicketErrorIn24Hours);
                return;
              }
              Navigator.of(context)
                  .push(TicketForm.pageRoute(context.read<TicketBloc>()));
            },
            tooltip: 'Create new ticket',
            child: const Icon(
              Ionicons.add,
              color: Colors.white,
            ),
          );
        },
      ),
    );
  }

  SizedBox _buildErrorWidget(TicketState state, BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(state.errorMessage),
          const SizedBox(height: 12),
          TextButton(
            onPressed: () {
              context.read<TicketBloc>().add(TicketReload());
            },
            child: state.status == TicketStateStatus.refresh
                ? const CircularProgressIndicator.adaptive()
                : Text(context.loc.tryAgain),
          ),
        ],
      ),
    );
  }

  void _debouncedListener() {
    EasyDebounce.debounce(
      _debounceID,
      const Duration(milliseconds: 300),
      _onScroll,
    );
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<TicketBloc>().add(TicketReqeustLoad());
    }
  }

  Widget _buildTicketRow(BuildContext context, Ticket ticket) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context)
            .push(TicketDetailScreen.pageRoute(ticket: ticket));
      },
      child: Card(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
          child: Table(
            columnWidths: const <int, TableColumnWidth>{
              0: FixedColumnWidth(48),
              1: FixedColumnWidth(12),
            },
            children: [
              _dataRow(context.loc.idLable, ticket.id.toString(), context),
              _dataRow(context.loc.title, ticket.subject, context),
              _dataRow(context.loc.date, ticket.dateFormat.toString(), context),
              _dataRow(context.loc.status, ticket.getStatus(context), context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _shimmerList(context) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    return Padding(
      padding: const EdgeInsets.only(top: 16.0),
      child: Shimmer.fromColors(
        baseColor: isDarkMode ? AppColors.boxDarkColor : Colors.grey.shade300,
        highlightColor: isDarkMode ? Colors.black45 : Colors.grey.shade100,
        enabled: true,
        child: ListView.separated(
          itemBuilder: (_, __) => Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Container(
                  width: 120,
                  height: 8.0,
                  color: Colors.white,
                ),
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 2.0),
                ),
                Container(
                  width: 140,
                  height: 8.0,
                  color: Colors.white,
                ),
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 2.0),
                ),
                Container(
                  width: 150,
                  height: 8.0,
                  color: Colors.white,
                ),
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 2.0),
                ),
                Container(
                  width: 100,
                  height: 8.0,
                  color: Colors.white,
                ),
              ],
            ),
          ),
          itemCount: 15,
          separatorBuilder: (BuildContext context, int index) {
            return const Divider();
          },
        ),
      ),
    );
  }

  TableRow _dataRow(String label, String data, context) {
    Widget dataWidget = Text(
      data,
    );

    if (label.toLowerCase() == 'status') {
      dataWidget = Text(
        data,
        style: TextStyle(
            color: data.toLowerCase() == 'open'
                ? AppColors.primaryColor
                : Colors.green),
      );
    }
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.middle,
          child: SizedBox(
            height: 20,
            child: Text(label, style: Theme.of(context).textTheme.bodySmall),
          ),
        ),
        const TableCell(
          verticalAlignment: TableCellVerticalAlignment.middle,
          child: SizedBox(child: Text(':')),
        ),
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.middle,
          child: SizedBox(
            width: 40,
            child: dataWidget,
          ),
        ),
      ],
    );
  }

  bool get _isBottom {
    return _scrollController.position.atEdge &&
        _scrollController.position.pixels != 0;
  }

  @override
  void dispose() {
    _scrollController
      ..removeListener(_debouncedListener)
      ..dispose();
    EasyDebounce.cancel(_debounceID);
    super.dispose();
  }
}
