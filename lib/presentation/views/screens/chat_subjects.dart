import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/chat_subjects/chat_subjects_bloc.dart';
import 'package:goldenprizma/presentation/views/screens/chat_screen.dart';
import 'package:goldenprizma/presentation/widgets/auth_wrapper.dart';
import 'package:hugeicons/hugeicons.dart';

class ChatSubjects extends StatefulWidget {
  final String type;
  final String? orderId;

  static pageRoute({String type = 'general', String? orderId}) {
    return MaterialPageRoute<void>(
      builder: (BuildContext context) =>
          AuthWrapper(child: ChatSubjects(type: type, orderId: orderId)),
    );
  }

  const ChatSubjects({
    super.key,
    this.type = 'general',
    this.orderId,
  });

  @override
  State<ChatSubjects> createState() => _ChatSubjectsState();
}

class _ChatSubjectsState extends State<ChatSubjects> {
  @override
  void initState() {
    super.initState();
    context
        .read<ChatSubjectsBloc>()
        .add(ChatSubjectsRequested(type: widget.type));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Select an issue"),
      ),
      body: BlocBuilder<ChatSubjectsBloc, ChatSubjectsState>(
        builder: (context, state) {
          if (state.status == ChatSubjectStatus.loading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (state.status == ChatSubjectStatus.failure) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text("An error occurred"),
                  const SizedBox(height: 8),
                  TextButton(
                    onPressed: () {
                      context
                          .read<ChatSubjectsBloc>()
                          .add(ChatSubjectsRequested(type: widget.type));
                    },
                    child: const Text("Try Again"),
                  )
                ],
              ),
            );
          }
          return Column(
            children: [
              const SizedBox(height: 24),
              const Icon(
                HugeIcons.strokeRoundedCustomerSupport,
                size: 32,
              ),
              const SizedBox(height: 24),
              const Text("How can we help you today?",
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  )),
              const SizedBox(height: 24),
              Expanded(
                  child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: ListView.separated(
                  itemCount: state.subjects.length + 1,
                  itemBuilder: (context, index) {
                    final isLast = index == state.subjects.length;
                    if (isLast) {
                      return subjectCard(
                          name: "Other",
                          onTap: () {
                            Navigator.of(context).push(
                              ChatScreen.pageRoute(
                                subject: widget.type == 'order'
                                    ? "Other Order #${widget.orderId}"
                                    : "Other",
                              ),
                            );
                          });
                    }

                    final subject = state.subjects[index];
                    return subjectCard(
                        name: subject.getName(context),
                        onTap: () {
                          Navigator.of(context).push(
                            ChatScreen.pageRoute(
                              subject: widget.type == 'order'
                                  ? "${subject.getName(context)} Order #${widget.orderId}"
                                  : subject.getName(context),
                            ),
                          );
                        });
                  },
                  separatorBuilder: (BuildContext context, int index) {
                    return const Divider(
                      height: 4,
                    );
                  },
                ),
              )),
            ],
          );
        },
      ),
    );
  }

  Widget subjectCard(
      {required String name, required GestureTapCallback onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: ListTile(
          title: Text(
            name,
            style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
          ),
          trailing: const Icon(Icons.arrow_forward_ios, color: Colors.grey),
        ),
      ),
    );
  }
}
