import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/auth/bloc/authentication_bloc.dart';
import 'package:goldenprizma/domain/auth/models/user.dart';
import 'package:goldenprizma/domain/notifications/bloc/notification_bloc.dart';
import 'package:goldenprizma/presentation/components/customer_navigation_bar.dart';
import 'package:goldenprizma/presentation/views/auth/login_screen.dart';
import 'package:goldenprizma/presentation/views/pages/improved_home_page.dart';
import 'package:goldenprizma/presentation/views/pages/my_orders_page.dart';
import 'package:goldenprizma/presentation/views/pages/store_page.dart';
import 'package:goldenprizma/presentation/views/pages/websites_page.dart';
import 'package:goldenprizma/presentation/views/screens/account_screen.dart';
import 'package:goldenprizma/presentation/views/screens/notifications_screen.dart';
import 'package:goldenprizma/presentation/views/screens/paste_link_input_screen.dart';
import 'package:goldenprizma/presentation/widgets/processed_order_bottom_sheet.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:new_version_plus/new_version_plus.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key, this.initialPage = 0});
  static const String routeName = '/home';
  final int initialPage;

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late final PageController _pageController;
  final ValueNotifier<int> _currentIndex = ValueNotifier<int>(0);
  final ValueNotifier<String?> selectedStatus = ValueNotifier<String?>(null);

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: widget.initialPage);
    _currentIndex.value = widget.initialPage;

    // Check initial authentication state
    _handleAuthenticationState(context.read<AuthenticationBloc>().state);

    checkVersion();
  }

  void _handleAuthenticationState(AuthenticationState authState) {
    if (authState.isAuthenticated) {
      // Load notifications for authenticated users
      context.read<NotificationBloc>().add(NotificationRequestLoad());

      // Show processed orders bottom sheet if applicable
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          final User user = authState.user;

          if (user.numebrOfProcessedOrders! > 0) {
            showProcessedOrderBottomSheet(
              context: context,
              processedOrders: user.numebrOfProcessedOrders!,
              onPressed: () {
                _onChange(3, status: 'Processed');
              },
            );
          }
        }
      });
    }
  }

  void _onChange(int index, {String? status}) {
    if (index == 3 &&
        !context.read<AuthenticationBloc>().state.isAuthenticated) {
      Navigator.of(context).pushNamed(LoginScreen.routeName);
      return;
    }

    _currentIndex.value = index;
    if (status != null) selectedStatus.value = status;

    _pageController.jumpToPage(index);
  }

  Future<void> checkVersion() async {
    final newVersion = NewVersionPlus();
    final versionStatus = await newVersion.getVersionStatus();
    if (versionStatus!.canUpdate && mounted) {
      newVersion.showUpdateDialog(
        context: context,
        versionStatus: versionStatus,
        allowDismissal: false,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      appBar: AppBar(
        title: SizedBox(
          height: 24,
          child: Image.asset('assets/images/text_logo.png'),
        ),
        centerTitle: false,
        elevation: 0,
        automaticallyImplyLeading: false,
        actions: [
          BlocBuilder<NotificationBloc, NotificationState>(
            builder: (context, state) {
              return Row(
                children: [
                  IconButton(
                    padding: const EdgeInsets.all(4),
                    splashRadius: 1,
                    iconSize: 24.0,
                    onPressed: () {
                      Navigator.of(context).push(Notifications.pageRoute());
                    },
                    icon: Row(
                      children: [
                        Stack(
                          children: [
                            const Icon(HugeIcons.strokeRoundedNotification02),
                            if (state.totalUnreadNotifications > 0)
                              Positioned(
                                top: 0,
                                left: 0,
                                child: Container(
                                  width: 13.0,
                                  height: 13.0,
                                  decoration: const BoxDecoration(
                                    color: Colors.red,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Center(
                                    child: Text(
                                      state.unreadCounts,
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 9,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
      body: BlocListener<AuthenticationBloc, AuthenticationState>(
        listener: (context, state) {
          // Handle authentication state changes
          _handleAuthenticationState(state);
        },
        child: ValueListenableBuilder<int>(
          valueListenable: _currentIndex,
          builder: (context, index, child) {
            return PageView(
              controller: _pageController,
              onPageChanged: (index) {
                if (index == 3 &&
                    !context.read<AuthenticationBloc>().state.isAuthenticated) {
                  Navigator.of(context).pushNamed(LoginScreen.routeName);
                  return;
                }

                _currentIndex.value = index;
                if (index != 3) {
                  Future.delayed(const Duration(milliseconds: 500), () {
                    if (mounted) {
                      selectedStatus.value = null;
                    }
                  });
                }
              },
              children: [
                ImprovedHomePage(
                  key: ValueKey(index + 100),
                  onViewOrders: () => _onChange(3),
                  onViewWebsites: () => _onChange(2),
                ),
                const StorePage(),
                const WebsitesPage(),
                ValueListenableBuilder<String?>(
                  valueListenable: selectedStatus,
                  builder: (context, status, child) {
                    return MyOrdersPage(initialStatus: status);
                  },
                ),
                const AccountScreen(),
              ],
            );
          },
        ),
      ),
      floatingActionButton: ValueListenableBuilder<int>(
        valueListenable: _currentIndex,
        builder: (context, index, child) {
          // Hide FAB for Store page (index 1) and Account screen (index 4)
          if (index == 1 || index == 4) {
            return const SizedBox.shrink();
          }

          return FloatingActionButton(
            elevation: 1,
            child: const Icon(
              HugeIcons.strokeRoundedShoppingBagAdd,
              color: Colors.white,
            ),
            onPressed: () {
              if (!context.read<AuthenticationBloc>().state.isAuthenticated) {
                Navigator.of(context).pushNamed(LoginScreen.routeName);
                return;
              }

              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const PasteLinkInputScreen(),
                ),
              );
            },
          );
        },
      ),
      bottomNavigationBar: ValueListenableBuilder<int>(
        valueListenable: _currentIndex,
        builder: (context, index, child) {
          return CustomNavigationBar(
            index: index,
            onChange: _onChange,
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    _currentIndex.dispose();
    selectedStatus.dispose();
    super.dispose();
  }
}
