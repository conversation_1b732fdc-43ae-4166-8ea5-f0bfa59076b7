import 'dart:async';

import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/domain/request_deliveries/bloc/request_delivery_bloc.dart';
import 'package:goldenprizma/domain/request_deliveries/models/request_delivery.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/helpers/custom_snackbars.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/screens/create_request_delivery_screen.dart';
import 'package:goldenprizma/presentation/widgets/auth_wrapper.dart';

import 'package:goldenprizma/presentation/widgets/scroll_loader.dart';
import 'package:goldenprizma/repositories/request_delivery_repository.dart';
import 'package:intl/intl.dart';
import 'package:ionicons/ionicons.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import 'package:lottie/lottie.dart';

class RequestDeliveriesScreen extends StatefulWidget {
  const RequestDeliveriesScreen({super.key});

  static MaterialPageRoute<RequestDeliveriesScreen> pageRoute() {
    return MaterialPageRoute(builder: (BuildContext context) {
      return BlocProvider(
        create: (_) => RequestDeliveryBloc(
          repository: RequestDeliveryRepository(),
        )..add(RequestDeliveryReqeustLoad()),
        child: const AuthWrapper(child: RequestDeliveriesScreen()),
      );
    });
  }

  @override
  _RequestDeliveriesScreenState createState() =>
      _RequestDeliveriesScreenState();
}

class _RequestDeliveriesScreenState extends State<RequestDeliveriesScreen> {
  final ScrollController _scrollController = ScrollController();
  final String _debounceID = 'request_scroll_listener';

  @override
  void initState() {
    _scrollController.addListener(_debouncedListener);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.loc.requestDelivery),
        elevation: 0,
      ),
      body: BlocBuilder<RequestDeliveryBloc, RequestDeliveryState>(
        builder: (context, state) {
          if (state.status == RequestDeliveryStatus.initial) {
            return _shimmerList(context);
          }

          if (state.status == RequestDeliveryStatus.failure ||
              state.status == RequestDeliveryStatus.refresh) {
            return _buildErrorWidget(state, context);
          }

          if (state.deliveries.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    height: 200,
                    width: 200,
                    child: Lottie.asset(
                      'assets/empty_state.json',
                      fit: BoxFit.contain,
                      repeat: true,
                      animate: true,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    "No delivery requests yet",
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ],
              ),
            );
          }

          return Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: RefreshIndicator(
              onRefresh: () async {
                context
                    .read<RequestDeliveryBloc>()
                    .add(RequestDeliveryRefresh());
              },
              child: ListView.builder(
                physics: const AlwaysScrollableScrollPhysics(),
                controller: _scrollController,
                itemBuilder: (context, int index) {
                  if (index < state.deliveries.length) {
                    return _buildDeliveryRow(context, state.deliveries[index]);
                  }
                  Timer(const Duration(milliseconds: 0), () {
                    if (_scrollController.hasClients) {
                      _scrollController.jumpTo(
                          _scrollController.position.maxScrollExtent - 10);
                    }
                  });
                  return scrollLoaderIndicator();
                },
                itemCount: state.deliveries.length +
                    (state.status == RequestDeliveryStatus.loading ? 1 : 0),
              ),
            ),
          );
        },
      ),
      floatingActionButton:
          BlocBuilder<RequestDeliveryBloc, RequestDeliveryState>(
        builder: (context, state) {
          return FloatingActionButton(
            onPressed: () {
              if (state.deliveries.isNotEmpty) {
                Iterable<RequestDelivery> deliveriesIn24Hours =
                    state.deliveries.where((element) {
                  DateTime date = DateTime.parse(element.createdAt);
                  return date.month == DateTime.now().month &&
                      date.day == DateTime.now().day &&
                      element.status == 'created';
                });

                if (deliveriesIn24Hours.length >= 2) {
                  showErrorSnackBar(
                      context: context,
                      message: context.loc.deliveryRequestErrorIn24Hours);
                  return;
                }
              }
              // DateTime.parse('2022-04-11 16:15:02')
              Navigator.of(context).push(
                CreateRequestDeliveryScreen.pageRoute(
                  context.read<RequestDeliveryBloc>(),
                ),
              );
            },
            tooltip: 'Create new delivery request',
            child: const Icon(
              Ionicons.add,
              color: Colors.white,
            ),
          );
        },
      ),
    );
  }

  Widget _buildErrorWidget(RequestDeliveryState state, BuildContext context) {
    return Center(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Ionicons.alert_circle_outline,
            size: 60,
            color: Colors.red.withOpacity(0.7),
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Text(
              state.errorMessage,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              context.read<RequestDeliveryBloc>().add(RequestDeliveryRefresh());
            },
            icon: state.status == RequestDeliveryStatus.refresh
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Ionicons.refresh_outline),
            label: Text(context.loc.tryAgain),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _debouncedListener() {
    EasyDebounce.debounce(
      _debounceID,
      const Duration(milliseconds: 300),
      _onScroll,
    );
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<RequestDeliveryBloc>().add(RequestDeliveryReqeustLoad());
    }
  }

  bool get _isBottom {
    return _scrollController.position.atEdge &&
        _scrollController.position.pixels != 0;
  }

  @override
  void dispose() {
    _scrollController
      ..removeListener(_debouncedListener)
      ..dispose();
    EasyDebounce.cancel(_debounceID);
    super.dispose();
  }

  Widget _buildDeliveryRow(BuildContext context, RequestDelivery delivery) {
    // Format the date nicely
    String formattedDate = '';
    try {
      final dateTime = DateTime.parse(delivery.createdAt);
      final formatter = DateFormat('MMM dd, yyyy • HH:mm');
      formattedDate = formatter.format(dateTime);
    } catch (e) {
      formattedDate = delivery.createdAt;
    }

    final isOpen = delivery.status.toLowerCase() == 'created';
    final statusText = isOpen
        ? AppLocalizations.of(context)!.open
        : AppLocalizations.of(context)!.closed;
    final statusColor = isOpen ? AppColors.primaryColor : Colors.green;
    final isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          // Could be used to show delivery details in the future
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Top row with ID and Status
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // ID chip
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? AppColors.boxDarkColor
                          : Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Ionicons.pricetag_outline,
                            size: 14,
                            color:
                                isDarkMode ? Colors.white70 : Colors.black54),
                        const SizedBox(width: 4),
                        Text(
                          "#${delivery.id}",
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium!
                              .copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: isDarkMode
                                      ? Colors.white70
                                      : Colors.black54),
                        ),
                      ],
                    ),
                  ),

                  // Status chip
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          isOpen ? Ionicons.ellipse : Ionicons.checkmark_circle,
                          size: 14,
                          color: statusColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          statusText,
                          style:
                              Theme.of(context).textTheme.bodyMedium!.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: statusColor,
                                  ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Date with icon
              Row(
                children: [
                  Icon(
                    Ionicons.calendar_outline,
                    size: 16,
                    color: Theme.of(context).textTheme.bodySmall!.color,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    formattedDate,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Note section with speech bubble icon
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(top: 2),
                    child: Icon(
                      Ionicons.chatbubble_outline,
                      size: 16,
                      color: Theme.of(context).textTheme.bodyMedium!.color,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      delivery.note.isNotEmpty
                          ? delivery.note
                          : "No notes provided",
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _shimmerList(context) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Shimmer.fromColors(
        baseColor: isDarkMode ? AppColors.boxDarkColor : Colors.grey.shade300,
        highlightColor: isDarkMode ? Colors.black45 : Colors.grey.shade100,
        enabled: true,
        child: ListView.builder(
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (_, index) => Container(
            margin: const EdgeInsets.symmetric(vertical: 8.0),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                // Top row with ID and Status
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // ID placeholder
                    Container(
                      width: 80,
                      height: 28,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),

                    // Status placeholder
                    Container(
                      width: 70,
                      height: 28,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Date placeholder
                Row(
                  children: [
                    Container(
                      width: 16,
                      height: 16,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      width: 140,
                      height: 12,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // Note placeholder
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 16,
                      height: 16,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            width: double.infinity,
                            height: 10,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            width: MediaQuery.of(context).size.width * 0.7,
                            height: 10,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          itemCount: 5,
        ),
      ),
    );
  }
}
