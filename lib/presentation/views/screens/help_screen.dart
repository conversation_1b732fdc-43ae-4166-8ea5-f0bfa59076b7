import 'dart:io';

import 'package:flutter/material.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/helpers/app_settings.dart';
import 'package:goldenprizma/helpers/page_urls.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/screens/account_screen.dart';
import 'package:goldenprizma/presentation/views/screens/website_launcher.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:provider/provider.dart';

class HelpScreen extends StatelessWidget {
  static pageRoute() {
    return MaterialPageRoute<void>(
      builder: (BuildContext context) => const HelpScreen(),
    );
  }

  const HelpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final isDark = Provider.of<AppProvider>(context).isDarkMode(context);

    Color dividerColor =
        Platform.isIOS ? Colors.grey.shade300 : Colors.grey.shade300;

    if (isDark) {
      dividerColor =
          Platform.isIOS ? Colors.grey.shade500 : Colors.grey.shade800;
    }

    final Widget divider = Divider(
      color: dividerColor,
      height: 6,
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('Help'),
      ),
      body: SingleChildScrollView(
        padding:
            const EdgeInsets.only(left: 16, right: 16, bottom: 24, top: 24),
        child: Column(
          children: [
            MenuContainer(
              children: [
                _menuItem(
                  context: context,
                  label: "FAQ",
                  onTap: () async {
                    String local = await AppSettings.getLocale();
                    String url = PageUrls.faqs + local;
                    if (context.mounted) {
                      Navigator.of(context).push(MaterialPageRoute(
                        builder: (context) {
                          return WebsiteLauncher(
                            url: url,
                            websiteName: 'Faq',
                            minimalDesign: true,
                          );
                        },
                      ));
                    }
                  },
                  leadingIcon: HugeIcons.strokeRoundedUserQuestion01,
                ),
                divider,
                // _menuItem(
                //   context: context,
                //   label: "Chat with us",
                //   onTap: () {
                //     Navigator.of(context)
                //         .push(ChatSubjects.pageRoute(type: 'general'));
                //   },
                //   leadingIcon: HugeIcons.strokeRoundedCustomerSupport,
                // ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _menuItem({
    required BuildContext context,
    required String label,
    required VoidCallback onTap,
    IconData? leadingIcon,
    Widget? trailing,
    Widget? leading,
  }) {
    return ListTile(
      visualDensity: const VisualDensity(vertical: -4, horizontal: -4),
      leading: leading ??
          Icon(
            leadingIcon,
            size: 22,
            color: Theme.of(context).iconTheme.color,
          ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
      horizontalTitleGap: 6,
      title: Text(label),
      titleTextStyle: Theme.of(context).textTheme.bodyMedium,
      onTap: onTap,
      splashColor: Colors.transparent,
      trailing: trailing ??
          const Icon(
            HugeIcons.strokeRoundedArrowRight02,
            size: 20,
            color: AppColors.primaryColor,
          ),
    );
  }
}
