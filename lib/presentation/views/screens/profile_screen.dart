import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/domain/auth/bloc/account_deletion_bloc.dart';
import 'package:goldenprizma/domain/auth/bloc/authentication_bloc.dart';
import 'package:goldenprizma/domain/auth/bloc/profile_avatar_bloc.dart';
import 'package:goldenprizma/domain/auth/bloc/profile_bloc.dart';
import 'package:goldenprizma/domain/auth/models/user.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/helpers/custom_snackbars.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/forms/auth/profile_avatar.dart';
import 'package:goldenprizma/presentation/views/forms/auth/profile_form.dart';
// import 'package:goldenprizma/presentation/views/forms/auth/fullname_input.dart';
import 'package:goldenprizma/presentation/views/forms/auth/email_input.dart';
import 'package:goldenprizma/presentation/views/screens/change_password_screen.dart';
import 'package:goldenprizma/repositories/auth_repository.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:provider/provider.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  static const routeName = '/profile';

  @override
  _ProfileScreenState createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  Widget build(BuildContext context) {
    final isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    return Scaffold(
      backgroundColor:
          isDarkMode ? const Color(0xFF121212) : const Color(0xFFF8F9FA),
      appBar: _buildAppBar(context, isDarkMode),
      body: BlocListener<ProfileBloc, ProfileState>(
        listener: (context, state) {
          if (state.hasServerError && state.errorMessage.isNotEmpty) {
            showErrorSnackBar(context: context, message: state.errorMessage);
          }

          if (state.status.isSubmissionSuccess &&
              state.successMessage.isNotEmpty) {
            showSuccessSnackBar(
                context: context, message: state.successMessage);

            // get new profile details after updating profile
            context
                .read<AuthenticationBloc>()
                .add(AuthenticationUserRequested());
          }
        },
        child: BlocBuilder<AuthenticationBloc, AuthenticationState>(
          builder: (context, state) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Profile Header Card
                  _buildProfileHeaderCard(context, state.user, isDarkMode),
                  const SizedBox(height: 16),

                  // Personal Information Card
                  _buildPersonalInfoCard(context, state.user, isDarkMode),
                  const SizedBox(height: 16),

                  // Account Actions Card
                  _buildAccountActionsCard(context, isDarkMode),
                  const SizedBox(height: 16),

                  // Danger Zone Card
                  _buildDangerZoneCard(context, isDarkMode),
                  const SizedBox(height: 32),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  AppBar _buildAppBar(BuildContext context, bool isDarkMode) {
    return AppBar(
      elevation: 0,
      backgroundColor:
          isDarkMode ? const Color(0xFF121212) : const Color(0xFFF8F9FA),
      foregroundColor: isDarkMode ? Colors.white : Colors.black87,
      title: Text(
        context.loc.profile,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 20,
        ),
      ),
      centerTitle: true,
    );
  }

  // Profile Header Card with Avatar and Basic Info
  Widget _buildProfileHeaderCard(
      BuildContext context, User user, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withOpacity(0.2)
                : Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Avatar Section
          BlocProvider(
            create: (context) => ProfileAvatarBloc(),
            child: const EditableProfileAvatar(),
          ),
          const SizedBox(height: 16),

          // User Name
          Text(
            user.name,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(height: 4),

          // User Email (Read-only indicator)
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                HugeIcons.strokeRoundedMail01,
                size: 16,
                color: isDarkMode ? Colors.white60 : Colors.grey[600],
              ),
              const SizedBox(width: 8),
              Text(
                user.email,
                style: TextStyle(
                  fontSize: 16,
                  color: isDarkMode ? Colors.white60 : Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Personal Information Card with Editable Fields
  Widget _buildPersonalInfoCard(
      BuildContext context, User user, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withOpacity(0.2)
                : Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Title
          Text(
            context.loc.personalInformation,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(height: 16),

          // Name Field (Editable)
          _buildInfoField(
            fieldName: 'fullName',
            context: context,
            icon: HugeIcons.strokeRoundedUser,
            label: context.loc.fullName,
            value: user.name,
            isEditable: false,
            isDarkMode: isDarkMode,
          ),
          const SizedBox(height: 16),

          // Email Field (Editable with validation)
          _buildInfoField(
            fieldName: 'email',
            context: context,
            icon: HugeIcons.strokeRoundedMail01,
            label: context.loc.email,
            value: user.email,
            isEditable: true,
            isDarkMode: isDarkMode,
            validator: _validateEmail,
          ),
          const SizedBox(height: 16),

          // Phone Field (Editable)
          _buildInfoField(
            fieldName: 'phone',
            context: context,
            icon: HugeIcons.strokeRoundedCall,
            label: context.loc.phone,
            value: user.phone,
            isEditable: false,
            isDarkMode: isDarkMode,
          ),

          const SizedBox(height: 20),

          // // Update Profile Button
          SizedBox(
            width: double.infinity,
            child: BlocBuilder<ProfileBloc, ProfileState>(
              builder: (context, state) {
                return ElevatedButton(
                  onPressed: state.status.isSubmissionInProgress
                      ? null
                      : () {
                          // Handle profile update
                          context
                              .read<ProfileBloc>()
                              .add(const ProfileFormSubmit());
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: state.status.isSubmissionInProgress
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(
                          context.loc.updateProfile,
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                          ),
                        ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // Account Actions Card
  Widget _buildAccountActionsCard(BuildContext context, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withOpacity(0.2)
                : Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildMenuItem(
            context,
            icon: HugeIcons.strokeRoundedLockPassword,
            title: context.loc.changePassword,
            subtitle: context.loc.updateYourPassword,
            onTap: () {
              Navigator.of(context).push(ChangePasswordScreen.pageRoute());
            },
            isDarkMode: isDarkMode,
          ),
        ],
      ),
    );
  }

  // Danger Zone Card
  Widget _buildDangerZoneCard(BuildContext context, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withOpacity(0.2)
                : Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          BlocProvider(
            create: (context) => AccountDeletionBloc(
              authRepository: context.read<AuthRepository>(),
            ),
            child: _buildMenuItem(
              context,
              icon: HugeIcons.strokeRoundedDelete02,
              title: context.loc.deleteAccount,
              subtitle: context.loc.permanentlyDeleteYourAccount,
              onTap: () => _showDeleteAccountDialog(context),
              isDarkMode: isDarkMode,
              isDestructive: true,
            ),
          ),
        ],
      ),
    );
  }

  // Email validation function
  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return context.loc.emailIsRequired;
    }

    // Basic email regex pattern
    final emailRegex =
        RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');

    if (!emailRegex.hasMatch(value)) {
      return context.loc.emailIsInvalid;
    }

    return null; // Valid email
  }

  // Helper method to build info fields
  Widget _buildInfoField({
    required String fieldName,
    required BuildContext context,
    required IconData icon,
    required String label,
    required String value,
    required bool isEditable,
    required bool isDarkMode,
    String? Function(String?)? validator,
  }) {
    return BlocBuilder<ProfileBloc, ProfileState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: isDarkMode ? Colors.white70 : Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                color: isEditable
                    ? (isDarkMode ? const Color(0xFF2A2A2A) : Colors.grey[50])
                    : (isDarkMode ? Colors.grey[800] : Colors.grey[100]),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isEditable
                      ? (isDarkMode ? Colors.grey[700]! : Colors.grey[300]!)
                      : (isDarkMode ? Colors.grey[600]! : Colors.grey[200]!),
                ),
              ),
              child: TextFormField(
                initialValue: value,
                enabled: isEditable,
                validator: validator,
                style: TextStyle(
                  color: isEditable
                      ? (isDarkMode ? Colors.white : Colors.black87)
                      : (isDarkMode ? Colors.grey[500] : Colors.grey[600]),
                  fontSize: 16,
                ),
                decoration: InputDecoration(
                  prefixIcon: Icon(
                    icon,
                    color: isEditable
                        ? AppColors.primaryColor
                        : (isDarkMode ? Colors.grey[500] : Colors.grey[400]),
                    size: 20,
                  ),
                  border: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  disabledBorder: InputBorder.none,
                  errorBorder: InputBorder.none,
                  focusedErrorBorder: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                  hintStyle: TextStyle(
                    color: isDarkMode ? Colors.grey[500] : Colors.grey[400],
                  ),
                ),
                onChanged: isEditable
                    ? (value) {
                        // Update the appropriate field based on fieldName
                        ProfileForm updatedForm;

                        switch (fieldName) {
                          // case 'fullName':
                          //   updatedForm = state.form.copyWith(
                          //     fullNameInput: FullNameInput.dirty(value),
                          //   );
                          //   break;
                          case 'email':
                            updatedForm = state.form.copyWith(
                              emailInput: EmailInput.dirty(value),
                            );
                            break;
                          default:
                            updatedForm = state.form;
                        }

                        context.read<ProfileBloc>().add(
                              ProfileFormChanged(form: updatedForm),
                            );
                      }
                    : null,
              ),
            ),
            if (!isEditable)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Row(
                  children: [
                    Icon(
                      HugeIcons.strokeRoundedLockPassword,
                      size: 12,
                      color: isDarkMode ? Colors.grey[500] : Colors.grey[400],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'This field cannot be changed',
                      style: TextStyle(
                        fontSize: 12,
                        color: isDarkMode ? Colors.grey[500] : Colors.grey[400],
                      ),
                    ),
                  ],
                ),
              ),
          ],
        );
      },
    );
  }

  // Helper method to build menu items (similar to account screen)
  Widget _buildMenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required bool isDarkMode,
    Widget? trailing,
    bool isDestructive = false,
  }) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
      visualDensity: const VisualDensity(
        vertical: -2,
        horizontal: -2,
      ),
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: isDestructive
              ? Colors.red.withOpacity(0.1)
              : AppColors.primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          size: 20,
          color: isDestructive ? Colors.red : AppColors.primaryColor,
        ),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 15,
          color: isDestructive
              ? Colors.red
              : (isDarkMode ? Colors.white : Colors.black87),
        ),
      ),
      subtitle: Padding(
        padding: const EdgeInsets.only(top: 2.0),
        child: Text(
          subtitle,
          style: TextStyle(
            fontSize: 14,
            color: isDarkMode ? Colors.white60 : Colors.grey[600],
          ),
        ),
      ),
      trailing: trailing ??
          Icon(
            HugeIcons.strokeRoundedArrowRight02,
            size: 20,
            color: isDarkMode ? Colors.white60 : Colors.grey[400],
          ),
      onTap: onTap,
    );
  }

  // Show delete account confirmation dialog
  void _showDeleteAccountDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          context.loc.deleteAccount,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.red,
          ),
        ),
        content: Text(
          'Are you sure you want to permanently delete your account? This action cannot be undone.',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[600],
          ),
        ),
        actions: [
          Column(
            children: [
              // Delete button (destructive action)
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    context.read<AccountDeletionBloc>().add(
                          const AccountDeleteRequestConfirmed(),
                        );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Delete Account',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              // Cancel button
              SizedBox(
                width: double.infinity,
                child: TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    context.loc.cancel,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class AccountDeleteButton extends StatelessWidget {
  const AccountDeleteButton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final AccountDeletionBloc bloc = context.read<AccountDeletionBloc>();
    return MaterialButton(
      onPressed: () {
        showDialog(
            context: context,
            builder: (_) {
              return BlocProvider<AccountDeletionBloc>.value(
                value: bloc,
                child: BlocListener<AccountDeletionBloc, AccountDeletionState>(
                  bloc: bloc,
                  listener: (context, state) {
                    if (state.status == AccountDeletionStatus.success) {
                      Navigator.of(context, rootNavigator: true).pop();
                      showSuccessSnackBar(
                          context: context, message: state.message);
                    }

                    if (state.status == AccountDeletionStatus.failure) {
                      Navigator.of(context, rootNavigator: true).pop();
                      showErrorSnackBar(
                          context: context, message: state.message);
                    }
                  },
                  child: AlertDialog(
                    title: const Text("Delete My Account"),
                    content: const Text(
                        'Are you sure you want to delete your account?'),
                    actions: [
                      BlocBuilder<AccountDeletionBloc, AccountDeletionState>(
                        builder: (context, state) {
                          return MaterialButton(
                            onPressed:
                                state.status == AccountDeletionStatus.loading
                                    ? null
                                    : () {
                                        context.read<AccountDeletionBloc>().add(
                                            const AccountDeleteRequestConfirmed());
                                      },
                            textColor: Colors.white,
                            color: Colors.red,
                            visualDensity: const VisualDensity(vertical: -4),
                            elevation: 0,
                            splashColor: Colors.black,
                            child: state.status == AccountDeletionStatus.loading
                                ? const CircularProgressIndicator()
                                : const Text('Confirm'),
                          );
                        },
                      ),
                      const SizedBox(width: 12),
                      BlocBuilder<AccountDeletionBloc, AccountDeletionState>(
                        builder: (context, state) {
                          return TextButton(
                            autofocus: true,
                            onPressed:
                                state.status == AccountDeletionStatus.loading
                                    ? null
                                    : () {
                                        Navigator.of(context).pop(true);
                                      },
                            style: TextButton.styleFrom(
                                foregroundColor: Colors.grey),
                            child: const Text('Cancel'),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              );
            });
      },
      child: Text(
        'Delete My Account'.toUpperCase(),
        style: const TextStyle(
            color: Colors.red, fontSize: 10, fontWeight: FontWeight.normal),
      ),
    );
  }
}
