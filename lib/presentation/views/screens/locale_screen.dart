import 'package:flutter/material.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/helpers/app_settings.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:ionicons/ionicons.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';

import 'onboarding_screen.dart';

class LocaleScreen extends StatelessWidget {
  const LocaleScreen({super.key});

  static const String routeName = '/locale_screen';

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: isDarkMode
                ? [AppColors.darkBodyColor, const Color(0xFF191A2E)]
                : [Colors.white, const Color(0xFFF8F9FA)],
          ),
        ),
        child: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              // Top area with logo
              Expanded(
                flex: 3,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Logo with subtle animation
                    TweenAnimationBuilder<double>(
                      tween: Tween(begin: 0.8, end: 1.0),
                      duration: const Duration(seconds: 1),
                      curve: Curves.easeOutBack,
                      builder: (context, value, child) {
                        return Transform.scale(
                          scale: value,
                          child: Image(
                            image: const AssetImage('assets/images/logo.png'),
                            width: width * 0.25,
                          ),
                        );
                      },
                    ),

                    const SizedBox(height: 24),

                    // Welcome text
                    Text(
                      "Welcome",
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color:
                            isDarkMode ? Colors.white : AppColors.headerColor,
                      ),
                    ),

                    const SizedBox(height: 8),

                    // Subtitle
                    Text(
                      "Select your preferred language",
                      style: TextStyle(
                        fontSize: 16,
                        color: isDarkMode
                            ? Colors.grey.shade400
                            : Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),

              // Language selection area
              Expanded(
                flex: 4,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 40),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _localeButton("English", "English", "en",
                          'assets/images/flags/en.png', context),
                      const SizedBox(height: 20),
                      _localeButton("Arabic", "العربية", "ar",
                          'assets/images/flags/ar.png', context),
                    ],
                  ),
                ),
              ),

              // Bottom area with additional info
              Padding(
                padding: const EdgeInsets.only(bottom: 32),
                child: FutureBuilder<PackageInfo>(
                    future: PackageInfo.fromPlatform(),
                    initialData: null,
                    builder: (BuildContext context, AsyncSnapshot snapshot) {
                      if (snapshot.hasData == true &&
                          snapshot.connectionState == ConnectionState.done) {
                        return Text(
                          "Golden Prizma ${snapshot.data.version}",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 12,
                            color: isDarkMode
                                ? Colors.grey.shade600
                                : Colors.grey.shade500,
                          ),
                        );
                      }
                      return const Text("");
                    }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _localeButton(String languageName, String localeName,
      String localeCode, String image, BuildContext context) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    return Container(
      width: double.infinity,
      height: 72,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isDarkMode
              ? [AppColors.boxDarkColor, const Color(0xFF252742)]
              : [Colors.white, Colors.grey.shade50],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withOpacity(0.2)
                : Colors.grey.withOpacity(0.15),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          splashColor: AppColors.primaryColor.withOpacity(0.1),
          highlightColor: AppColors.primaryColor.withOpacity(0.05),
          onTap: () async {
            // Add a small delay for better user feedback
            await Future.delayed(const Duration(milliseconds: 150));
            if (context.mounted) {
              await AppSettings.setLocale(localeCode, context);
              // ignore: use_build_context_synchronously
              Navigator.of(context).pushNamed(OnboardingScreen.routeName);
            }
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                // Flag with circular border
                Container(
                  width: 44,
                  height: 44,
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isDarkMode
                          ? Colors.grey.shade700
                          : Colors.grey.shade300,
                      width: 1.5,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(21),
                    child: Image.asset(
                      image,
                      fit: BoxFit.cover,
                      width: 40,
                      height: 40,
                    ),
                  ),
                ),

                const SizedBox(width: 16),

                // Language information
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        languageName,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color:
                              isDarkMode ? Colors.white : AppColors.headerColor,
                        ),
                      ),
                      Text(
                        localeName,
                        style: TextStyle(
                          fontSize: 14,
                          color: isDarkMode
                              ? Colors.grey.shade400
                              : Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),

                // Arrow icon
                Icon(
                  Ionicons.chevron_forward,
                  color:
                      isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
