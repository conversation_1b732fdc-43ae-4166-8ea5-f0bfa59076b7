import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:goldenprizma/domain/auth/bloc/authentication_bloc.dart';
import 'package:goldenprizma/domain/auth/models/user.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/helpers/app_settings.dart';
import 'package:goldenprizma/helpers/page_urls.dart';
import 'package:goldenprizma/presentation/modals/locale_selector.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/screens/check_auth_screen.dart';
import 'package:goldenprizma/presentation/views/screens/contact_screen.dart';
import 'package:goldenprizma/presentation/views/screens/profile_screen.dart';
import 'package:goldenprizma/presentation/views/screens/request_deliveries_screen.dart';
import 'package:goldenprizma/presentation/views/screens/support_screen.dart';
import 'package:goldenprizma/presentation/views/screens/website_launcher.dart';
import 'package:ionicons/ionicons.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppDrawer extends StatelessWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          Expanded(
            child: ListView(
              // Important: Remove any padding from the ListView.
              padding: EdgeInsets.zero,
              children: [
                // _userSection(context, user),
                const UserSection(),
                ..._dawerList(context),
              ],
            ),
          ),
          const Divider(),
          _localeRow(context),
          Container(
            child: _themeRow(context),
          ),
          const SafeArea(
            child: Center(
              child: AppVersionWidget(),
            ),
          )
        ],
      ),
    );
  }

  Widget _themeRow(context) {
    final theme = Provider.of<AppProvider>(context).currentTheme;
    return ListTile(
      title: Text(AppLocalizations.of(context)!.theme),
      dense: true,
      trailing: IconButton(
        icon: theme == ThemeMode.dark
            ? const Icon(Ionicons.sunny)
            : const Icon(Ionicons.moon),
        onPressed: null,
      ),
      onTap: () async {
        final currentTheme = theme == ThemeMode.dark ? 'light' : 'dark';
        Provider.of<AppProvider>(context, listen: false).setTheme(currentTheme);
        SharedPreferences pref = await SharedPreferences.getInstance();
        pref.setString('theme', currentTheme);
      },
    );
  }

  Widget _localeRow(context) {
    return ListTile(
      title: Text(
        AppLocalizations.of(context)!.language,
      ),
      dense: true,
      trailing: IconButton(
        icon: Image.asset(
          Provider.of<AppProvider>(context, listen: false).getCurrentFlag(),
          scale: 2,
        ),
        onPressed: null,
      ),
      onTap: () {
        showMaterialModalBottomSheet(
          expand: false,
          context: context,
          backgroundColor: Colors.transparent,
          builder: (context) => const LocaleSelectorModal(),
        );
      },
    );
  }

  _dawerList(context) {
    return [
      // ListTile(
      //   leading: const Icon(
      //     Ionicons.card,
      //   ),
      //   title: Text(AppLocalizations.of(context)!.balance),
      //   onTap: () {
      //     Navigator.of(context).pop();
      //     Navigator.of(context).push(MaterialPageRoute(builder: (context) {
      //       return MultiBlocProvider(
      //         providers: [
      //           BlocProvider(
      //             create: (_) => AccountBloc(
      //               accountingRepostory: AccountingRepostory(),
      //             )..add(AccountRequestLoad()),
      //           ),
      //           BlocProvider(
      //             create: (context) => TransactionFilterBloc(),
      //           ),
      //           BlocProvider(
      //             create: (context) => TransactionBloc(
      //               accountingRepostory: AccountingRepostory(),
      //               transactionFilterBloc:
      //                   BlocProvider.of<TransactionFilterBloc>(context),
      //             )..add(TransactionRequestLoad()),
      //           ),
      //         ],
      //         child: const AccountScreen(),
      //       );
      //     }));
      //   },
      // ),
      ListTile(
        leading: const Icon(
          Ionicons.car,
        ),
        title: Text(AppLocalizations.of(context)!.requestDelivery),
        onTap: () {
          Navigator.of(context).pop();
          Navigator.of(context).push(RequestDeliveriesScreen.pageRoute());
        },
      ),
      // ListTile(
      //   leading: Icon(Ionicons.gift, color: Theme.of(context).disabledColor),
      //   title: Text(AppLocalizations.of(context)!.copounsAndGifts,
      //       style: TextStyle(color: Theme.of(context).disabledColor)),
      //   onTap: null,
      // ),
      ListTile(
        leading: const Icon(
          Ionicons.chatbubble,
        ),
        title: Text(AppLocalizations.of(context)!.support),
        onTap: () {
          Navigator.of(context).pop();
          Navigator.of(context).push(SupportScreen.pageRoute());
        },
      ),
      ListTile(
        leading: const Icon(
          Ionicons.help_circle,
        ),
        title: Text(AppLocalizations.of(context)!.faq),
        onTap: () async {
          String local = await AppSettings.getLocale();
          String url = PageUrls.faqs + local;
          Navigator.of(context).push(MaterialPageRoute(
            builder: (context) {
              return WebsiteLauncher(
                url: url,
                websiteName: 'Faq',
                minimalDesign: true,
              );
            },
          ));
        },
      ),
      ListTile(
        leading: const Icon(
          Ionicons.information_circle,
        ),
        title: Text(AppLocalizations.of(context)!.aboutUs),
        onTap: () async {
          String local = await AppSettings.getLocale();
          String url = PageUrls.about + local;
          Navigator.of(context).push(MaterialPageRoute(
            builder: (context) {
              return WebsiteLauncher(
                url: url,
                websiteName: 'About us',
                minimalDesign: true,
              );
            },
          ));
        },
      ),
      ListTile(
        leading: const Icon(
          Ionicons.send,
        ),
        title: Text(AppLocalizations.of(context)!.conteactUs),
        onTap: () {
          Navigator.of(context).pop();
          Navigator.of(context).push(ContactScreen.pageRoute());
        },
      ),
      BlocBuilder<AuthenticationBloc, AuthenticationState>(
        builder: (context, state) {
          return ListTile(
            leading: const Icon(
              Ionicons.log_out,
            ),
            title: Text(AppLocalizations.of(context)!.logout),
            onTap: () {
              Navigator.of(context).pop();
              showDialog(
                  context: context,
                  builder: (context) {
                    return AlertDialog(
                      content: Text(context.loc.logoutConfirmation),
                      actions: [
                        TextButton(
                            onPressed: () {
                              context
                                  .read<AuthenticationBloc>()
                                  .add(AuthenticationLogoutRequested());
                              Navigator.of(context)
                                  .pushNamed(CheckAuthScreen.routeName);
                            },
                            child: Text(context.loc.logout)),
                        TextButton(
                            onPressed: () {
                              Navigator.of(context, rootNavigator: true).pop();
                            },
                            child: Text(context.loc.cancel)),
                      ],
                    );
                  });
            },
          );
        },
      ),
    ];
  }
}

class UserSection extends StatelessWidget {
  const UserSection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthenticationBloc, AuthenticationState>(
      builder: (context, state) {
        final User user = state.user;
        return DrawerHeader(
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [AppColors.secondaryColor, AppColors.secondaryColor],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                      Navigator.of(context).pushNamed(ProfileScreen.routeName);
                    },
                    child: CircleAvatar(
                      backgroundImage: NetworkImage(user.profilePhotoUrl),
                      radius: 32,
                    ),
                  ),
                  const SizedBox(width: 16),
                  SizedBox(
                    width: 160,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user.name.toString(),
                          overflow: TextOverflow.fade,
                          maxLines: 1,
                          softWrap: false,
                          style: const TextStyle(
                              fontWeight: FontWeight.bold, color: Colors.white),
                        ),
                        const SizedBox(height: 6),
                        Text(
                          '${user.phoneCode!} ${user.phone}',
                          style: const TextStyle(
                              fontSize: 12, color: Colors.white70),
                        ),
                        const SizedBox(height: 6),
                        Text(
                          user.cid,
                          style: const TextStyle(
                              fontSize: 12, color: Colors.white70),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              GestureDetector(
                onTap: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pushNamed(ProfileScreen.routeName);
                },
                // style: TextButton.styleFrom(padding: EdgeInsets.zero),
                child: Text(
                  context.loc.editProfile,
                  style: const TextStyle(fontSize: 11, color: Colors.white70),
                ),
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 3, horizontal: 6),
                      decoration: BoxDecoration(
                        color: user.customerTypeColor,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        user.customerType.toUpperCase().trim(),
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                    Text(
                      user.formattedBalance,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        );
      },
    );
  }
}

class AppVersionWidget extends StatelessWidget {
  const AppVersionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 50,
      child: Center(
        child: FutureBuilder<PackageInfo>(
          future: PackageInfo.fromPlatform(),
          initialData: null,
          builder: (BuildContext context, AsyncSnapshot snapshot) {
            if (snapshot.hasData == true &&
                snapshot.connectionState == ConnectionState.done) {
              return Text(
                  AppLocalizations.of(context)!
                      .version("${snapshot.data.version ?? ""}"),
                  style: Theme.of(context).textTheme.bodySmall);
            }
            return const Text("");
          },
        ),
      ),
    );
  }
}
