import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:goldenprizma/helpers/logger.dart';

bool onScroll(
    {required ScrollNotification notification,
    required ScrollController scrollController,
    required Function onLoad,
    required int total,
    bool isReveres = false,
    bool isLoading = false,
    bool isNotMatch = false,
    required int perPage,
    required int page}) {
  logger(" ${page * perPage}  $total $perPage $page");
  try {
    if (((page - 1) * perPage) >= total) {
      return false;
    }

    if (notification is ScrollUpdateNotification &&
        scrollController.position.userScrollDirection ==
            ScrollDirection.reverse) {
      final double remaninedOffset =
          scrollController.position.maxScrollExtent - scrollController.offset;
      if (remaninedOffset <= 3000) {
        logger("remaninedOffset $remaninedOffset");

        if ((isNotMatch && !isLoading)) {
          onLoad();
        }
      }
    }
    return true;
  } catch (e) {
    logger("error in onScroll $e");
    return false;
  }
}
