import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/ads/bloc/advertisement_bloc.dart';
import 'package:goldenprizma/domain/ads/bloc/slides_bloc.dart';
import 'package:goldenprizma/presentation/widgets/advertisement_list_view.dart';
import 'package:goldenprizma/presentation/widgets/slider_widget.dart';

class HomePage extends StatefulWidget {
  final VoidCallback? onViewOrders;

  const HomePage({super.key, this.onViewOrders});

  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  void initState() {
    super.initState();
    if (mounted) {
      if (context.read<SlidesBloc>().state.status == SlidesStatus.initial) {
        context.read<SlidesBloc>().add(SlidesRequestLoad());
      }

      if (context.read<AdvertisementBloc>().state.status ==
          AdvertisementStatus.initial) {
        context.read<AdvertisementBloc>().add(AdvertisementRequestLoad());
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return const Column(children: [
      SlideWidget(),
      AdvertisementListView(),
    ]);
  }
}
