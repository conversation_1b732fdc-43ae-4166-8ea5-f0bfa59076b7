import 'dart:async';

import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:goldenprizma/domain/orders/bloc/order_approval_bloc.dart';
import 'package:goldenprizma/domain/orders/bloc/order_bloc.dart';
import 'package:goldenprizma/domain/orders/models/order.dart';
import 'package:goldenprizma/domain/orders/models/order_query.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/presentation/components/order_card.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/screens/home_screen.dart';
import 'package:goldenprizma/presentation/widgets/empty_state.dart';
import 'package:goldenprizma/presentation/widgets/scroll_loader.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';

class MyOrdersPage extends StatefulWidget {
  final String? initialStatus;

  const MyOrdersPage({super.key, this.initialStatus});

  @override
  _MyOrdersPageState createState() => _MyOrdersPageState();
}

class _MyOrdersPageState extends State<MyOrdersPage> {
  final ScrollController _scrollController = ScrollController();
  final ScrollController _tabScrollController = ScrollController();

  Timer? _throttleTimer;

  void _throttledListener() {
    // Check if we're close to the bottom of the list
    final double remainingOffset =
        _scrollController.position.maxScrollExtent - _scrollController.offset;

    // Only trigger if we're close to the bottom, and the timer isn't already running
    if (_scrollController.position.atEdge &&
        _scrollController.position.pixels != 0 &&
        remainingOffset <= 250) {
      // If a timer is already running, do not trigger again
      if (_throttleTimer?.isActive ?? false) {
        return;
      }

      // Start a timer that triggers the function only once every 300ms (throttle duration)
      _throttleTimer = Timer(const Duration(milliseconds: 100), () {
        // Dispatch the event (load more data)
        context.read<OrderBloc>().add(const OrderRequestLoad());
      });
    }
  }

  @override
  void initState() {
    super.initState();
    // Load notification when home screen is mounted and user is logged in
    if (mounted &&
        context.read<OrderBloc>().state.status == OrderStatus.initial) {
      context
          .read<OrderBloc>()
          .add(OrderRequestLoad(status: widget.initialStatus));
    } else {
      context.read<OrderBloc>().add(const OrderReload(query: OrderQuery()));
    }
  }

  @override
  Widget build(BuildContext context) {
    var statuses = _statuses(context);

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: Column(
        children: [
          Container(
            margin: const EdgeInsets.symmetric(vertical: 6),
            width: MediaQuery.of(context).size.width,
            height: 46,
            child: BlocBuilder<OrderBloc, OrderState>(
              builder: (context, state) {
                return ListView.separated(
                  scrollDirection: Axis.horizontal,
                  itemCount: statuses.length,
                  itemBuilder: (_, index) {
                    String status = statuses[index]['label'];
                    String? statusValue = statuses[index]['value'];

                    bool isSelected =
                        state.query.statuses.contains(statusValue);
                    if (state.query.statuses.isEmpty &&
                        statusValue == null &&
                        index == 0) {
                      isSelected = true;
                    }

                    final Widget chip = ChoiceChip(
                      label: Text(status),
                      selectedColor: AppColors.primaryColor,
                      visualDensity: const VisualDensity(vertical: -1),
                      backgroundColor:
                          Theme.of(context).appBarTheme.backgroundColor,
                      labelStyle: TextStyle(
                          fontSize: 13,
                          fontWeight:
                              isSelected ? FontWeight.w600 : FontWeight.normal,
                          color: isSelected
                              ? Colors.white
                              : Theme.of(context).textTheme.bodyLarge!.color),
                      shadowColor: Colors.black,
                      elevation: isSelected ? 1 : 0.3,
                      pressElevation: 2,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 2),
                      shape: RoundedRectangleBorder(
                          side: BorderSide(
                            color: isSelected
                                ? Colors.transparent
                                : Theme.of(context).dividerColor,
                            width: 0.8,
                          ),
                          borderRadius: BorderRadius.circular(12)),
                      selected: isSelected,
                      onSelected: (value) {
                        if (value && statusValue == null) {
                          context.read<OrderBloc>().add(OrderQueryChanged(
                                query: state.query.copyWith(statuses: const []),
                              ));
                        } else {
                          context.read<OrderBloc>().add(OrderQueryChanged(
                              query: state.query
                                  .copyWith(statuses: ["$statusValue"])));
                        }
                      },
                    );

                    if (index == 0) {
                      return Padding(
                        padding: const EdgeInsetsDirectional.only(start: 16),
                        child: chip,
                      );
                    }
                    if (index == statuses.length - 1) {
                      return Padding(
                        padding: const EdgeInsetsDirectional.only(end: 16),
                        child: chip,
                      );
                    }
                    return chip;
                  },
                  separatorBuilder: (context, index) =>
                      const SizedBox(width: 8),
                );
              },
            ),
          ),
          Expanded(
            child: Container(
              color: Theme.of(context).cardColor,
              padding: const EdgeInsets.only(top: 12.0),
              child: BlocBuilder<OrderBloc, OrderState>(
                builder: (context, state) {
                  if (state.status == OrderStatus.initial) {
                    return const OrderLoadingUI();
                  }

                  if (state.status == OrderStatus.failure) {
                    return _buildFailedUI(context);
                  }

                  if (state.status == OrderStatus.success &&
                      state.hasFetched &&
                      state.totalOrders <= 0 &&
                      state.isQueryEmpty()) {
                    return const EmptyTotalOrders();
                  }

                  if (state.status == OrderStatus.success &&
                      state.orders.isEmpty &&
                      state.hasFetched &&
                      !state.isQueryEmpty()) {
                    return const EmptyOrders();
                  }

                  return RefreshIndicator(
                    onRefresh: () async {
                      context
                          .read<OrderBloc>()
                          .add(const OrderReload(query: OrderQuery(page: 1)));
                    },
                    child: ListView.separated(
                      scrollDirection: Axis.vertical,
                      controller: _scrollController
                        ..addListener(_throttledListener),
                      itemBuilder: (context, index) {
                        if (index < state.orders.length) {
                          Order order = state.orders[index];
                          return BlocProvider(
                            create: (context) => OrderApprovalBloc(),
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 4),
                              color: Theme.of(context).cardColor,
                              child: OrderCard(
                                image: order.image,
                                status: order.status,
                                size: order.size,
                                quantity: order.quantity,
                                price: order.totalPrice,
                                id: order.id,
                                showAcceptReject: order.showAcceptReject,
                                shippingPrice: order.shippingPriceRaw,
                                showPrice: order.showPrice,
                                hasStatusReason: order.canViewReason(),
                                rejectionReasons: order.rejectionReasons,
                                canConfirmAgain: order.canConfirmAgain(),
                              ),
                            ),
                          );
                        } else {
                          Timer(const Duration(milliseconds: 0), () {
                            _scrollController.jumpTo(
                                _scrollController.position.maxScrollExtent -
                                    10);
                          });
                          return scrollLoaderIndicator();
                        }
                      },
                      separatorBuilder: (context, _) =>
                          const Divider(height: 1),
                      itemCount: state.orders.length +
                          (state.status == OrderStatus.loading ? 1 : 0),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  List _statuses(BuildContext context) {
    return OrderQuery.statusList(context);
  }

  Center _buildFailedUI(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            AppLocalizations.of(context)!.generalErrorMessage,
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(
            height: 16,
          ),
          MaterialButton(
            color: AppColors.primaryColor,
            elevation: 0,
            onPressed: () {
              context.read<OrderBloc>().add(const OrderRequestLoad());
            },
            child: Text(context.loc.tryAgain),
          )
        ],
      ),
    );
  }

  @override
  void dispose() {
    _scrollController.removeListener(_throttledListener);
    EasyDebounce.cancel('scroll-listener');
    EasyDebounce.cancel('search');
    _scrollController.dispose();
    _tabScrollController.dispose();
    super.dispose();
  }
}

class SearchBox extends StatelessWidget {
  final TextEditingController _searchController = TextEditingController();

  SearchBox({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrderBloc, OrderState>(
      builder: (context, state) {
        return Container(
          color: Theme.of(context).cardColor,
          padding: const EdgeInsets.only(top: 12, left: 16, right: 16),
          child: SizedBox(
            height: 46,
            width: double.infinity,
            child: TextField(
              controller: _searchController,
              autofocus: false,
              decoration: InputDecoration(
                fillColor: Provider.of<AppProvider>(context).isDarkMode(context)
                    ? Theme.of(context).colorScheme.surface
                    : Theme.of(context).inputDecorationTheme.fillColor,
                contentPadding:
                    const EdgeInsetsDirectional.only(start: 0, end: 56),
                isDense: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(0.0),
                ),
                prefixIcon: const Icon(
                  Icons.search,
                  color: Colors.grey,
                  size: 24,
                ),
                hintText: context.loc.search,
              ),
              onChanged: (value) {
                EasyDebounce.debounce(
                    'search', const Duration(milliseconds: 500), () {
                  context.read<OrderBloc>().add(
                        OrderSearchQueryChanged(search: value),
                      );
                });
              },
            ),
          ),
        );
      },
    );
  }
}

class EmptyTotalOrders extends StatelessWidget {
  const EmptyTotalOrders({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final height = MediaQuery.of(context).size.height;
    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(height: height * 0.16),
          SvgPicture.asset(
            'assets/images/svg/empty-cart.svg',
            width: 250,
          ),
          Text(
            context.loc.noTotalOrders,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          MaterialButton(
            onPressed: () {
              Navigator.of(context).push(MaterialPageRoute(builder: (context) {
                return const HomeScreen(initialPage: 1); // StorePage
              }));
            },
            elevation: 0,
            color: AppColors.primaryColor,
            textColor: Colors.white,
            visualDensity: const VisualDensity(vertical: -2),
            child: Text(context.loc.letsOrder),
          )
        ],
      ),
    );
  }
}

class EmptyOrders extends StatelessWidget {
  const EmptyOrders({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final height = size.height;
    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(height: height * 0.22),
          EmptyState(width: height * 0.20),
          const SizedBox(height: 24),
          Text(
            context.loc.noOrders,
            style: Theme.of(context).textTheme.titleLarge,
          ),
        ],
      ),
    );
  }
}

class OrderLoadingUI extends StatelessWidget {
  const OrderLoadingUI({super.key});

  @override
  Widget build(BuildContext context) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    return Shimmer.fromColors(
      baseColor: isDarkMode ? AppColors.boxDarkColor : Colors.grey.shade300,
      highlightColor: isDarkMode ? Colors.black45 : Colors.grey.shade100,
      enabled: true,
      child: ListView.separated(
        itemBuilder: (_, __) => Container(
          height: 120,
          color: Colors.white,
        ),
        itemCount: 10,
        separatorBuilder: (BuildContext context, int index) =>
            const SizedBox(height: 4),
      ),
    );
  }
}
