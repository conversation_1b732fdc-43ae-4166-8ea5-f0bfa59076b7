import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/ads/bloc/advertisement_bloc.dart';
import 'package:goldenprizma/domain/auth/bloc/authentication_bloc.dart';
import 'package:goldenprizma/domain/auth/models/user.dart';
import 'package:goldenprizma/domain/home/<USER>/home_bloc.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/screens/help_screen.dart';
import 'package:goldenprizma/presentation/views/screens/paste_link_input_screen.dart';
import 'package:goldenprizma/presentation/widgets/simple_advertisement_list.dart';
import 'package:goldenprizma/presentation/widgets/quick_action_card.dart';
import 'package:goldenprizma/presentation/widgets/home_slide_widget.dart';
import 'package:goldenprizma/presentation/widgets/exchange_rate_marquee.dart';
import 'package:hugeicons/hugeicons.dart';

class ImprovedHomePage extends StatefulWidget {
  final VoidCallback? onViewOrders;
  final VoidCallback? onViewWebsites;

  const ImprovedHomePage({
    super.key,
    this.onViewOrders,
    this.onViewWebsites,
  });

  @override
  _ImprovedHomePageState createState() => _ImprovedHomePageState();
}

class _ImprovedHomePageState extends State<ImprovedHomePage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    if (mounted) {
      context.read<HomeBloc>().add(const HomeDataRequested());

      // Keep existing advertisement loading
      if (context.read<AdvertisementBloc>().state.status ==
          AdvertisementStatus.initial) {
        context.read<AdvertisementBloc>().add(AdvertisementRequestLoad());
      }
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      controller: _scrollController,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const ExchangeRateMarquee(),
          _buildWelcomeSection(),
          _buildQuickActions(),
          const HomeSlideWidget(),
          // const TopBrandsList(),
          _buildSectionTitle(context.loc.featuredItems),
          SimpleAdvertisementList(parentScrollController: _scrollController),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return BlocBuilder<AuthenticationBloc, AuthenticationState>(
      builder: (context, state) {
        final User user = state.user;
        final bool isAuthenticated = state.isAuthenticated;

        return Container(
          padding: const EdgeInsets.fromLTRB(16, 10, 16, 4),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                isAuthenticated
                    ? '${context.loc.hello}, ${user.name}!'
                    : context.loc.welcome,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildQuickActions() {
    return Padding(
      padding: const EdgeInsets.only(top: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            height: 96,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                QuickActionCard(
                  icon: HugeIcons.strokeRoundedShoppingBagAdd,
                  title: context.loc.newOrder,
                  color: AppColors.primaryColor,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const PasteLinkInputScreen(),
                      ),
                    );
                  },
                ),
                QuickActionCard(
                  icon: HugeIcons.strokeRoundedTask01,
                  title: context.loc.myOrders,
                  color: Colors.orange,
                  onTap: widget.onViewOrders,
                ),
                QuickActionCard(
                  icon: HugeIcons.strokeRoundedGlobe02,
                  title: context.loc.websites,
                  color: Colors.blue,
                  onTap: widget.onViewWebsites,
                ),
                QuickActionCard(
                  icon: Icons.help_outline,
                  title: context.loc.help,
                  color: Colors.green,
                  onTap: () {
                    Navigator.of(context).push(HelpScreen.pageRoute());
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
