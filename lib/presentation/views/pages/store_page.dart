import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:async';

// Store components
import 'package:goldenprizma/domain/store/bloc/brands_bloc.dart';
import 'package:goldenprizma/domain/store/bloc/categories_bloc.dart';
import 'package:goldenprizma/domain/store/bloc/products_bloc.dart';
import 'package:goldenprizma/domain/store/bloc/home_bloc.dart';
import 'package:goldenprizma/domain/store/cubit/store_filter_cubit.dart';
import 'package:goldenprizma/domain/store/models/brand.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/presentation/widgets/shimmer_loading.dart';
import 'store/widgets/banner_carousel.dart';
import 'store/widgets/brands_section.dart';
import 'store/widgets/search_and_categories.dart';
import 'store/widgets/products_grid.dart';
import 'store/widgets/brands_bottom_sheet.dart';
import 'store/widgets/filter_bottom_sheet.dart';
import 'store/widgets/sticky_header_delegate.dart';

class StorePage extends StatefulWidget {
  const StorePage({super.key});

  @override
  _StorePageState createState() => _StorePageState();
}

class _StorePageState extends State<StorePage>
    with SingleTickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  Timer? _debounceTimer;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // Initialize fade animation controller
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    // Start with carousel fully visible (animation at 0.0)
    _fadeController.reset(); // Load initial data
    context.read<StoreHomeBloc>().add(const StoreHomeRequestLoad());

    final filterState = context.read<StoreFilterCubit>().state;

    context.read<ProductsBloc>().add(ProductsRequestLoad(
          search: '',
          brandId: filterState.selectedBrandId,
          categoryId: filterState.selectedCategoryId,
          isOutlet: context.read<StoreFilterCubit>().getOutletFilterValue(),
          hasPromotion:
              context.read<StoreFilterCubit>().getPromotionFilterValue(),
        ));

    // Listen to search changes
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _fadeController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onScroll() {
    // Smooth fade carousel based on scroll position
    final scrollOffset = _scrollController.offset;
    const fadeStartOffset = 0.0; // Start fading immediately
    const fadeEndOffset = 100.0; // Complete fade at 100px

    // Calculate fade progress (0.0 = fully visible, 1.0 = fully hidden)
    double fadeProgress =
        (scrollOffset - fadeStartOffset) / (fadeEndOffset - fadeStartOffset);
    fadeProgress = fadeProgress.clamp(0.0, 1.0);

    // Animate to the calculated position
    _fadeController.animateTo(fadeProgress, duration: Duration.zero);

    // Simple pagination check on every scroll
    _simplePaginationCheck();
  }

  void _simplePaginationCheck() {
    if (!_scrollController.hasClients) return;

    final position = _scrollController.position;
    final pixels = position.pixels;
    final maxScrollExtent = position.maxScrollExtent;

    // Simple calculation: trigger when 450px from bottom
    if (pixels >= maxScrollExtent - 450) {
      _loadMoreIfNeeded();
    }
  }

  void _loadMoreIfNeeded() {
    final productsState = context.read<ProductsBloc>().state;
    final filterState = context.read<StoreFilterCubit>().state;

    // Only load if not already loading and has more data
    if (productsState.status != ProductsStatus.loadingMore &&
        productsState.status != ProductsStatus.loading &&
        !productsState.hasReachedMax &&
        !_isLoadingMore) {
      _isLoadingMore = true;

      context.read<ProductsBloc>().add(ProductsLoadMore(
            search: _searchController.text,
            brandId: filterState.selectedBrandId,
            categoryId: filterState.selectedCategoryId,
            isOutlet: context.read<StoreFilterCubit>().getOutletFilterValue(),
            hasPromotion:
                context.read<StoreFilterCubit>().getPromotionFilterValue(),
          ));

      // Reset flag after delay
      Future.delayed(const Duration(milliseconds: 1000), () {
        if (mounted) {
          _isLoadingMore = false;
        }
      });
    }
  }

  void _onSearchChanged() {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      final filterState = context.read<StoreFilterCubit>().state;
      context.read<ProductsBloc>().add(ProductsRequestLoad(
            search: _searchController.text,
            brandId:
                filterState.selectedBrandId, // This will be null after clearing
            categoryId: filterState
                .selectedCategoryId, // This will be null after clearing
            isOutlet: context.read<StoreFilterCubit>().getOutletFilterValue(),
            hasPromotion: context
                .read<StoreFilterCubit>()
                .getPromotionFilterValue(), // This will be null after clearing
          ));
    });
  }

  void _onBrandSelected(int? brandId, [Brand? brand]) {
    // Update filter state using cubit
    context.read<StoreFilterCubit>().selectBrand(brandId, brand);

    // Cancel any pending search debounce
    _debounceTimer?.cancel();

    // Immediately reload products with new filter
    final filterState = context.read<StoreFilterCubit>().state;
    context.read<ProductsBloc>().add(ProductsRequestLoad(
          search: _searchController.text,
          brandId: filterState.selectedBrandId,
          categoryId: filterState.selectedCategoryId,
          isOutlet: context.read<StoreFilterCubit>().getOutletFilterValue(),
          hasPromotion:
              context.read<StoreFilterCubit>().getPromotionFilterValue(),
        ));
  }

  void _onOutletSelected() {
    // Update filter state using cubit
    context.read<StoreFilterCubit>().toggleOutlet();

    // Cancel any pending search debounce
    _debounceTimer?.cancel();

    // Immediately reload products with outlet filter
    final filterState = context.read<StoreFilterCubit>().state;
    context.read<ProductsBloc>().add(ProductsRequestLoad(
          search: _searchController.text,
          brandId: filterState.selectedBrandId,
          categoryId: filterState.selectedCategoryId,
          isOutlet: context.read<StoreFilterCubit>().getOutletFilterValue(),
          hasPromotion:
              context.read<StoreFilterCubit>().getPromotionFilterValue(),
        ));
  }

  void _showBrandsBottomSheet() {
    // Load brands when bottom sheet is opened
    context.read<BrandsBloc>().add(BrandsRequestLoad());

    final filterState = context.read<StoreFilterCubit>().state;
    BrandsBottomSheet.show(
      context: context,
      selectedBrandId: filterState.selectedBrandId,
      onBrandSelected: (brandId, selectedBrand) {
        Navigator.pop(context);
        _onBrandSelected(brandId, selectedBrand);
      },
    );
  }

  void _showFiltersBottomSheet() {
    // Load categories when bottom sheet is opened
    context.read<CategoriesBloc>().add(CategoriesRequestLoad());

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => BlocBuilder<CategoriesBloc, CategoriesState>(
        builder: (context, state) {
          return BlocBuilder<StoreFilterCubit, StoreFilterState>(
            builder: (context, filterState) {
              return FilterBottomSheet(
                categories: state.categories,
                selectedCategoryId: filterState.selectedCategoryId,
                isPromotionSelected: filterState.isPromotionSelected,
                isOutlet:
                    context.read<StoreFilterCubit>().getOutletFilterValue() ??
                        false, // Add this
                onCategorySelected: (categoryId) {
                  // Update filter state using cubit
                  context.read<StoreFilterCubit>().selectCategory(categoryId);

                  final updatedFilterState =
                      context.read<StoreFilterCubit>().state;
                  context.read<ProductsBloc>().add(ProductsRequestLoad(
                        search: _searchController.text,
                        brandId: updatedFilterState.selectedBrandId,
                        categoryId: updatedFilterState.selectedCategoryId,
                        isOutlet: context
                            .read<StoreFilterCubit>()
                            .getOutletFilterValue(),
                        hasPromotion: context
                            .read<StoreFilterCubit>()
                            .getPromotionFilterValue(),
                      ));
                },
                onPromotionToggle: (bool value) {
                  // Update filter state using cubit
                  context.read<StoreFilterCubit>().togglePromotion();

                  final updatedFilterState =
                      context.read<StoreFilterCubit>().state;
                  context.read<ProductsBloc>().add(ProductsRequestLoad(
                        search: _searchController.text,
                        brandId: updatedFilterState.selectedBrandId,
                        categoryId: updatedFilterState.selectedCategoryId,
                        isOutlet: context
                            .read<StoreFilterCubit>()
                            .getOutletFilterValue(),
                        hasPromotion: context
                            .read<StoreFilterCubit>()
                            .getPromotionFilterValue(),
                      ));
                },
                onClearFilters: () {
                  // Clear all filters using cubit
                  context.read<StoreFilterCubit>().clearAllFilters();

                  final updatedFilterState =
                      context.read<StoreFilterCubit>().state;
                  context.read<ProductsBloc>().add(ProductsRequestLoad(
                        search: _searchController.text,
                        brandId: updatedFilterState.selectedBrandId,
                        categoryId: updatedFilterState.selectedCategoryId,
                        isOutlet: context
                            .read<StoreFilterCubit>()
                            .getOutletFilterValue(),
                        hasPromotion: context
                            .read<StoreFilterCubit>()
                            .getPromotionFilterValue(),
                      ));
                },
              );
            },
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ProductsBloc, ProductsState>(
      listener: (context, state) {
        // Reset loading flag when state changes
        if (state.status != ProductsStatus.loadingMore) {
          _isLoadingMore = false;
        }
      },
      child: Scaffold(
        body: CustomScrollView(
          controller: _scrollController,
          slivers: [
            // Banner Carousel (collapsible) - using StoreHomeBloc
            BlocBuilder<StoreHomeBloc, StoreHomeState>(
              builder: (context, state) {
                return BannerCarousel(
                  fadeAnimation: _fadeAnimation,
                  banners: state.homeData?.banners ?? [],
                );
              },
            ),

            // Sticky Header with Brands, Search and Categories
            SliverPersistentHeader(
              pinned: true,
              delegate: StickyHeaderDelegate(
                child: Container(
                  color: Colors.transparent, // Theme-aware background
                  child: Column(
                    children: [
                      // Combined Filters + Brands Section - using StoreHomeBloc and StoreFilterCubit
                      BlocBuilder<StoreHomeBloc, StoreHomeState>(
                        builder: (context, homeState) {
                          if (homeState.status == StoreHomeStatus.loading) {
                            return ShimmerLoading.brands();
                          }

                          if (homeState.status == StoreHomeStatus.failure) {
                            return SizedBox(
                              height: 90,
                              child: Center(
                                  child: Text(context.loc.failedToLoadBrands)),
                            );
                          }

                          return BlocBuilder<StoreFilterCubit,
                              StoreFilterState>(
                            builder: (context, filterState) {
                              final topBrands =
                                  homeState.homeData?.topBrands ?? [];

                              // Create brands list including selected brand if not in top brands
                              List<Brand> displayBrands = topBrands
                                  .map((topBrand) => Brand(
                                        id: topBrand.id,
                                        name: topBrand.name,
                                        isTop: true,
                                        sortOrder: 0,
                                        logoUrl: topBrand.logoUrl,
                                      ))
                                  .toList();

                              // Add selected brand if it's not already in the top brands list
                              if (filterState.selectedBrand != null &&
                                  !displayBrands.any((brand) =>
                                      brand.id ==
                                      filterState.selectedBrand!.id)) {
                                displayBrands.add(filterState.selectedBrand!);
                              }

                              return BrandsSection(
                                brands: displayBrands,
                                selectedBrandId: filterState.selectedBrandId,
                                isOutletSelected: filterState.isOutletSelected,
                                onBrandSelected: _onBrandSelected,
                                onOutletSelected: _onOutletSelected,
                                onViewAllPressed: _showBrandsBottomSheet,
                                scaleAnimation: _scaleAnimation,
                              );
                            },
                          );
                        },
                      ),
                      // Search and Categories - using BLoC and StoreFilterCubit
                      BlocBuilder<CategoriesBloc, CategoriesState>(
                        builder: (context, categoriesState) {
                          return BlocBuilder<StoreFilterCubit,
                              StoreFilterState>(
                            builder: (context, filterState) {
                              return SizedBox(
                                child: SearchAndCategories(
                                  searchController: _searchController,
                                  categories: categoriesState.categories,
                                  selectedCategoryId:
                                      filterState.selectedCategoryId,
                                  isPromotionSelected:
                                      filterState.isPromotionSelected,
                                  onFiltersPressed: _showFiltersBottomSheet,
                                  onSearchChanged:
                                      _onSearchStarted, // Add this callback
                                ),
                              );
                            },
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Products Grid - using BLoC and StoreFilterCubit
            BlocBuilder<ProductsBloc, ProductsState>(
              builder: (context, productsState) {
                if (productsState.status == ProductsStatus.loading &&
                    productsState.products.isEmpty) {
                  return ShimmerLoading.products();
                }

                if (productsState.status == ProductsStatus.failure &&
                    productsState.products.isEmpty) {
                  return SliverFillRemaining(
                    child:
                        Center(child: Text(context.loc.failedToLoadProducts)),
                  );
                }

                return BlocBuilder<StoreFilterCubit, StoreFilterState>(
                  builder: (context, filterState) {
                    return ProductsGrid(
                      products: productsState.products,
                      selectedBrandId: filterState.selectedBrandId,
                      selectedCategoryId: filterState.selectedCategoryId,
                      searchQuery: _searchController.text,
                      hasMore: !productsState.hasReachedMax,
                      isLoadingMore:
                          productsState.status == ProductsStatus.loadingMore,
                    );
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _onSearchStarted() {
    // Clear all filters when user starts searching
    context.read<StoreFilterCubit>().clearAllFilters();

    // Don't trigger immediate search here, let the existing debounce handle it
    // The _onSearchChanged method will be called by the existing listener
  }
}
