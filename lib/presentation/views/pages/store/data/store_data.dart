import '../models/store_models.dart';

class StoreData {
  static List<Ad> get ads => [
        Ad(
          id: '1',
          title: 'Summer Sale',
          subtitle: 'Up to 50% Off',
          imageUrl:
              'https://placehold.co/400x200/FF6B6B/FFFFFF?text=Summer+Sale',
        ),
        Ad(
          id: '2',
          title: 'New Collection',
          subtitle: 'Fresh Arrivals',
          imageUrl:
              'https://placehold.co/400x200/4ECDC4/FFFFFF?text=New+Collection',
        ),
        Ad(
          id: '3',
          title: 'Free Shipping',
          subtitle: 'On orders over \$100',
          imageUrl:
              'https://placehold.co/400x200/45B7D1/FFFFFF?text=Free+Shipping',
        ),
      ];

  static List<Brand> get brands => [
        Brand(
          id: '1',
          name: 'Nike',
          imageUrl: 'https://placehold.co/80x80/000000/FFFFFF?text=N',
        ),
        <PERSON>(
          id: '2',
          name: 'Adidas',
          imageUrl: 'https://placehold.co/80x80/000000/FFFFFF?text=A',
        ),
        <PERSON>(
          id: '3',
          name: '<PERSON>',
          imageUrl: 'https://placehold.co/80x80/000000/FFFFFF?text=🍎',
        ),
        <PERSON>(
          id: '4',
          name: 'Samsung',
          imageUrl: 'https://placehold.co/80x80/1428A0/FFFFFF?text=S',
        ),
        Brand(
          id: '5',
          name: 'Sony',
          imageUrl: 'https://placehold.co/80x80/000000/FFFFFF?text=SONY',
        ),
        Brand(
          id: '6',
          name: 'H&M',
          imageUrl: 'https://placehold.co/80x80/E50000/FFFFFF?text=H%26M',
        ),
      ];

  static List<Category> get categories => [
        Category(
          id: '1',
          name: 'Electronics',
          imageUrl: 'https://placehold.co/80x80/6C5CE7/FFFFFF?text=📱',
        ),
        Category(
          id: '2',
          name: 'Fashion',
          imageUrl: 'https://placehold.co/80x80/A29BFE/FFFFFF?text=👕',
        ),
        Category(
          id: '3',
          name: 'Sports',
          imageUrl: 'https://placehold.co/80x80/FD79A8/FFFFFF?text=⚽',
        ),
        Category(
          id: '4',
          name: 'Home',
          imageUrl: 'https://placehold.co/80x80/FDCB6E/FFFFFF?text=🏠',
        ),
        Category(
          id: '5',
          name: 'Beauty',
          imageUrl: 'https://placehold.co/80x80/E17055/FFFFFF?text=💄',
        ),
      ];

  static List<Product> get products => [
        Product(
          id: '1',
          name:
              'CeraVe Cleansers Cleansing Micellar Water with Moisturising Effect 295 Ml',
          price: 999.99,
          imageUrl: 'https://placehold.co/150x150/000000/FFFFFF?text=iPhone',
          brandId: '3',
          categoryId: '1',
        ),
        Product(
          id: '2',
          name: 'Nike Air Max',
          price: 129.99,
          imageUrl: 'https://placehold.co/150x150/FF6B6B/FFFFFF?text=Nike',
          brandId: '1',
          categoryId: '3',
        ),
        Product(
          id: '3',
          name: 'Samsung Galaxy',
          price: 799.99,
          imageUrl: 'https://placehold.co/150x150/1428A0/FFFFFF?text=Samsung',
          brandId: '4',
          categoryId: '1',
        ),
        Product(
          id: '4',
          name: 'Adidas Hoodie',
          price: 79.99,
          imageUrl: 'https://placehold.co/150x150/000000/FFFFFF?text=Adidas',
          brandId: '2',
          categoryId: '2',
        ),
        Product(
          id: '5',
          name: 'Sony Headphones',
          price: 199.99,
          imageUrl: 'https://placehold.co/150x150/000000/FFFFFF?text=Sony',
          brandId: '5',
          categoryId: '1',
        ),
        Product(
          id: '6',
          name: 'H&M Dress',
          price: 49.99,
          imageUrl: 'https://placehold.co/150x150/E50000/FFFFFF?text=H%26M',
          brandId: '6',
          categoryId: '2',
        ),
      ];
}
