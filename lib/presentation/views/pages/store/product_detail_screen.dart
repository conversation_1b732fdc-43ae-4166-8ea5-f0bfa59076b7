import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:goldenprizma/domain/auth/bloc/authentication_bloc.dart';
import 'package:goldenprizma/domain/store/bloc/place_store_order_bloc.dart';
import 'package:goldenprizma/domain/store/models/product.dart';
import 'package:goldenprizma/domain/store/models/store_order.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/helpers/custom_dialogs.dart';
import 'package:goldenprizma/presentation/views/auth/login_screen.dart';
import 'package:toastification/toastification.dart';
import 'package:hugeicons/hugeicons.dart';

class ProductDetailPage extends StatefulWidget {
  final Product product;
  final String heroTag;

  const ProductDetailPage({
    super.key,
    required this.product,
    required this.heroTag,
  });

  static void showAsBottomSheet({
    required BuildContext context,
    required Product product,
    required String heroTag,
  }) {
    showCupertinoModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      shadow: const BoxShadow(
        color: Colors.transparent,
      ),
      topRadius: const Radius.circular(0),

      expand: true,
      enableDrag: false, // Disable to prevent conflicts with our custom gesture
      barrierColor: Colors.transparent, // Hide barrier/overlay
      builder: (context) => BeautifulProductDetail(
        product: product,
        heroTag: heroTag,
      ),
    );
  }

  @override
  State<ProductDetailPage> createState() => _ProductDetailPageState();
}

class _ProductDetailPageState extends State<ProductDetailPage> {
  @override
  Widget build(BuildContext context) {
    return const SizedBox.shrink();
  }
}

class BeautifulProductDetail extends StatefulWidget {
  final Product product;
  final String heroTag;

  const BeautifulProductDetail({
    super.key,
    required this.product,
    required this.heroTag,
  });

  @override
  State<BeautifulProductDetail> createState() => _BeautifulProductDetailState();
}

class _BeautifulProductDetailState extends State<BeautifulProductDetail>
    with TickerProviderStateMixin {
  // Animation controllers
  late AnimationController _snapBackController;
  late Animation<double> _snapBackAnimation;

  // Swipe gesture state
  double _dragDistance = 0.0;
  bool _isDragging = false;
  bool _hasTriggeredHaptic = false;
  Offset _dragOffset = Offset.zero; // Track both X and Y movement

  // UI state
  int _quantity = 1;
  bool _isLoading = false;

  // Constants
  static const double _dismissThreshold = 120.0;
  static const double _hideContentThreshold =
      20.0; // Reduced for faster response

  @override
  void initState() {
    super.initState();

    _snapBackController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _snapBackAnimation = CurvedAnimation(
      parent: _snapBackController,
      curve: Curves.easeOutCubic,
    );
  }

  @override
  void dispose() {
    _snapBackController.dispose();
    super.dispose();
  }

  // Check if touch is in the image area (where we want to enable grab gesture)
  bool _isTouchInImageArea(Offset globalPosition) {
    final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox == null) return false;

    final localPosition = renderBox.globalToLocal(globalPosition);
    final screenSize = MediaQuery.of(context).size;

    // Calculate the actual image area bounds using dynamic height
    // Image is now positioned at the top after the app bar
    const appBarHeight = 24.0; // From toolbarHeight
    const topPadding = 2.0; // From padding
    final imageHeight = (screenSize.height * 0.35).clamp(250.0, 400.0);

    // Image takes full width with 20px horizontal padding
    const horizontalPadding = 20.0;
    const imageLeft = horizontalPadding;
    final imageRight = screenSize.width - horizontalPadding;
    const imageTop = appBarHeight + topPadding;
    final imageBottom = imageTop + imageHeight;

    // Expand the touch area slightly for easier grabbing
    const touchPadding = 10.0;
    const expandedLeft = imageLeft - touchPadding;
    final expandedRight = imageRight + touchPadding;
    const expandedTop = imageTop - touchPadding;
    final expandedBottom = imageBottom + touchPadding;

    return localPosition.dx >= expandedLeft &&
        localPosition.dx <= expandedRight &&
        localPosition.dy >= expandedTop &&
        localPosition.dy <= expandedBottom;
  }

  // Gesture handlers for smooth 2D swipe-to-dismiss
  void _handlePanStart(DragStartDetails details) {
    // Only enable drag if touch is in image area
    if (!_isTouchInImageArea(details.globalPosition)) {
      return; // Let normal scrolling handle this
    }

    // Stop any ongoing animation
    if (_snapBackController.isAnimating) {
      _snapBackController.stop();
      _snapBackController.reset();
    }

    setState(() {
      _isDragging = true;
      _dragDistance = 0.0;
      _dragOffset = Offset.zero;
      _hasTriggeredHaptic = false;
    });
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    // Only continue if we started dragging (touch was in image area)
    if (!_isDragging) return;

    setState(() {
      // Always update both X and Y offset for smooth 2D movement
      _dragOffset += details.delta;

      // Count any downward movement for dismiss logic
      if (details.delta.dy > 0) {
        _dragDistance += details.delta.dy;
      } else if (_dragDistance > 0) {
        // Allow upward movement to reduce drag distance smoothly
        _dragDistance = (_dragDistance + details.delta.dy * 0.5)
            .clamp(0.0, double.infinity);
      }
    });

    // Haptic feedback when content starts hiding (reduced threshold)
    if (_dragDistance > _hideContentThreshold && !_hasTriggeredHaptic) {
      HapticFeedback.lightImpact(); // Lighter haptic for smoother feel
      _hasTriggeredHaptic = true;
    }
  }

  void _handlePanEnd(DragEndDetails details) {
    // Only handle end if we were actually dragging
    if (!_isDragging) return;

    _isDragging = false;
    final velocity = details.velocity.pixelsPerSecond.dy;

    // More lenient dismiss conditions for better UX
    if (_dragDistance > _dismissThreshold ||
        (_dragDistance > 50 && velocity > 300)) {
      _dismissBottomSheet();
    } else {
      _snapBackToNormal();
    }
  }

  void _dismissBottomSheet() {
    HapticFeedback.lightImpact();
    Navigator.of(context).pop();
  }

  void _snapBackToNormal() {
    final startDistance = _dragDistance;
    final startOffset = _dragOffset;

    // Clear any existing listeners to prevent conflicts
    _snapBackController.removeListener(() {});

    void animationListener() {
      if (_snapBackController.isAnimating) {
        setState(() {
          final progress = 1.0 - _snapBackAnimation.value;
          _dragDistance = startDistance * progress;
          _dragOffset = startOffset * progress;
        });
      }
    }

    _snapBackController.addListener(animationListener);

    _snapBackController.forward().then((_) {
      setState(() {
        _dragDistance = 0.0;
        _dragOffset = Offset.zero;
      });
      _snapBackController.removeListener(animationListener);
      _snapBackController.reset();
    });
  }

  // Computed properties for smooth interaction
  bool get _shouldHideContent => _dragDistance > _hideContentThreshold;

  // UI helpers
  void _incrementQuantity() {
    if (_quantity < widget.product.stocks) {
      setState(() {
        _quantity++;
      });
      HapticFeedback.selectionClick();
    }
  }

  void _decrementQuantity() {
    if (_quantity > 1) {
      setState(() {
        _quantity--;
      });
      HapticFeedback.selectionClick();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onPanStart: _handlePanStart,
      onPanUpdate: _handlePanUpdate,
      onPanEnd: _handlePanEnd,
      child: Scaffold(
        appBar: _shouldHideContent
            ? null
            : AppBar(
                elevation: 0,
                systemOverlayStyle: SystemUiOverlayStyle.light,
                toolbarHeight: 30,
                leading: Padding(
                  padding: const EdgeInsetsDirectional.only(start: 8, top: 0),
                  child: _backIcon(),
                ),
                leadingWidth: 48,
              ),
        backgroundColor: Colors.transparent,
        body: Stack(
          children: [
            if (!_shouldHideContent) _buildMainContent(),

            // Floating image that follows finger during drag
            if (_shouldHideContent) _buildFloatingImage(),
          ],
        ),
      ),
    );
  }

  Widget _backIcon() {
    return InkWell(
      onTap: () {
        Navigator.of(context).pop();
      },
      borderRadius: BorderRadius.circular(12),
      child: const SizedBox(
        width: 32,
        height: 32,
        child: Icon(
          Icons.arrow_back_ios_new,
          size: 16,
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return BlocListener<PlaceStoreOrderBloc, PlaceStoreOrderState>(
      listener: (context, state) {
        if (state.status == PlaceStoreOrderStatus.success) {
          setState(() {
            _isLoading = false;
          });
          Navigator.pop(context);
          showSuccessDialogWithAutoHide(
            context: context,
            message: state.message ??
                context.loc.orderPlacedSuccessfully(_quantity.toString()),
          );
        } else if (state.status == PlaceStoreOrderStatus.failure) {
          setState(() {
            _isLoading = false;
          });
          toastification.show(
            type: ToastificationType.error,
            alignment: Alignment.topCenter,
            context: context,
            title: state.message ?? context.loc.failedToPlaceOrder,
            autoCloseDuration: const Duration(seconds: 5),
            showProgressBar: false,
          );
        }
      },
      child: Material(
        child: Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
          ),
          child: Column(
            children: [
              // Fixed image area (no scrolling)
              Padding(
                padding: const EdgeInsets.fromLTRB(20, 2, 20, 0),
                child: _buildProductImage(),
              ),

              // Scrollable content area below image
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.fromLTRB(20, 20, 20, 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Product info
                      _buildProductInfo(),

                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),

              // Bottom buy section
              _buildBottomBuySection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProductImage() {
    final screenHeight = MediaQuery.of(context).size.height;
    // Use 35% of screen height for image, with min/max constraints
    final imageHeight = (screenHeight * 0.35).clamp(250.0, 400.0);

    return Center(
      child: Container(
        width: double.infinity,
        height: imageHeight,
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
        padding: const EdgeInsets.all(6),
        child: Hero(
          tag: widget.heroTag,
          child: CachedNetworkImage(
            imageUrl: widget.product.image,
            fit: BoxFit.contain,
            errorWidget: (context, error, stackTrace) => Icon(
              HugeIcons.strokeRoundedImage01,
              size: 80,
              color: Colors.grey.withOpacity(0.3),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProductInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.product.name,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Price display with promotion support (like product card)
            _buildPriceDisplay(context),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: widget.product.stocks > 0
                    ? Colors.green.shade50
                    : Colors.red.shade50,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: widget.product.stocks > 0
                      ? Colors.green.shade200
                      : Colors.red.shade200,
                ),
              ),
              child: Text(
                widget.product.stocks > 0
                    ? context.loc
                        .inStockWithCount(widget.product.stocks.toString())
                    : context.loc.outOfStock,
                style: TextStyle(
                  color: widget.product.stocks > 0
                      ? Colors.green.shade700
                      : Colors.red.shade700,
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Text(
          widget.product.description,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey.shade600,
                height: 1.5,
              ),
        ),
      ],
    );
  }

  Widget _buildPriceDisplay(BuildContext context) {
    if (widget.product.hasPromotion && widget.product.originalPrice != null) {
      // Show both original price (strikethrough) and current price (red)
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Original price with strikethrough
          Text(
            widget.product.originalPrice!.formattedAmount,
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              fontSize: 16,
              decoration: TextDecoration.lineThrough,
              decorationColor:
                  Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 4),
          // Current price in red
          Text(
            widget.product.currentPrice.formattedAmount,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
          ),
        ],
      );
    } else {
      // Show only current price with primary color
      return Text(
        widget.product.currentPrice.formattedAmount,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.bold,
            ),
      );
    }
  }

  Widget _buildQuantitySelector() {
    return Container(
      padding:
          const EdgeInsetsDirectional.only(top: 2, bottom: 2, start: 6, end: 2),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: Text(
              context.loc.quantity,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
          ),
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                IconButton(
                  onPressed: _decrementQuantity,
                  icon: const Icon(Icons.remove),
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    foregroundColor: Colors.grey.shade600,
                  ),
                ),
                Container(
                  width: 60,
                  alignment: Alignment.center,
                  child: Text(
                    _quantity.toString(),
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
                IconButton(
                  onPressed: _incrementQuantity,
                  icon: const Icon(Icons.add),
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    foregroundColor: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomBuySection() {
    return Container(
      padding: const EdgeInsets.only(top: 20, left: 20, right: 20, bottom: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 18,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          children: [
            // Quantity selector
            _buildQuantitySelector(),

            const SizedBox(height: 16),

            SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: widget.product.stocks > 0 && !_isLoading
                    ? _placeOrder
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  elevation: 0,
                ),
                child: _isLoading
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        context.loc.buyNow,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingImage() {
    // Calculate the image position - start from where it was in the bottom sheet
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final imageHeight = (screenHeight * 0.35).clamp(250.0, 400.0);

    // Base position (where image was in the new layout)
    // Image now takes full width with 20px horizontal padding
    const horizontalPadding = 20.0;
    const appBarHeight = 24.0;
    const topPadding = 2.0;

    const baseLeft = horizontalPadding;
    const baseTop = appBarHeight + topPadding;
    final imageWidth = screenWidth - (horizontalPadding * 2);

    // Add drag offset for smooth 2D movement with constraints
    final imageLeft = (baseLeft + _dragOffset.dx).clamp(
      -imageWidth * 0.3, // Allow some off-screen movement
      screenWidth - imageWidth * 0.7,
    );
    final imageTop = (baseTop + _dragOffset.dy).clamp(
      -imageHeight * 0.2, // Allow some off-screen movement
      screenHeight - imageHeight * 0.8,
    );

    return Positioned.fill(
      child: Container(
        // Transparent background - no overlay during drag
        color: Colors.transparent,
        child: Stack(
          children: [
            // Floating image that follows finger - pure image without background
            Positioned(
              left: imageLeft + 6, // Account for container padding
              top: imageTop + 6, // Account for container padding
              child: SizedBox(
                width: imageWidth - 12, // Remove container padding from size
                height: imageHeight - 12, // Remove container padding from size
                child: Hero(
                  tag: widget.heroTag,
                  child: CachedNetworkImage(
                    imageUrl: widget.product.image,
                    fit: BoxFit.contain,
                    errorWidget: (context, error, stackTrace) => Icon(
                      HugeIcons.strokeRoundedImage01,
                      size: 80,
                      color: Colors.grey.withOpacity(0.3),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _placeOrder() {
    final authState = context.read<AuthenticationBloc>().state;
    if (authState.status != AuthenticationStatus.authenticated) {
      Navigator.of(context).push(
        MaterialPageRoute(builder: (context) => const LoginScreen()),
      );
      return;
    }

    // Show confirmation dialog before placing order
    _showOrderConfirmationDialog();
  }

  void _showOrderConfirmationDialog() {
    final totalPrice =
        (double.tryParse(widget.product.currentPrice.amount) ?? 0.0) *
            _quantity;
    final formattedTotal =
        '${totalPrice.toStringAsFixed(2)} ${widget.product.currentPrice.currency}';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            context.loc.confirmOrder,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                context.loc.areYouSureYouWantToPlaceThisOrder,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: CachedNetworkImage(
                            imageUrl: widget.product.image,
                            width: 50,
                            height: 50,
                            fit: BoxFit.cover,
                            errorWidget: (context, error, stackTrace) =>
                                Container(
                              width: 50,
                              height: 50,
                              color: Colors.grey[200],
                              child:
                                  const Icon(Icons.image, color: Colors.grey),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.product.name,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '${context.loc.quantity}: $_quantity',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          context.loc.total,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          formattedTotal,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "${context.loc.stock}:",
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                        Text(
                          context.loc.inStockWithCount(
                              widget.product.stocks.toString()),
                          style: const TextStyle(
                            color: Colors.green,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                children: [
                  // Confirm Order button (primary action)
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        _confirmAndPlaceOrder();
                      },
                      style: ElevatedButton.styleFrom(
                        elevation: 0,
                        backgroundColor: Theme.of(context).primaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        context.loc.confirmOrder,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  // Maybe Later button (secondary action)
                  SizedBox(
                    width: double.infinity,
                    child: TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        context.loc.maybeLater,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  void _confirmAndPlaceOrder() {
    setState(() {
      _isLoading = true;
    });

    final storeOrder = StoreOrder(
      name: widget.product.name,
      image: widget.product.image,
      quantity: _quantity,
      description: widget.product.description,
      itemPrice: double.tryParse(widget.product.currentPrice.amount) ?? 0.0,
      productId: widget.product.id.toString(),
      link: widget.product.link,
      priceType: widget.product.priceType,
      barcode: widget.product.barcode ?? '',
    );

    context.read<PlaceStoreOrderBloc>().add(
          PlaceStoreOrderSubmitted(storeOrder: storeOrder),
        );
  }
}
