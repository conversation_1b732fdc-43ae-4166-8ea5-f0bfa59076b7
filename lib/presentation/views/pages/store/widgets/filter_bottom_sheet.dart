import 'package:flutter/material.dart';
import 'package:goldenprizma/helpers/app_settings.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:goldenprizma/domain/store/models/category.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'categories_bottom_sheet.dart';

class FilterBottomSheet extends StatefulWidget {
  final List<Category> categories;
  final int? selectedCategoryId;
  final bool isPromotionSelected;
  final bool isOutlet; // Add this
  final Function(int?) onCategorySelected;
  final Function(bool) onPromotionToggle;
  final VoidCallback onClearFilters;

  const FilterBottomSheet({
    super.key,
    required this.categories,
    required this.selectedCategoryId,
    required this.isPromotionSelected,
    required this.isOutlet, // Add this
    required this.onCategorySelected,
    required this.onPromotionToggle,
    required this.onClearFilters,
  });

  @override
  State<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  late int? selectedCategoryId;
  late bool isPromotionSelected;

  @override
  void initState() {
    super.initState();
    selectedCategoryId = widget.selectedCategoryId;
    isPromotionSelected = widget.isPromotionSelected;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Theme.of(context).dividerColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  context.loc.filters,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                TextButton(
                  onPressed: () {
                    setState(() {
                      selectedCategoryId = null;
                      isPromotionSelected = false;
                    });
                    widget.onClearFilters();
                    Navigator.pop(context);
                  },
                  child: Text(
                    context.loc.clearAll,
                    style: TextStyle(
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Filter options
          Flexible(
            child: ListView(
              shrinkWrap: true,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              children: [
                // Categories filter
                _buildFilterItem(
                  context: context,
                  icon: HugeIcons.strokeRoundedCatalogue,
                  title: context.loc.categories,
                  subtitle: selectedCategoryId != null
                      ? widget.categories
                          .firstWhere(
                            (cat) => cat.id == selectedCategoryId,
                            orElse: () =>
                                Category(id: 0, name: context.loc.categories),
                          )
                          .name
                      : context.loc.allCategories,
                  isActive: selectedCategoryId != null,
                  onTap: () => _showCategoriesBottomSheet(context),
                ),

                const SizedBox(height: 12),

                // Only show On Sale filter when outlet is false
                if (!widget.isOutlet) ...[
                  // On Sale filter (toggle switch)
                  _buildToggleFilterItem(
                    context: context,
                    icon: HugeIcons.strokeRoundedDiscount,
                    title: context.loc.onSale,
                    subtitle: isPromotionSelected
                        ? context.loc.active
                        : context.loc.inactive,
                    isActive: isPromotionSelected,
                    onToggle: (value) {
                      setState(() {
                        isPromotionSelected = value;
                      });
                      widget.onPromotionToggle(value);
                    },
                  ),

                  const SizedBox(height: 32),
                ] else
                  const SizedBox(height: 32),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isActive
              ? Theme.of(context).primaryColor.withOpacity(0.1)
              : Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isActive
                ? Theme.of(context).primaryColor.withOpacity(0.3)
                : Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isActive
                    ? Theme.of(context).primaryColor.withOpacity(0.2)
                    : Theme.of(context).colorScheme.onSurface.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: isActive
                    ? Theme.of(context).primaryColor
                    : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                      color: isActive
                          ? Theme.of(context).primaryColor
                          : Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withOpacity(0.6),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              AppSettings.isRtl(context)
                  ? HugeIcons.strokeRoundedArrowLeft02
                  : HugeIcons.strokeRoundedArrowRight02,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildToggleFilterItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required bool isActive,
    required Function(bool) onToggle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isActive
            ? Theme.of(context).primaryColor.withOpacity(0.1)
            : Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isActive
              ? Theme.of(context).primaryColor.withOpacity(0.3)
              : Theme.of(context).dividerColor,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isActive
                  ? Theme.of(context).primaryColor.withOpacity(0.2)
                  : Theme.of(context).colorScheme.onSurface.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: isActive
                  ? Theme.of(context).primaryColor
                  : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: isActive
                        ? Theme.of(context).primaryColor
                        : Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.6),
                  ),
                ),
              ],
            ),
          ),
          Switch.adaptive(
            value: isActive,
            onChanged: onToggle,
            activeColor: Theme.of(context).primaryColor,
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ],
      ),
    );
  }

  void _showCategoriesBottomSheet(BuildContext context) {
    CategoriesBottomSheet.show(
      context: context,
      selectedCategoryId: selectedCategoryId,
      onCategorySelected: (categoryId) {
        setState(() {
          selectedCategoryId = categoryId;
        });
        widget.onCategorySelected(categoryId);
        Navigator.pop(context); // Close filter sheet
      },
    );
  }
}
