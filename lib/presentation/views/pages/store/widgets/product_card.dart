import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:goldenprizma/domain/store/models/product.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import '../product_detail_screen.dart';

class ProductCard extends StatelessWidget {
  final Product product;

  const ProductCard({
    super.key,
    required this.product,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _navigateToDetailPage(context),
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor, // Theme-aware background
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Theme.of(context).dividerColor, // Theme-aware border
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Image
            Container(
              width: double.infinity,
              height: 150,
              padding: const EdgeInsets.all(12),
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
                color: Colors.white,
              ),
              child: ClipRRect(
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(16)),
                child: Hero(
                  tag: 'product_${product.id}',
                  child: CachedNetworkImage(
                    imageUrl: product.thumb,
                    fit: BoxFit.contain,
                    width: double.infinity,
                    height: double.infinity,
                    progressIndicatorBuilder: (context, url, progress) {
                      return const Center(
                        child: CircularProgressIndicator.adaptive(),
                      );
                    },
                    errorWidget: (context, error, stackTrace) => Container(
                      color: Theme.of(context).colorScheme.surface,
                      child: Icon(
                        Icons.shopping_bag,
                        size: 50,
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.3),
                      ),
                    ),
                  ),
                ),
              ), // Closing Hero widget
            ),
            // Product Info
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Hero(
                      tag: 'product_${product.id}_name',
                      child: Material(
                        color: Colors.transparent,
                        child: Text(
                          product.name,
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Spacer(),
                    // Price display with promotion support
                    _buildPriceSection(context),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      height: 32,
                      child: ElevatedButton(
                        onPressed: () => _navigateToDetailPage(context),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).primaryColor,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          elevation: 0,
                        ),
                        child: Text(
                          context.loc.buy,
                          style: const TextStyle(
                              fontSize: 12, fontWeight: FontWeight.w600),
                        ),
                      ),
                    ),
                  ],
                ),
              ), // Closing the Container
            ), // Closing the Expanded widget
          ],
        ), // Closing the Column
      ), // Closing the Container
    ); // Closing the GestureDetector
  }

  void _navigateToDetailPage(BuildContext context) {
    ProductDetailPage.showAsBottomSheet(
      context: context,
      product: product,
      heroTag: 'product_${product.id}',
    );
  }

  Widget _buildPriceSection(BuildContext context) {
    if (product.hasPromotion && product.originalPrice != null) {
      // Show both original price (strikethrough) and current price (red)
      return Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Original price with strikethrough
          Text(
            product.originalPrice!.formattedAmount,
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              fontSize: 15,
              decoration: TextDecoration.lineThrough,
              decorationColor:
                  Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          const SizedBox(width: 8),
          // Current price in red
          Text(
            product.currentPrice.formattedAmount,
            style: const TextStyle(
              color: Colors.red,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ],
      );
    } else {
      // Show only current price with normal color
      return Text(
        product.currentPrice.formattedAmount,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
      );
    }
  }
}
