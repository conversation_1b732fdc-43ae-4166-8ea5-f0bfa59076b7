import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/store/models/product.dart';
import 'package:goldenprizma/domain/store/models/store_order.dart';
import 'package:goldenprizma/domain/store/bloc/place_store_order_bloc.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/helpers/custom_dialogs.dart';
import 'package:goldenprizma/presentation/views/screens/home_screen.dart';
import 'package:toastification/toastification.dart';

class BuyProductBottomSheet extends StatefulWidget {
  final Product product;
  final VoidCallback? onPlaceOrder;

  const BuyProductBottomSheet({
    super.key,
    required this.product,
    this.onPlaceOrder,
  });

  @override
  State<BuyProductBottomSheet> createState() => _BuyProductBottomSheetState();
}

class _BuyProductBottomSheetState extends State<BuyProductBottomSheet> {
  int quantity = 1;
  bool isLoading = false;

  void _incrementQuantity() {
    if (quantity < widget.product.stocks) {
      setState(() {
        quantity++;
      });
    }
  }

  void _decrementQuantity() {
    if (quantity > 1) {
      setState(() {
        quantity--;
      });
    }
  }

  void _handlePlaceOrder() async {
    // Show confirmation dialog first
    final priceAmount =
        double.tryParse(widget.product.currentPrice.amount) ?? 0.0;
    final totalPrice = (priceAmount * quantity).toStringAsFixed(2);

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          context.loc.confirmOrder,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${context.loc.product}: ${widget.product.name}'),
            const SizedBox(height: 8),
            Text('${context.loc.quantity}: $quantity'),
            const SizedBox(height: 8),
            Text(
              '${context.loc.total}: \$$totalPrice',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              context.loc.areYouSureYouWantToPlaceThisOrder,
              style: const TextStyle(color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(context.loc.maybeLater),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(context.loc.confirmOrders),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      isLoading = true;
    });

    // Create store order
    final storeOrder = StoreOrder(
      name: widget.product.name,
      image: widget.product.image,
      quantity: quantity,
      description: widget.product.description,
      itemPrice: double.tryParse(widget.product.currentPrice.amount) ?? 0.0,
      priceType: widget.product.priceType,
      productId: widget.product.id.toString(),
      link: widget.product.link,
      barcode: widget.product.barcode ?? '',
    );

    // Place order using bloc
    if (mounted) {
      context.read<PlaceStoreOrderBloc>().add(
            PlaceStoreOrderSubmitted(storeOrder: storeOrder),
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Convert currentPrice.amount to double for calculation
    final priceAmount =
        double.tryParse(widget.product.currentPrice.amount) ?? 0.0;
    final totalPrice = (priceAmount * quantity).toStringAsFixed(2);

    return BlocListener<PlaceStoreOrderBloc, PlaceStoreOrderState>(
      listener: (context, state) {
        if (state.status == PlaceStoreOrderStatus.success) {
          setState(() {
            isLoading = false;
          });
          Navigator.pop(context);
          widget.onPlaceOrder?.call();

          // Show success dialog like in new_order_screen
          showSuccessDialogWithAutoHide(
            context: context,
            message: state.message ??
                context.loc.orderPlacedSuccessfully(quantity.toString()),
          ).then((value) {
            if (context.mounted) {
              Navigator.of(context).push(PageRouteBuilder(
                pageBuilder: (context, animation, secondaryAnimation) =>
                    const HomeScreen(initialPage: 2), // MyOrdersPage
              ));
            }
          });
        } else if (state.status == PlaceStoreOrderStatus.failure) {
          setState(() {
            isLoading = false;
          });

          // Show error message at top of screen in the parent context
          toastification.show(
            type: ToastificationType.error,
            alignment: Alignment.topCenter,
            context: context, // optional if you use ToastificationWrapper
            title: state.message ?? context.loc.failedToPlaceOrder,
            autoCloseDuration: const Duration(seconds: 5),
            showProgressBar: false,
          );
        }
      },
      child: _buildBottomSheet(context, totalPrice),
    );
  }

  Widget _buildBottomSheet(BuildContext context, String totalPrice) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 8, bottom: 16),
            width: 36,
            height: 4,
            decoration: BoxDecoration(
              color: Theme.of(context).dividerColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header with close button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  context.loc.placeOrder,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(
                    Icons.close,
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.6),
                    size: 20,
                  ),
                  visualDensity: VisualDensity.compact,
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // Product info section - compact horizontal layout
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(12),
                // Removed border
              ),
              child: Row(
                children: [
                  // Product Image - slightly bigger
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      width: 120,
                      height: 120,
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withOpacity(0.1),
                        ),
                      ),
                      child: Image.network(
                        widget.product.thumb.isNotEmpty
                            ? widget.product.thumb
                            : widget.product.image,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => Container(
                          color: Theme.of(context).colorScheme.surface,
                          child: Icon(
                            Icons.shopping_bag_outlined,
                            size: 24,
                            color: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withOpacity(0.4),
                          ),
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Product Details - compact
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.product.name,
                          style: Theme.of(context)
                              .textTheme
                              .titleMedium
                              ?.copyWith(
                                fontWeight: FontWeight.w700,
                                color: Theme.of(context).colorScheme.onSurface,
                                height: 1.2,
                              ),
                          maxLines: 4,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 10),
                        Text(
                          context.loc.inStockWithCount(
                              widget.product.stocks.toString()),
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: widget.product.stocks > 0
                                        ? Colors.green
                                        : Colors.red,
                                    fontWeight: FontWeight.w600,
                                  ),
                        ),
                        const SizedBox(height: 10),
                        // Price display with promotion support
                        _buildPriceDisplay(context),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Quantity Section - compact
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  context.loc.quantity,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                ),
                // Modern quantity selector with proper buttons
                Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).cardColor,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withOpacity(0.1),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Decrease button - proper button styling
                      Container(
                        decoration: BoxDecoration(
                          color: quantity > 1
                              ? Theme.of(context).primaryColor
                              : Theme.of(context).disabledColor,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: quantity > 1 ? _decrementQuantity : null,
                            borderRadius: BorderRadius.circular(8),
                            child: const SizedBox(
                              width: 36,
                              height: 36,
                              child: Icon(
                                Icons.remove,
                                size: 20,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(width: 12),

                      // Quantity display - simple text with bigger font
                      Container(
                        width: 72,
                        height: 40,
                        alignment: Alignment.center,
                        child: Text(
                          quantity.toString(),
                          style: Theme.of(context)
                              .textTheme
                              .titleLarge
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                        ),
                      ),

                      const SizedBox(width: 12),

                      // Increase button - proper button styling
                      Container(
                        decoration: BoxDecoration(
                          color: quantity < widget.product.stocks
                              ? Theme.of(context).primaryColor
                              : Theme.of(context).disabledColor,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: quantity < widget.product.stocks
                                ? _incrementQuantity
                                : null,
                            borderRadius: BorderRadius.circular(8),
                            child: const SizedBox(
                              width: 36,
                              height: 36,
                              child: Icon(
                                Icons.add,
                                size: 20,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Total Price Section - compact
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.08),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).primaryColor.withOpacity(0.2),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        context.loc.totalItems(
                            quantity.toString(),
                            quantity == 1
                                ? context.loc.item
                                : context.loc.items),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurface
                                  .withOpacity(0.7),
                            ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        '\$$totalPrice',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).primaryColor,
                            ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Place Order Button - compact
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 0, 20, 24),
            child: SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                onPressed: isLoading ? null : _handlePlaceOrder,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.shopping_cart,
                            size: 18,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            context.loc.placeOrder,
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                          ),
                        ],
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceDisplay(BuildContext context) {
    if (widget.product.hasPromotion && widget.product.originalPrice != null) {
      // Show both original price (strikethrough) and current price (red)
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Original price with strikethrough
          Text(
            widget.product.originalPrice!.formattedAmount,
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              fontSize: 14,
              decoration: TextDecoration.lineThrough,
              decorationColor:
                  Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 2),
          // Current price in red
          Text(
            widget.product.currentPrice.formattedAmount,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
          ),
        ],
      );
    } else {
      // Show only current price with normal color
      return Text(
        widget.product.currentPrice.formattedAmount,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
      );
    }
  }
}
