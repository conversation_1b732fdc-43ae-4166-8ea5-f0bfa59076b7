import 'package:flutter/material.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:goldenprizma/domain/store/models/brand.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';

class BrandsSection extends StatelessWidget {
  final List<Brand> brands;
  final int? selectedBrandId;
  final bool isOutletSelected;
  final Function(int?, Brand?) onBrandSelected;
  final VoidCallback onOutletSelected;
  final VoidCallback onViewAllPressed;
  final Animation<double>? scaleAnimation;

  const BrandsSection({
    super.key,
    required this.brands,
    required this.selectedBrandId,
    required this.isOutletSelected,
    required this.onBrandSelected,
    required this.onOutletSelected,
    required this.onViewAllPressed,
    this.scaleAnimation,
  });

  int _calculateItemCount() {
    // Outlets + All Brands + Individual Brands + View All
    const fixedItems = 2; // Outlets + All Brands

    if (brands.isEmpty) {
      return fixedItems; // Just Outlets + All Brands
    }

    return fixedItems + brands.length + 1; // + Individual Brands + View All
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: scaleAnimation ?? const AlwaysStoppedAnimation(1.0),
      builder: (context, child) {
        // Calculate dynamic values based on animation - less aggressive compression
        final animationValue = scaleAnimation?.value ?? 0.0;
        final height = 90.0 - (animationValue * 20.0);
        final topPadding = 16.0 - (animationValue * 6.0);
        final bottomPadding = 4.0 - (animationValue * 1.5);

        return Container(
          color: Theme.of(context).colorScheme.surface,
          padding: EdgeInsets.only(top: topPadding, bottom: bottomPadding),
          child: SizedBox(
            height: height,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 14),
              itemCount: _calculateItemCount(),
              itemBuilder: (context, index) {
                // Calculate dynamic sizes for all items - less aggressive compression
                final containerWidth = 70.0 - (animationValue * 6.0);
                final imageSize = 60.0 - (animationValue * 10.0);
                final fontSize = 12.0 - (animationValue * 0.5);
                final marginRight = 6.0 - (animationValue * 1.5);
                final spacing = 8.0 - (animationValue * 2.0);

                // First item: "All Brands"
                if (index == 0) {
                  return _buildAllBrandsItem(context, containerWidth, imageSize,
                      fontSize, marginRight, spacing);
                }

                // Second item: Outlets (always after All Brands)
                if (index == 1) {
                  return _buildOutletItem(context, containerWidth, imageSize,
                      fontSize, marginRight, spacing);
                }

                // Calculate the correct offset for brands and view all
                const offset = 2; // Outlets + All Brands

                // Last item: "View All" - only show if there are brands to display
                if (brands.isNotEmpty && index == brands.length + offset) {
                  return _buildViewAllBrands(context, containerWidth, imageSize,
                      fontSize, marginRight, spacing);
                }

                // Regular brand items - add safety check
                final brandIndex = index - offset;
                if (brandIndex >= brands.length || brandIndex < 0) {
                  return const SizedBox
                      .shrink(); // Return empty widget if index is out of bounds
                }

                final brand = brands[brandIndex];
                final isSelected = selectedBrandId == brand.id;

                return GestureDetector(
                  onTap: () => onBrandSelected(
                      isSelected ? null : brand.id, isSelected ? null : brand),
                  child: Container(
                    width: containerWidth,
                    margin: EdgeInsets.only(
                      right: Directionality.of(context) == TextDirection.ltr
                          ? marginRight
                          : 0,
                      left: Directionality.of(context) == TextDirection.rtl
                          ? marginRight
                          : 0,
                    ),
                    child: Column(
                      children: [
                        Container(
                          width: imageSize,
                          height: imageSize,
                          decoration: BoxDecoration(
                            color: isSelected
                                ? Theme.of(context)
                                    .primaryColor
                                    .withOpacity(0.1)
                                : Theme.of(context).cardColor,
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: isSelected
                                  ? Theme.of(context).primaryColor
                                  : Theme.of(context).dividerColor,
                              width: isSelected ? 2 : 1,
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(2.0),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: Image.network(
                                brand.displayLogoUrl,
                                fit: BoxFit.contain,
                                errorBuilder: (context, error, stackTrace) =>
                                    Container(
                                  color: Theme.of(context).colorScheme.surface,
                                  child: Icon(
                                    Icons.store,
                                    size: 24,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface
                                        .withOpacity(0.4),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: spacing),
                        Text(
                          brand.name,
                          style: TextStyle(
                            fontSize: fontSize,
                            fontWeight: isSelected
                                ? FontWeight.bold
                                : FontWeight.normal,
                            color: isSelected
                                ? Theme.of(context).primaryColor
                                : Theme.of(context)
                                    .colorScheme
                                    .onSurface
                                    .withOpacity(0.7),
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildAllBrandsItem(BuildContext context, double containerWidth,
      double imageSize, double fontSize, double marginRight, double spacing) {
    final isSelected = selectedBrandId == null && !isOutletSelected;

    return GestureDetector(
      onTap: () => onBrandSelected(null, null),
      child: Container(
        width: containerWidth,
        margin: EdgeInsets.only(
          right:
              Directionality.of(context) == TextDirection.ltr ? marginRight : 0,
          left:
              Directionality.of(context) == TextDirection.rtl ? marginRight : 0,
        ),
        child: Column(
          children: [
            Container(
              width: imageSize,
              height: imageSize,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                color: isSelected
                    ? Theme.of(context).primaryColor.withOpacity(0.1)
                    : Theme.of(context).cardColor,
                border: Border.all(
                  color: isSelected
                      ? Theme.of(context).primaryColor
                      : Theme.of(context).dividerColor,
                  width: isSelected ? 2 : 1,
                ),
              ),
              child: Icon(
                HugeIcons.strokeRoundedBrandfetch,
                color: isSelected
                    ? Theme.of(context).primaryColor
                    : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                size: 24,
              ),
            ),
            SizedBox(height: spacing),
            Text(
              context.loc.allBrands,
              style: TextStyle(
                fontSize: fontSize,
                color: isSelected
                    ? Theme.of(context).primaryColor
                    : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildViewAllBrands(BuildContext context, double containerWidth,
      double imageSize, double fontSize, double marginRight, double spacing) {
    return GestureDetector(
      onTap: onViewAllPressed,
      child: Container(
        width: containerWidth,
        margin: EdgeInsets.only(
          right:
              Directionality.of(context) == TextDirection.ltr ? marginRight : 0,
          left:
              Directionality.of(context) == TextDirection.rtl ? marginRight : 0,
        ),
        child: Column(
          children: [
            Container(
              width: imageSize,
              height: imageSize,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                color: Theme.of(context).cardColor,
                border: Border.all(color: Theme.of(context).dividerColor),
              ),
              child: Icon(
                HugeIcons.strokeRoundedMoreHorizontal,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                size: 20,
              ),
            ),
            SizedBox(height: spacing),
            Text(
              context.loc.viewAll,
              style: TextStyle(
                fontSize: fontSize,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOutletItem(BuildContext context, double containerWidth,
      double imageSize, double fontSize, double marginRight, double spacing) {
    return GestureDetector(
      onTap: onOutletSelected,
      child: Container(
        width: containerWidth,
        margin: EdgeInsets.only(
          right:
              Directionality.of(context) == TextDirection.ltr ? marginRight : 0,
          left:
              Directionality.of(context) == TextDirection.rtl ? marginRight : 0,
        ),
        child: Column(
          children: [
            Container(
              width: imageSize,
              height: imageSize,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                color: isOutletSelected
                    ? Theme.of(context).primaryColor.withOpacity(0.1)
                    : Theme.of(context).cardColor,
                border: Border.all(
                  color: isOutletSelected
                      ? Theme.of(context).primaryColor
                      : Theme.of(context).dividerColor,
                  width: isOutletSelected ? 2 : 1,
                ),
              ),
              child: Icon(
                HugeIcons.strokeRoundedStore01,
                color: isOutletSelected
                    ? Theme.of(context).primaryColor
                    : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                size: 20,
              ),
            ),
            SizedBox(height: spacing),
            Text(
              context.loc.outlets,
              style: TextStyle(
                fontSize: fontSize,
                fontWeight:
                    isOutletSelected ? FontWeight.bold : FontWeight.normal,
                color: isOutletSelected
                    ? Theme.of(context).primaryColor
                    : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
