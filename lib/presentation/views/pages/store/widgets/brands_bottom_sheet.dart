import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:goldenprizma/domain/store/bloc/brands_bloc.dart';
import 'package:goldenprizma/domain/store/models/brand.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/presentation/widgets/shimmer_loading.dart';
import 'package:hugeicons/hugeicons.dart';

class BrandsBottomSheet extends StatefulWidget {
  final int? selectedBrandId;
  final Function(int?, Brand?) onBrandSelected;

  const BrandsBottomSheet({
    super.key,
    required this.selectedBrandId,
    required this.onBrandSelected,
  });

  static void show({
    required BuildContext context,
    required int? selectedBrandId,
    required Function(int?, Brand?) onBrandSelected,
  }) {
    showCupertinoModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => BrandsBottomSheet(
        selectedBrandId: selectedBrandId,
        onBrandSelected: onBrandSelected,
      ),
    );
  }

  @override
  State<BrandsBottomSheet> createState() => _BrandsBottomSheetState();
}

class _BrandsBottomSheetState extends State<BrandsBottomSheet> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    // This will be handled by BlocBuilder
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: Theme.of(context)
              .scaffoldBackgroundColor, // Theme-aware background
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              margin: const EdgeInsets.symmetric(vertical: 12),
              width: 50,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).dividerColor, // Handle color
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Title
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Select Brand',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            // Search Bar
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search brands...',
                  prefixIcon: const Icon(HugeIcons.strokeRoundedSearch01),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(HugeIcons.strokeRoundedCancel01),
                          onPressed: () {
                            _searchController.clear();
                          },
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide:
                        BorderSide(color: Theme.of(context).dividerColor),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide:
                        BorderSide(color: Theme.of(context).dividerColor),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide:
                        BorderSide(color: Theme.of(context).primaryColor),
                  ),
                  filled: true,
                  fillColor: Theme.of(context).colorScheme.surface,
                ),
                onChanged: (value) =>
                    setState(() {}), // Trigger rebuild for suffix icon
              ),
            ),
            const SizedBox(height: 8),
            const Divider(),
            // Brands List
            Expanded(
              child: BlocBuilder<BrandsBloc, BrandsState>(
                builder: (context, state) {
                  if (state.status == BrandsStatus.loading) {
                    return ShimmerLoading.brands();
                  }

                  if (state.status == BrandsStatus.failure) {
                    return const Center(
                      child: Text('Failed to load brands'),
                    );
                  }

                  final brands = state.brands;
                  final query = _searchController.text.toLowerCase();
                  final filteredBrands = query.isEmpty
                      ? brands
                      : brands
                          .where((brand) =>
                              brand.name.toLowerCase().contains(query))
                          .toList();

                  return ListView.builder(
                    padding: EdgeInsets.zero,
                    itemCount: filteredBrands.length + 1, // +1 for "All Brands"
                    itemBuilder: (context, index) {
                      if (index == 0) {
                        return _buildBrandTile(
                          context: context,
                          brandId: null,
                          name: context.loc.allBrands,
                          imageUrl:
                              'https://placehold.co/50x50/6C5CE7/FFFFFF?text=All',
                          isSelected: widget.selectedBrandId == null,
                          onTap: () {
                            widget.onBrandSelected(null, null);
                          },
                        );
                      }
                      final brand = filteredBrands[index - 1];
                      return _buildBrandTile(
                        context: context,
                        brandId: brand.id,
                        name: brand.name,
                        imageUrl: brand.displayLogoUrl,
                        isSelected: widget.selectedBrandId == brand.id,
                        onTap: () {
                          widget.onBrandSelected(brand.id, brand);
                        },
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  static Widget _buildBrandTile({
    required BuildContext context,
    required int? brandId,
    required String name,
    required String imageUrl,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(
          horizontal: 20, vertical: 2), // Reduced vertical margin from 4 to 2
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 10), // Reduced vertical padding from 12 to 10
          decoration: BoxDecoration(
            color: isSelected
                ? Theme.of(context).primaryColor.withOpacity(0.08)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isSelected
                  ? Theme.of(context).primaryColor
                  : Theme.of(context).dividerColor,
              width: isSelected ? 1.5 : 1,
            ),
          ),
          child: Row(
            children: [
              // Brand image - rounded rectangle with high radius (iOS style)
              Container(
                width: 44,
                height: 44,
                decoration: BoxDecoration(
                  color: Theme.of(context)
                      .cardColor, // Theme-aware white background
                  borderRadius:
                      BorderRadius.circular(12), // High radius like iOS
                  border: Border.all(
                      color: Theme.of(context).dividerColor, width: 1),
                  // Removed boxShadow for cleaner look
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(11),
                  child: Image.network(
                    imageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      color: Theme.of(context).colorScheme.surface,
                      child: Icon(
                        Icons.store,
                        size: 20,
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.5),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // Brand name
              Expanded(
                child: Text(
                  name,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    color: isSelected
                        ? Theme.of(context).primaryColor
                        : Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ),
              // Selection indicator
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: Theme.of(context).primaryColor,
                  size: 22,
                )
              else
                Icon(
                  Icons.circle_outlined,
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                  size: 22,
                ),
            ],
          ),
        ),
      ),
    );
  }
}
