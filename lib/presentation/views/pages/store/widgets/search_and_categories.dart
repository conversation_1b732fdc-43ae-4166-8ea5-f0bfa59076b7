import 'package:flutter/material.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:goldenprizma/domain/store/models/category.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';

class SearchAndCategories extends StatelessWidget {
  final TextEditingController searchController;
  final List<Category> categories;
  final int? selectedCategoryId;
  final bool isPromotionSelected;
  final VoidCallback onFiltersPressed;
  final VoidCallback? onSearchChanged; // Add this

  const SearchAndCategories({
    super.key,
    required this.searchController,
    required this.categories,
    required this.selectedCategoryId,
    required this.isPromotionSelected,
    required this.onFiltersPressed,
    this.onSearchChanged, // Add this
  });

  @override
  Widget build(BuildContext context) {
    // Check if any filter is active
    final hasActiveFilters = selectedCategoryId != null || isPromotionSelected;

    return Container(
      color: Theme.of(context).colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
        child: Row(
          children: [
            Expanded(
              flex: 3,
              child: SizedBox(
                height: 40,
                child: TextField(
                  controller: searchController,
                  style: const TextStyle(fontSize: 15),
                  onChanged: (value) => onSearchChanged?.call(), // Add this
                  decoration: InputDecoration(
                    hintText: context.loc.searchProductsPlaceholder,
                    hintStyle: const TextStyle(fontSize: 15),
                    prefixIcon:
                        const Icon(HugeIcons.strokeRoundedSearch01, size: 20),
                    filled: true,
                    fillColor: Theme.of(context).cardColor,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Theme.of(context).dividerColor,
                        width: 1,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Theme.of(context).dividerColor,
                        width: 1,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Theme.of(context).primaryColor,
                        width: 2,
                      ),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            GestureDetector(
              onTap: onFiltersPressed,
              child: Container(
                height: 40,
                width: 88,
                padding: const EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  color: hasActiveFilters
                      ? Theme.of(context).primaryColor.withOpacity(0.1)
                      : Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: hasActiveFilters
                        ? Theme.of(context).primaryColor.withOpacity(0.3)
                        : Theme.of(context).dividerColor,
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      HugeIcons.strokeRoundedFilterHorizontal,
                      color: hasActiveFilters
                          ? Theme.of(context).primaryColor
                          : Theme.of(context).iconTheme.color,
                      size: 18,
                    ),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        context.loc.filter,
                        style: TextStyle(
                          color: hasActiveFilters
                              ? Theme.of(context).primaryColor
                              : Theme.of(context).colorScheme.onSurface,
                          fontWeight: hasActiveFilters
                              ? FontWeight.w600
                              : FontWeight.w500,
                          fontSize: 14,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
