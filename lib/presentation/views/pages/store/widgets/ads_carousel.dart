import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import '../models/store_models.dart';

class AdsCarousel extends StatelessWidget {
  final List<Ad> ads;
  final Animation<double> fadeAnimation;

  const AdsCarousel({
    super.key,
    required this.ads,
    required this.fadeAnimation,
  });

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: AnimatedBuilder(
        animation: fadeAnimation,
        builder: (context, child) {
          return Transform.translate(
            offset: Offset(0, -20 * (1 - fadeAnimation.value)),
            child: Opacity(
              opacity: fadeAnimation.value,
              child: SizedBox(
                height: (96 + 20) * fadeAnimation.value,
                child: fadeAnimation.value > 0.05
                    ? Padding(
                        padding: const EdgeInsets.only(
                            top: 16.0, right: 12.0, left: 12.0, bottom: 4.0),
                        child: SizedBox(
                          width: double.infinity,
                          height: 96,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: CarouselSlider.builder(
                              itemCount: ads.length,
                              itemBuilder: (context, index, realIndex) {
                                final ad = ads[index];
                                return SizedBox(
                                  width: double.infinity,
                                  child: Image.network(
                                    ad.imageUrl,
                                    fit: BoxFit.cover,
                                    width: double.infinity,
                                    errorBuilder:
                                        (context, error, stackTrace) =>
                                            Container(
                                      color: Theme.of(context).dividerColor,
                                      child: const Icon(Icons.image, size: 40),
                                    ),
                                  ),
                                );
                              },
                              options: CarouselOptions(
                                height: 96,
                                autoPlay: true,
                                aspectRatio: 16 / 9,
                                viewportFraction: 1,
                                enableInfiniteScroll: true,
                                autoPlayInterval: const Duration(seconds: 4),
                              ),
                            ),
                          ),
                        ),
                      )
                    : const SizedBox.shrink(),
              ),
            ),
          );
        },
      ),
    );
  }
}
