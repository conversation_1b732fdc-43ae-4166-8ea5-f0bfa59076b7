import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:goldenprizma/domain/store/bloc/categories_bloc.dart';
import 'package:goldenprizma/presentation/widgets/shimmer_loading.dart';
import 'package:hugeicons/hugeicons.dart';

class CategoriesBottomSheet extends StatefulWidget {
  final int? selectedCategoryId;
  final Function(int?) onCategorySelected;

  const CategoriesBottomSheet({
    super.key,
    required this.selectedCategoryId,
    required this.onCategorySelected,
  });

  static void show({
    required BuildContext context,
    required int? selectedCategoryId,
    required Function(int?) onCategorySelected,
  }) {
    showCupertinoModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => CategoriesBottomSheet(
        selectedCategoryId: selectedCategoryId,
        onCategorySelected: onCategorySelected,
      ),
    );
  }

  @override
  State<CategoriesBottomSheet> createState() => _CategoriesBottomSheetState();
}

class _CategoriesBottomSheetState extends State<CategoriesBottomSheet> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    // This will be handled by BlocBuilder
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Container(
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.only(bottom: 32),
        decoration: BoxDecoration(
          color: Theme.of(context)
              .scaffoldBackgroundColor, // Theme-aware background
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              margin: const EdgeInsets.symmetric(vertical: 12),
              width: 50,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).dividerColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Title
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Select Category',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            // Search Bar
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search categories...',
                  prefixIcon: const Icon(HugeIcons.strokeRoundedSearch01),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(HugeIcons.strokeRoundedCancel01),
                          onPressed: () {
                            _searchController.clear();
                          },
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide:
                        BorderSide(color: Theme.of(context).dividerColor),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide:
                        BorderSide(color: Theme.of(context).dividerColor),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide:
                        BorderSide(color: Theme.of(context).primaryColor),
                  ),
                  filled: true,
                  fillColor: Theme.of(context).colorScheme.surface,
                ),
                onChanged: (value) =>
                    setState(() {}), // Trigger rebuild for suffix icon
              ),
            ),
            const SizedBox(height: 8),
            const Divider(),
            // Categories List
            Expanded(
              child: BlocBuilder<CategoriesBloc, CategoriesState>(
                builder: (context, state) {
                  if (state.status == CategoriesStatus.loading) {
                    return ShimmerLoading.brands();
                  }

                  if (state.status == CategoriesStatus.failure) {
                    return const Center(
                      child: Text('Failed to load categories'),
                    );
                  }

                  final categories = state.categories;
                  final query = _searchController.text.toLowerCase();
                  final filteredCategories = query.isEmpty
                      ? categories
                      : categories
                          .where((category) =>
                              category.name.toLowerCase().contains(query))
                          .toList();

                  return ListView.builder(
                    padding: EdgeInsets.zero,
                    itemCount: filteredCategories.length +
                        1, // +1 for "All Categories"
                    itemBuilder: (context, index) {
                      if (index == 0) {
                        return _buildCategoryTile(
                          context: context,
                          categoryId: null,
                          name: context.loc.allCategories,
                          isSelected: widget.selectedCategoryId == null,
                          onTap: () {
                            widget.onCategorySelected(null);
                          },
                        );
                      }
                      final category = filteredCategories[index - 1];
                      return _buildCategoryTile(
                        context: context,
                        categoryId: category.id,
                        name: category.name,
                        isSelected: widget.selectedCategoryId == category.id,
                        onTap: () {
                          widget.onCategorySelected(category.id);
                        },
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  static Widget _buildCategoryTile({
    required BuildContext context,
    required int? categoryId,
    required String name,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(
          horizontal: 20, vertical: 2), // Reduced vertical margin from 4 to 2
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.symmetric(
              horizontal: 20,
              vertical: 12), // Reduced vertical padding from 16 to 12
          decoration: BoxDecoration(
            color: isSelected
                ? Theme.of(context).primaryColor.withOpacity(0.08)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isSelected
                  ? Theme.of(context).primaryColor
                  : Theme.of(context).dividerColor,
              width: isSelected ? 1.5 : 1,
            ),
          ),
          child: Row(
            children: [
              // Category name
              Expanded(
                child: Text(
                  name,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    color: isSelected
                        ? Theme.of(context).primaryColor
                        : Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ),
              // Selection indicator
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: Theme.of(context).primaryColor,
                  size: 22,
                )
              else
                Icon(
                  Icons.circle_outlined,
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
                  size: 22,
                ),
            ],
          ),
        ),
      ),
    );
  }
}
