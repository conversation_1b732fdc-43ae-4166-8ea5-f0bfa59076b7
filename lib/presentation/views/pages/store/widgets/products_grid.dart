import 'package:flutter/material.dart';
import 'package:goldenprizma/domain/store/models/product.dart';
import 'product_card.dart';

class ProductsGrid extends StatelessWidget {
  final List<Product> products;
  final int? selectedBrandId;
  final int? selectedCategoryId;
  final String searchQuery;
  final bool hasMore;
  final bool isLoadingMore;

  const ProductsGrid({
    super.key,
    required this.products,
    required this.selectedBrandId,
    required this.selectedCategoryId,
    required this.searchQuery,
    this.hasMore = true,
    this.isLoadingMore = false,
  });

  @override
  Widget build(BuildContext context) {
    // No need to filter here - BLoC already provides filtered products
    final totalItems = products.length + (isLoadingMore ? 1 : 0);

    return SliverPadding(
      padding: const EdgeInsets.fromLTRB(16, 4, 16, 180),
      sliver: SliverGrid(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          mainAxisExtent:
              280, // Increased to prevent overflow (160px image + ~140px info section)
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
        ),
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            // Show loading indicator at the end if loading more
            if (index >= products.length) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: CircularProgressIndicator(),
                ),
              );
            }

            final product = products[index];
            return ProductCard(
              product: product,
            );
          },
          childCount: totalItems,
        ),
      ),
    );
  }
}
