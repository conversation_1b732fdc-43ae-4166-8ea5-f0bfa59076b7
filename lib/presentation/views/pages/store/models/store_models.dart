// Store page data models

class Brand {
  final String id;
  final String name;
  final String imageUrl;

  Brand({required this.id, required this.name, required this.imageUrl});
}

class Category {
  final String id;
  final String name;
  final String imageUrl;

  Category({required this.id, required this.name, required this.imageUrl});
}

class Product {
  final String id;
  final String name;
  final double price;
  final String imageUrl;
  final String brandId;
  final String categoryId;

  Product({
    required this.id,
    required this.name,
    required this.price,
    required this.imageUrl,
    required this.brandId,
    required this.categoryId,
  });
}

class Ad {
  final String id;
  final String title;
  final String subtitle;
  final String imageUrl;

  Ad({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.imageUrl,
  });
}
