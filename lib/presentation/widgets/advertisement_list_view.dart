import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/ads/bloc/advertisement_bloc.dart';
import 'package:goldenprizma/domain/ads/models/advertisement.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/services/advertisement_action_service.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';

class AdvertisementListView extends StatefulWidget {
  const AdvertisementListView({super.key});

  @override
  State<AdvertisementListView> createState() => _AdvertisementListViewState();
}

class _AdvertisementListViewState extends State<AdvertisementListView> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    _scrollController.addListener(_onScroll);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AdvertisementBloc, AdvertisementState>(
      builder: (context, state) {
        if (state.status == AdvertisementStatus.initial) {
          return Expanded(child: _shimmer());
        }

        if ((state.status == AdvertisementStatus.success ||
                state.status == AdvertisementStatus.failure) &&
            state.advertismenets.isEmpty) {
          return Center(
            child: Text(context.loc.noData),
          );
        }

        return Expanded(
            child: ListView.separated(
          controller: _scrollController,
          scrollDirection: Axis.vertical,
          itemBuilder: (BuildContext context, int index) {
            if (index < state.advertismenets.length) {
              return GestureDetector(
                onTap: () {
                  Advertisement ads = state.advertismenets[index];
                  AdvertisementActionService()
                      .handleAdvertisementTap(context, ads);
                },
                child: Image.network(
                  state.advertismenets[index].image,
                  fit: BoxFit.contain,
                  width: double.infinity,
                ),
              );
            }

            return const SizedBox(
              height: 80,
              child: Padding(
                padding: EdgeInsets.only(bottom: 16),
                child: Center(child: CircularProgressIndicator.adaptive()),
              ),
            );
          },
          separatorBuilder: (_, int index) => const Divider(height: 2),
          itemCount: state.advertismenets.length +
              (state.status == AdvertisementStatus.loading ? 1 : 0),
        ));
      },
    );
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<AdvertisementBloc>().add(AdvertisementRequestLoad());
    }
  }

  bool get _isBottom {
    double maxScroll = _scrollController.position.maxScrollExtent;
    double currentScroll = _scrollController.position.pixels;
    double delta = 850.0;
    return maxScroll - currentScroll <= delta &&
        _scrollController.position.pixels != 0;
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Widget _shimmer() {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    return Shimmer.fromColors(
      baseColor: isDarkMode ? AppColors.boxDarkColor : Colors.grey.shade300,
      highlightColor: isDarkMode ? Colors.black45 : Colors.grey.shade100,
      enabled: true,
      child: ListView.separated(
        itemBuilder: (_, __) => _shimmerRow(),
        itemCount: 6,
        separatorBuilder: (_, __) => const Divider(height: 4),
      ),
    );
  }

  Widget _shimmerRow() {
    return SizedBox(
      width: 390,
      height: 200,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(2),
        ),
      ),
    );
  }
}
