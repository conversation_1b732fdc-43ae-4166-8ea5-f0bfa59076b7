import 'package:flutter/material.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/screens/website_launcher.dart';
import 'package:provider/provider.dart';

class TopBrandsList extends StatelessWidget {
  const TopBrandsList({super.key});

  @override
  Widget build(BuildContext context) {
    // Sample brand data - in a real app, this would come from an API or bloc
    final List<BrandItem> brands = [
      BrandItem(
        name: 'Amazon',
        logoUrl:
            'https://upload.wikimedia.org/wikipedia/commons/thumb/a/a9/Amazon_logo.svg/1024px-Amazon_logo.svg.png',
        websiteUrl: 'https://www.amazon.com',
      ),
      BrandItem(
        name: 'eBay',
        logoUrl:
            'https://upload.wikimedia.org/wikipedia/commons/thumb/1/1b/EBay_logo.svg/1200px-EBay_logo.svg.png',
        websiteUrl: 'https://www.ebay.com',
      ),
      BrandItem(
        name: '<PERSON><PERSON><PERSON>',
        logoUrl:
            'https://upload.wikimedia.org/wikipedia/commons/thumb/9/9a/Alibaba_Group_logo.svg/1280px-Alibaba_Group_logo.svg.png',
        websiteUrl: 'https://www.alibaba.com',
      ),
      BrandItem(
        name: 'AliExpress',
        logoUrl:
            'https://upload.wikimedia.org/wikipedia/commons/thumb/1/1a/AliExpress_logo.svg/1200px-AliExpress_logo.svg.png',
        websiteUrl: 'https://www.aliexpress.com',
      ),
      BrandItem(
        name: 'Walmart',
        logoUrl:
            'https://upload.wikimedia.org/wikipedia/commons/thumb/c/ca/Walmart_logo.svg/1200px-Walmart_logo.svg.png',
        websiteUrl: 'https://www.walmart.com',
      ),
      BrandItem(
        name: 'Target',
        logoUrl:
            'https://upload.wikimedia.org/wikipedia/commons/thumb/c/c5/Target_Corporation_logo_%28vector%29.svg/1200px-Target_Corporation_logo_%28vector%29.svg.png',
        websiteUrl: 'https://www.target.com',
      ),
      BrandItem(
        name: 'Best Buy',
        logoUrl:
            'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f5/Best_Buy_Logo.svg/1200px-Best_Buy_Logo.svg.png',
        websiteUrl: 'https://www.bestbuy.com',
      ),
      BrandItem(
        name: 'Etsy',
        logoUrl:
            'https://upload.wikimedia.org/wikipedia/commons/thumb/8/89/Etsy_logo.svg/1200px-Etsy_logo.svg.png',
        websiteUrl: 'https://www.etsy.com',
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 80,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 8),
            itemCount: brands.length,
            itemBuilder: (context, index) {
              return _buildBrandLogo(context, brands[index]);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildBrandLogo(BuildContext context, BrandItem brand) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(MaterialPageRoute(builder: (context) {
          return WebsiteLauncher(
            url: brand.websiteUrl,
            websiteName: brand.name,
            minimalDesign: true,
          );
        }));
      },
      child: Container(
        width: 80,
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: isDarkMode ? AppColors.boxDarkColor : Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                brand.logoUrl,
                width: 50,
                height: 50,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 50,
                    height: 50,
                    color: Colors.grey[200],
                    child: const Icon(
                      Icons.error_outline,
                      color: Colors.grey,
                      size: 20,
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 4),
            Text(
              brand.name,
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: isDarkMode ? Colors.white70 : Colors.black87,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class BrandItem {
  final String name;
  final String logoUrl;
  final String websiteUrl;

  BrandItem({
    required this.name,
    required this.logoUrl,
    required this.websiteUrl,
  });
}
