import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/auth/bloc/authentication_bloc.dart';
import 'package:goldenprizma/presentation/views/auth/login_screen.dart';

class AuthWrapper extends StatelessWidget {
  final Widget child;

  // If user is not authenticated and guestChild is provided, guestChild will be shown
  final Widget? guestChild;

  const AuthWrapper({
    super.key,
    required this.child,
    this.guestChild,
  });

  @override
  Widget build(BuildContext context) {
    if (context.read<AuthenticationBloc>().state.isAuthenticated) {
      return child;
    }

    return const LoginScreen();
  }
}
