import 'dart:ui';

import 'package:flutter/material.dart';

class GlassMorphism extends StatelessWidget {
  const GlassMorphism({
    super.key,
    required this.child,
    this.blur = 6,
    this.opacity = 0.2,
    this.borderRadius = 0,
  });

  final double blur;
  final Widget child;
  final double opacity;
  final double borderRadius;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(borderRadius),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: blur, sigmaY: blur),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: [
              Colors.white.withOpacity(0.2),
              Colors.white.withOpacity(0.05),
            ]),
          ),
          child: child,
        ),
      ),
    );
  }
}
