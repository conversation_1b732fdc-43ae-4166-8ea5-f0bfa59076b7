import 'package:flutter/material.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:ionicons/ionicons.dart';
import 'package:provider/provider.dart';

class Option {
  final String label;
  final dynamic value;

  Option({required this.label, required this.value});
}

class SelectWidget extends StatefulWidget {
  final List<Option> options;
  final bool searchable;
  final List selectedOptions;
  final String title;
  final bool isRemote;
  final Function? onChanged;
  final Future<List<Option>?> Function(String?)? onSearch;
  final Future<List<Option>?> Function()? onInit;

  const SelectWidget({
    super.key,
    required this.options,
    this.selectedOptions = const [],
    this.searchable = true,
    this.isRemote = true,
    this.title = '',
    this.onChanged,
    this.onSearch,
    this.onInit,
  });

  @override
  State<SelectWidget> createState() => _SelectWidgetState();
}

class _SelectWidgetState extends State<SelectWidget> {
  List _selectedItems = [];
  List _computedOptions = [];
  bool isLoading = false;

  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    _computedOptions = widget.options;
    _selectedItems = widget.selectedOptions;
    if (mounted && widget.options.isEmpty) {
      isLoading = true;
      widget.onInit!().then((value) {
        if (value != null && value.isNotEmpty && isLoading != false) {
          _computedOptions = value.toList();
        }
        setState(() {
          isLoading = false;
        });
      });
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Provider.of<AppProvider>(context).isDarkMode(context)
          ? AppColors.darkBodyColor
          : AppColors.bodyColor,
      appBar: AppBar(
        title: Text(widget.title),
        elevation: 0,
      ),
      body: Stack(
        children: [
          Column(children: [
            if (widget.searchable)
              Padding(
                padding: const EdgeInsetsDirectional.only(
                    top: 24, start: 16, end: 24, bottom: 8),
                child: _searchField(context),
              ),
            const SizedBox(height: 16),
            Expanded(
              child: isLoading
                  ? const Column(
                      children: [
                        Center(
                          child: CircularProgressIndicator.adaptive(),
                        )
                      ],
                    )
                  : ListView.separated(
                      itemCount: _computedOptions.length,
                      itemBuilder: (context, index) {
                        var option = _computedOptions[index];
                        bool isSelected = _selectedItems.contains(option.value);

                        return ListTile(
                          contentPadding: const EdgeInsetsDirectional.only(
                              end: 8, start: 16, top: 0),
                          title: Text(option.label),
                          tileColor: isSelected
                              ? Theme.of(context).canvasColor
                              : Theme.of(context).cardColor,
                          onTap: () {
                            if (_selectedItems.contains(option.value)) {
                              removeItem(option.value);
                            } else {
                              addItem(option.value);
                            }
                          },
                          trailing: IconButton(
                            icon: isSelected
                                ? Icon(Ionicons.checkbox_outline,
                                    color: Theme.of(context).primaryColor)
                                : const Icon(Ionicons.square_outline),
                            onPressed: () {
                              if (_selectedItems.contains(option.value)) {
                                removeItem(option.value);
                              } else {
                                addItem(option.value);
                              }
                            },
                          ),
                        );
                      },
                      separatorBuilder: (context, index) =>
                          const Divider(height: 2),
                    ),
            ),
          ]),
          Positioned(
              left: 20,
              right: 20,
              bottom: 0,
              child: SafeArea(
                child: MaterialButton(
                  minWidth: MediaQuery.of(context).size.width,
                  color: AppColors.primaryColor,
                  height: 50,
                  elevation: 0,
                  onPressed: () {
                    if (Navigator.of(context).canPop()) {
                      Navigator.of(context).pop();
                    }
                  },
                  child: const Text('OK'),
                ),
              ))
        ],
      ),
    );
  }

  bool addItem(dynamic value) {
    if (_selectedItems.contains(value)) {
      return false;
    }
    setState(() {
      _selectedItems.add(value);
    });
    var onChange = widget.onChanged;
    if (onChange != null) {
      onChange(_selectedItems);
    }
    return true;
  }

  bool removeItem(dynamic value) {
    if (!_selectedItems.contains(value)) {
      return false;
    }
    setState(() {
      _selectedItems.remove(value);
    });
    var onChange = widget.onChanged;
    if (onChange != null) {
      onChange(_selectedItems);
    }
    return true;
  }

  Widget _searchField(BuildContext context) {
    return TextField(
      controller: _searchController,
      autofocus: false,
      decoration: InputDecoration(
        contentPadding: EdgeInsets.zero,
        isDense: true,
        prefixIcon: const Icon(
          Icons.search,
          color: Colors.grey,
          size: 24,
        ),
        hintText: context.loc.search,
      ),
      onChanged: (value) {
        if (!widget.isRemote) {
          setState(() {
            _computedOptions = widget.options.where((element) {
              String label = element.label.toString().toLowerCase();
              return label.contains(value.toString().toLowerCase());
            }).toList();
          });
        }
        if (widget.onSearch != null) {
          setState(() {
            isLoading = true;
          });
          widget.onSearch!(value).then((value) {
            setState(() {
              if (value != null && value.isNotEmpty && isLoading != false) {
                _computedOptions = value.toList();
              }
              isLoading = false;
            });
          });
        }
      },
    );
  }
}
