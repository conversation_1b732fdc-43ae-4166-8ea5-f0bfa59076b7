import 'package:flutter/material.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:provider/provider.dart';
import 'package:lottie/lottie.dart'; // Add this to pubspec.yaml

class ProcessedOrdersCard extends StatelessWidget {
  final int? processedOrdersCount;
  final VoidCallback? onViewOrders;

  const ProcessedOrdersCard({
    required this.onViewOrders,
    super.key,
    this.processedOrdersCount = 0,
  });

  @override
  Widget build(BuildContext context) {
    if (processedOrdersCount == 0) {
      return const SizedBox.shrink();
    }

    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDarkMode ? Colors.grey[800]! : Colors.grey[300]!,
        ),
        color: isDarkMode ? AppColors.appbarDarkColor : Colors.white,
      ),
      child: Row(
        children: [
          // Lottie animation or icon on the left
          Container(
            width: 96,
            height: 96,
            margin: const EdgeInsetsDirectional.only(end: 12),
            child: Lottie.asset(
              'assets/processed_orders.json', // Replace with your Lottie file
              repeat: true,
            ),
          ),
          // Content on the right
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Title
                Text(
                  'Processed Orders',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 8),
                // Description
                Text(
                  'You have $processedOrdersCount processed orders waiting for your action.',
                  style: TextStyle(
                    color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 10),
                // Redirect Button
                Align(
                  alignment: Alignment.centerRight,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      backgroundColor: Theme.of(context).primaryColor,
                      elevation: 0,
                    ),
                    onPressed: onViewOrders,
                    child: const Text('View Orders'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
