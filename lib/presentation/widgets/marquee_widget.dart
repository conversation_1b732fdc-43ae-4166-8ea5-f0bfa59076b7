import 'package:flutter/material.dart';

class MarqueeWidget extends StatefulWidget {
  final Widget child;
  final double speed;
  final Color? backgroundColor;
  final EdgeInsetsGeometry? padding;
  final double? height;
  final double? width;
  final Axis direction;
  final double gap;
  final bool pauseOnHover;
  final bool enableManualScroll;

  const MarqueeWidget({
    super.key,
    required this.child,
    this.speed = 50.0,
    this.backgroundColor,
    this.padding,
    this.height,
    this.width,
    this.direction = Axis.horizontal,
    this.gap = 50.0,
    this.pauseOnHover = false,
    this.enableManualScroll = false,
  });

  @override
  State<MarqueeWidget> createState() => _MarqueeWidgetState();
}

class _MarqueeWidgetState extends State<MarqueeWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  late ScrollController _scrollController;
  bool _isPaused = false;
  bool _isUserInteracting = false;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _controller = AnimationController(
      duration: Duration(milliseconds: (10000 / widget.speed * 100).round()),
      vsync: this,
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setupAnimation();
    });
  }

  void _setupAnimation() {
    if (_scrollController.hasClients && mounted) {
      final maxScrollExtent = _scrollController.position.maxScrollExtent;

      if (maxScrollExtent > 0) {
        _animation = Tween<double>(
          begin: 0.0,
          end: maxScrollExtent,
        ).animate(CurvedAnimation(
          parent: _controller,
          curve: Curves.linear,
        ));

        _animation.addListener(() {
          if (_scrollController.hasClients &&
              mounted &&
              !_isPaused &&
              !_isUserInteracting) {
            _scrollController.jumpTo(_animation.value);
          }
        });

        _animation.addStatusListener((status) {
          if (status == AnimationStatus.completed && mounted) {
            _controller.reset();
            if (!_isPaused && !_isUserInteracting) {
              _controller.forward();
            }
          }
        });

        _controller.forward();
      }
    }
  }

  void _pauseAnimation() {
    if (widget.pauseOnHover && !_isPaused) {
      setState(() {
        _isPaused = true;
      });
      _controller.stop();
    }
  }

  void _resumeAnimation() {
    if (widget.pauseOnHover && _isPaused && !_isUserInteracting) {
      setState(() {
        _isPaused = false;
      });
      _controller.forward();
    }
  }

  void _onScrollStart(ScrollStartNotification notification) {
    if (widget.enableManualScroll) {
      _isUserInteracting = true;
      _controller.stop();
    }
  }

  void _onScrollEnd(ScrollEndNotification notification) {
    if (widget.enableManualScroll) {
      // Resume auto-scroll after a short delay
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _isUserInteracting = false;
          if (!_isPaused) {
            // Sync animation with current scroll position
            _syncAnimationWithScroll();
            _controller.forward();
          }
        }
      });
    }
  }

  void _syncAnimationWithScroll() {
    if (_scrollController.hasClients && _animation.isCompleted == false) {
      final currentPosition = _scrollController.position.pixels;
      final maxScrollExtent = _scrollController.position.maxScrollExtent;

      if (maxScrollExtent > 0) {
        final progress = currentPosition / maxScrollExtent;
        _controller.value = progress;
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _pauseAnimation(),
      onExit: (_) => _resumeAnimation(),
      child: Container(
        height: widget.height,
        width: widget.width,
        color: widget.backgroundColor,
        padding: widget.padding,
        child: NotificationListener<ScrollNotification>(
          onNotification: (ScrollNotification notification) {
            if (notification is ScrollStartNotification) {
              _onScrollStart(notification);
            } else if (notification is ScrollEndNotification) {
              _onScrollEnd(notification);
            }
            return false;
          },
          child: widget.direction == Axis.horizontal
              ? _buildHorizontalMarquee()
              : _buildVerticalMarquee(),
        ),
      ),
    );
  }

  Widget _buildHorizontalMarquee() {
    return SingleChildScrollView(
      controller: _scrollController,
      scrollDirection: Axis.horizontal,
      physics: widget.enableManualScroll
          ? const BouncingScrollPhysics()
          : const NeverScrollableScrollPhysics(),
      child: Row(
        children: [
          widget.child,
          SizedBox(width: widget.gap),
          widget.child,
          SizedBox(width: widget.gap),
          widget.child,
          SizedBox(width: widget.gap),
        ],
      ),
    );
  }

  Widget _buildVerticalMarquee() {
    return SingleChildScrollView(
      controller: _scrollController,
      scrollDirection: Axis.vertical,
      physics: widget.enableManualScroll
          ? const BouncingScrollPhysics()
          : const NeverScrollableScrollPhysics(),
      child: Column(
        children: [
          widget.child,
          SizedBox(height: widget.gap),
          widget.child,
          SizedBox(height: widget.gap),
          widget.child,
          SizedBox(height: widget.gap),
        ],
      ),
    );
  }
}
