import 'package:flutter/material.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/helpers/app_settings.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:lottie/lottie.dart';

bool _isBottomSheetOpen = false;

void showProcessedOrderBottomSheet({
  required BuildContext context,
  required int processedOrders,
  required Function() onPressed,
}) {
  if (_isBottomSheetOpen) return; // Prevent multiple openings

  _isBottomSheetOpen = true; // Set flag

  showModalBottomSheet(
    context: context,
    isDismissible: true,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
    ),
    builder: (context) {
      return PopScope(
        // Ensure flag resets when closed
        onPopInvokedWithResult: (bool didPop, Object? result) {
          if (didPop) {
            _isBottomSheetOpen = false;
          }
        },
        child: DraggableScrollableSheet(
          expand: false,
          initialChildSize: 0.3,
          minChildSize: 0.2,
          maxChildSize: 0.5,
          builder: (context, scrollController) {
            return SingleChildScrollView(
              controller: scrollController,
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const Icon(Icons.drag_handle, size: 30, color: Colors.grey),
                  Container(
                    width: 96,
                    height: 96,
                    margin: const EdgeInsetsDirectional.only(end: 12),
                    child: Lottie.asset(
                      'assets/processed_orders.json',
                      repeat: false,
                    ),
                  ),
                  Text(
                    context.loc.processedOrders,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(context.loc.processedOrdersMessage(processedOrders)),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(elevation: 0),
                    onPressed: () {
                      Navigator.of(context).pop();
                      Future.delayed(const Duration(milliseconds: 100), () {
                        _isBottomSheetOpen = false; // Reset flag after closing
                        onPressed();
                      });
                    },
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(context.loc.viewOrders),
                        const SizedBox(width: 6),
                        Icon(
                          AppSettings.isRtl(context)
                              ? HugeIcons.strokeRoundedArrowLeft02
                              : HugeIcons.strokeRoundedArrowRight02,
                          size: 18,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      );
    },
  ).whenComplete(() => _isBottomSheetOpen = false); // Ensure flag resets
}
