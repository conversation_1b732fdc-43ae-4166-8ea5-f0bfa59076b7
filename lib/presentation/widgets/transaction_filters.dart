// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/accounting/bloc/transaction_filter_bloc.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:intl/intl.dart';
import 'package:ionicons/ionicons.dart';

class TransactionFilters extends StatelessWidget {
  const TransactionFilters({super.key});

  @override
  Widget build(BuildContext context) {
    return Material(
      clipBehavior: Clip.antiAlias,
      borderRadius: BorderRadius.circular(12),
      child: Safe<PERSON><PERSON>(
        top: false,
        child: Padding(
          padding: const EdgeInsets.only(
            top: 32.0,
            left: 16.0,
            right: 16.0,
            bottom: 16.0,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(context.loc.transactionType,
                  style: Theme.of(context).textTheme.bodySmall),
              const SizedBox(height: 4.0),
              _typeFilter(),
              const SizedBox(height: 16.0),
              Text(context.loc.date,
                  style: Theme.of(context).textTheme.bodySmall),
              const SizedBox(height: 4.0),
              _dateFilter(),
              const SizedBox(height: 48.0),
              _clearFilterButton()
            ],
          ),
        ),
      ),
    );
  }

  _typeFilter() {
    return BlocBuilder<TransactionFilterBloc, TransactionFilterState>(
      builder: (context, state) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            shape: BoxShape.rectangle,
            color: Theme.of(context).inputDecorationTheme.fillColor,
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: DropdownButton<String>(
            value: state.transactionType,
            isExpanded: true,
            underline: const SizedBox(),
            onChanged: (String? newValue) {
              context.read<TransactionFilterBloc>().add(
                    TransactionFilterTypeChanged(
                        transactionType: newValue ?? state.transactionType),
                  );
            },
            items: <String>['All', 'Cash-In', 'Cash-Out']
                .map<DropdownMenuItem<String>>((String value) {
              return DropdownMenuItem<String>(
                value: value,
                child: Text(value),
              );
            }).toList(),
          ),
        );
      },
    );
  }

  _dateFilter() {
    return BlocBuilder<TransactionFilterBloc, TransactionFilterState>(
      builder: (context, state) {
        return GestureDetector(
          onTap: () async {
            DateTimeRange? dateTime = await showDateRangePicker(
              context: context,
              initialDateRange: (state.createdAt?.start != null &&
                      state.createdAt?.end != null)
                  ? DateTimeRange(
                      start: state.createdAt?.start ?? DateTime(2021),
                      end: state.createdAt?.end ?? DateTime.now(),
                    )
                  : null,
              firstDate: DateTime(2021),
              lastDate: DateTime.now(),
            );

            context.read<TransactionFilterBloc>().add(
                  TransactionFilterDateChanged(
                    createdAt: dateTime,
                  ),
                );
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            height: 50,
            decoration: BoxDecoration(
              shape: BoxShape.rectangle,
              color: Theme.of(context).inputDecorationTheme.fillColor,
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: Row(
              children: [
                Expanded(
                  child: state.createdAt?.start != null
                      ? Text(
                          '${DateFormat('y-MM-dd').format(state.createdAt!.start)} / ${DateFormat('y-MM-dd').format(state.createdAt!.end)}',
                        )
                      : const SizedBox(),
                ),
                const Icon(Ionicons.calendar)
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _clearFilterButton() {
    return BlocBuilder<TransactionFilterBloc, TransactionFilterState>(
      builder: (context, state) {
        if (state.createdAt == null) {
          return const SizedBox();
        }
        return Center(
          child: MaterialButton(
              onPressed: () {
                context.read<TransactionFilterBloc>()
                  ..add(const TransactionFilterDateChanged(createdAt: null))
                  ..add(const TransactionFilterTypeChanged(
                      transactionType: 'All'));
              },
              child: Text(context.loc.clearFilters)),
        );
      },
    );
  }
}
