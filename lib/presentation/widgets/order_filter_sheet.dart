import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/orders/bloc/order_bloc.dart';
import 'package:goldenprizma/domain/orders/models/filter_brand_model.dart';
import 'package:goldenprizma/domain/orders/models/filter_website_model.dart';
import 'package:goldenprizma/domain/orders/models/order_query.dart';
import 'package:goldenprizma/domain/orders/models/size_model.dart';
import 'package:goldenprizma/helpers/app_settings.dart';
import 'package:goldenprizma/main.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/widgets/select_widget.dart';
import 'package:goldenprizma/repositories/filter_brand_repository.dart';
import 'package:goldenprizma/repositories/filter_size_repository.dart';
import 'package:goldenprizma/repositories/filter_website_repository.dart';
import 'package:ionicons/ionicons.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';

class OrderFilterSheet extends StatefulWidget {
  const OrderFilterSheet({super.key});

  @override
  State<OrderFilterSheet> createState() => _OrderFilterSheetState();
}

class _OrderFilterSheetState extends State<OrderFilterSheet> {
  List<Option> _sizes = [];
  List<Option> _brands = [];
  List<Option> _websites = [];

  final FilterSizeRepository _sizeRepository =
      getIt.get<FilterSizeRepository>();
  final FilterBrandRepository _brandRepository =
      getIt.get<FilterBrandRepository>();
  final FilterWebsiteRepository _websiteRepository =
      getIt.get<FilterWebsiteRepository>();

  @override
  void initState() {
    if (mounted) {
      loadFilterData();
    }
    super.initState();
  }

  void loadFilterData() {
    _sizeRepository.getAll().then((value) {
      setState(() {
        _sizes = mapToOption(value);
      });
    });

    _brandRepository.getAll().then((value) {
      setState(() {
        _brands = mapToOption(value);
      });
    });

    _websiteRepository.getAll().then((value) {
      setState(() {
        _websites = mapToOption(value);
      });
    });
  }

  List<Option> mapToOption(List list) {
    return list.map((e) => Option(label: e.name, value: e.id)).toList();
  }

  @override
  Widget build(BuildContext context) {
    var statuses = _statuses(context);

    return BlocBuilder<OrderBloc, OrderState>(
      builder: (blocContext, state) {
        return Scaffold(
          body: SafeArea(
              bottom: true,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                      child: SizedBox(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: Text(
                            'Filters',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                        ),
                        const SizedBox(height: 24),
                        ListTile(
                          title: const Text('Status'),
                          contentPadding: const EdgeInsetsDirectional.only(
                              start: 16, end: 0),
                          subtitle: Container(
                            margin: const EdgeInsets.symmetric(vertical: 6),
                            width: MediaQuery.of(context).size.width,
                            height: 46,
                            child: ListView.separated(
                              scrollDirection: Axis.horizontal,
                              itemCount: statuses.length,
                              itemBuilder: (_, index) {
                                String status = statuses[index]['label'];
                                String? statusValue = statuses[index]['value'];

                                bool isSelected =
                                    state.query.statuses.contains(statusValue);
                                if (state.query.statuses.isEmpty &&
                                    statusValue == null) {
                                  isSelected = true;
                                }

                                return ChoiceChip(
                                  label: Text(status),
                                  selectedColor: AppColors.primaryColor,
                                  visualDensity:
                                      const VisualDensity(vertical: -2),
                                  backgroundColor: Theme.of(context)
                                      .appBarTheme
                                      .backgroundColor,
                                  labelStyle: TextStyle(
                                      color: isSelected
                                          ? Colors.white
                                          : Theme.of(context)
                                              .textTheme
                                              .bodyLarge!
                                              .color),
                                  shadowColor: Colors.black,
                                  elevation: 0.5,
                                  shape: RoundedRectangleBorder(
                                      side: BorderSide(
                                        color: isSelected
                                            ? Colors.transparent
                                            : Theme.of(context).dividerColor,
                                      ),
                                      borderRadius: BorderRadius.circular(6)),
                                  selected: isSelected,
                                  onSelected: (value) {
                                    if (value && statusValue == null) {
                                      context
                                          .read<OrderBloc>()
                                          .add(OrderQueryChanged(
                                            query: state.query
                                                .copyWith(statuses: const []),
                                          ));
                                    } else {
                                      context.read<OrderBloc>().add(
                                          OrderQueryChanged(
                                              query: state.query.toggleStatus(
                                                  "$statusValue")));
                                    }
                                  },
                                );
                              },
                              separatorBuilder: (context, index) =>
                                  const SizedBox(width: 8),
                            ),
                          ),
                        ),
                        const Divider(),
                        ListTile(
                          title: const Text('Websites'),
                          trailing: Icon(AppSettings.isRtl(context)
                              ? Ionicons.chevron_back_outline
                              : Ionicons.chevron_forward_outline),
                          onTap: () => showCupertinoModalBottomSheet(
                            expand: false,
                            context: context,
                            backgroundColor: Colors.black,
                            builder: (context) => SelectWidget(
                              options: _websites,
                              isRemote: true,
                              onInit: () async {
                                List<FilterWebsiteModel> websites =
                                    await _websiteRepository.getAll(
                                        ignoreCache:
                                            state.query.websiteIds.isEmpty);
                                return mapToOption(websites);
                              },
                              onSearch: (String? search) async {
                                List<FilterWebsiteModel> websites =
                                    await _websiteRepository.getAll(
                                        filter: "$search");
                                return mapToOption(websites);
                              },
                              onChanged: (List selectedItems) {
                                var items =
                                    selectedItems.map((e) => e as int).toList();
                                blocContext
                                    .read<OrderBloc>()
                                    .add(OrderQueryChanged(
                                      query: state.query
                                          .copyWith(websiteIds: items),
                                    ));
                              },
                              selectedOptions:
                                  List.from(state.query.websiteIds),
                              title: 'Websites',
                            ),
                          ),
                        ),
                        const Divider(),
                        ListTile(
                          title: const Text('Brands'),
                          trailing: Icon(AppSettings.isRtl(context)
                              ? Ionicons.chevron_back_outline
                              : Ionicons.chevron_forward_outline),
                          onTap: () => showCupertinoModalBottomSheet(
                            expand: false,
                            context: context,
                            backgroundColor: Colors.black,
                            builder: (context) => SelectWidget(
                              options: _brands,
                              isRemote: true,
                              onInit: () async {
                                List<FilterBrandModel> brands =
                                    await _brandRepository.getAll(
                                        ignoreCache:
                                            state.query.brandIds.isEmpty);
                                return mapToOption(brands);
                              },
                              onSearch: (String? search) async {
                                List<FilterBrandModel> brands =
                                    await _brandRepository.getAll(
                                        filter: "$search");
                                return mapToOption(brands);
                              },
                              onChanged: (List selectedItems) {
                                var items =
                                    selectedItems.map((e) => e as int).toList();
                                blocContext
                                    .read<OrderBloc>()
                                    .add(OrderQueryChanged(
                                      query:
                                          state.query.copyWith(brandIds: items),
                                    ));
                              },
                              selectedOptions: List.from(state.query.brandIds),
                              title: 'Brands',
                            ),
                          ),
                        ),
                        const Divider(),
                        ListTile(
                          title: const Text('Sizes'),
                          trailing: Icon(AppSettings.isRtl(context)
                              ? Ionicons.chevron_back_outline
                              : Ionicons.chevron_forward_outline),
                          onTap: () => showCupertinoModalBottomSheet(
                            expand: false,
                            context: context,
                            backgroundColor: Colors.black,
                            builder: (context) => SelectWidget(
                              options: _sizes,
                              isRemote: true,
                              onInit: () async {
                                List<SizeModel> sizes =
                                    await _sizeRepository.getAll(
                                        ignoreCache:
                                            state.query.sizeIds.isEmpty);
                                return mapToOption(sizes);
                              },
                              onSearch: (String? search) async {
                                List<SizeModel> sizes = await _sizeRepository
                                    .getAll(filter: "$search");
                                return mapToOption(sizes);
                              },
                              onChanged: (List selectedItems) {
                                var items =
                                    selectedItems.map((e) => e as int).toList();
                                blocContext
                                    .read<OrderBloc>()
                                    .add(OrderQueryChanged(
                                      query:
                                          state.query.copyWith(sizeIds: items),
                                    ));
                              },
                              selectedOptions: List.from(state.query.sizeIds),
                              title: 'Sizes',
                            ),
                          ),
                        ),
                      ],
                    ),
                  )),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Flexible(
                          flex: 2,
                          child: MaterialButton(
                            elevation: 0,
                            minWidth: double.infinity,
                            color: AppColors.primaryColor,
                            onPressed: () {
                              context.read<OrderBloc>()
                                ..add(OrderQueryChanged(query: state.query))
                                ..add(OrderReload(query: state.query));

                              Navigator.of(context).pop();
                            },
                            child: const Text('Apply filters'),
                          ),
                        ),
                        Flexible(
                          child: MaterialButton(
                            minWidth: double.infinity,
                            onPressed: () {
                              var query = const OrderQuery();
                              context.read<OrderBloc>()
                                ..add(OrderQueryChanged(query: query))
                                ..add(OrderReload(query: query));
                              Navigator.of(context).pop();
                            },
                            child: const Text('Reset'),
                          ),
                        )
                      ],
                    ),
                  ),
                ],
              )),
        );
      },
    );
  }

  List _statuses(BuildContext context) {
    return OrderQuery.statusList(context);
  }
}
