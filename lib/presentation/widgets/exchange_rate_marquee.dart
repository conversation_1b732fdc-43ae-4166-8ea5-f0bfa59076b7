import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/home/<USER>/home_bloc.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/widgets/marquee_widget.dart';
import 'package:provider/provider.dart';
import 'package:goldenprizma/domain/exchange_rates/models/exchange_rate.dart';

class ExchangeRateMarquee extends StatelessWidget {
  const ExchangeRateMarquee({super.key});

  @override
  Widget build(BuildContext context) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) {
        if (state.status == HomeStatus.loading) {
          return _buildLoadingWidget(isDarkMode);
        } else if (state.status == HomeStatus.success) {
          return _buildSuccessWidget(state, isDarkMode, context);
        } else if (state.status == HomeStatus.failure) {
          return _buildErrorWidget(isDarkMode);
        } else {
          return _buildLoadingWidget(isDarkMode);
        }
      },
    );
  }

  Widget _buildLoadingWidget(bool isDarkMode) {
    return Container(
      width: double.infinity,
      height: 36,
      decoration: BoxDecoration(
        color: isDarkMode ? AppColors.boxDarkColor : Colors.grey[50],
        border: Border(
          bottom: BorderSide(
            color: isDarkMode ? Colors.grey[800]! : Colors.grey[200]!,
            width: 1,
          ),
        ),
      ),
      child: const Center(
        child: SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      ),
    );
  }

  Widget _buildSuccessWidget(
      HomeState state, bool isDarkMode, BuildContext context) {
    if (state.homeData.exchangeRates.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      height: 36,
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: isDarkMode ? Colors.grey[900]! : Colors.grey[200]!,
            width: 1,
          ),
        ),
      ),
      child: MarqueeWidget(
        speed: 25,
        gap: 0,
        child: Row(
          children: state.homeData.exchangeRates
              .map((rate) => _buildRateItemFromExchangeRate(rate, isDarkMode))
              .toList(),
        ),
      ),
      // child: Marquee(
      //   direction: Axis.horizontal,
      //   animationDuration: const Duration(seconds: 20),
      //   backDuration: const Duration(milliseconds: 5000),
      //   pauseDuration: const Duration(milliseconds: 2500),
      //   directionMarguee: DirectionMarguee.TwoDirection,
      //   child: Row(
      //     children: state.homeData.exchangeRates
      //         .map((rate) => _buildRateItemFromExchangeRate(rate, isDarkMode))
      //         .toList(),
      //   ),
      // ),
    );
  }

  Widget _buildErrorWidget(bool isDarkMode) {
    return Container(
      width: double.infinity,
      height: 36,
      decoration: BoxDecoration(
        color: isDarkMode ? AppColors.boxDarkColor : Colors.grey[50],
        border: Border(
          bottom: BorderSide(
            color: isDarkMode ? Colors.grey[800]! : Colors.grey[200]!,
            width: 1,
          ),
        ),
      ),
      child: Center(
        child: Text(
          'Exchange rates temporarily unavailable',
          style: TextStyle(
            fontSize: 12,
            color: isDarkMode ? Colors.white60 : Colors.black54,
          ),
        ),
      ),
    );
  }

  Widget _buildRateItemFromExchangeRate(ExchangeRate rate, bool isDarkMode) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      child: Row(
        children: [
          Text(
            rate.toCurreny, // Using toCurreny from ExchangeRate model
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 13,
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(width: 6),
          Text(
            rate.formattedRate, // Using formattedRate from ExchangeRate model
            style: TextStyle(
              fontSize: 13,
              color: isDarkMode ? Colors.white70 : Colors.black54,
            ),
          ),
        ],
      ),
    );
  }
}

class ExchangeRateItem {
  final String currencyCode;
  final String currencyName;
  final double rate;
  final double change;

  ExchangeRateItem({
    required this.currencyCode,
    required this.currencyName,
    required this.rate,
    required this.change,
  });
}
