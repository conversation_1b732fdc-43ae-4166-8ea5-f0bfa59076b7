import 'package:flutter/material.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:webview_flutter/webview_flutter.dart';

class NavigationControls extends StatelessWidget {
  const NavigationControls({required this.controller, super.key});

  final WebViewController controller;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        IconButton(
          padding: const EdgeInsets.all(0),
          visualDensity: const VisualDensity(horizontal: -2, vertical: -2),
          splashRadius: 20,
          tooltip: "Go back",
          icon: const Icon(HugeIcons.strokeRoundedArrowLeft02),
          onPressed: () async {
            if (await controller.canGoBack()) {
              controller.goBack();
            }
          },
        ),
        IconButton(
          padding: const EdgeInsets.all(0),
          visualDensity: const VisualDensity(horizontal: -2, vertical: -2),
          splashRadius: 20,
          tooltip: "Go Forward",
          icon: const Icon(HugeIcons.strokeRoundedArrowRight02),
          onPressed: () async {
            if (await controller.canGoForward()) {
              controller.goForward();
            }
          },
        ),
      ],
    );
  }
}
