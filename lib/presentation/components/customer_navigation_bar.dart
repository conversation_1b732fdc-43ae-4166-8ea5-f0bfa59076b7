import 'package:flutter/material.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:provider/provider.dart';

class CustomNavigationBar extends StatelessWidget {
  final int index;
  final void Function(int)? onChange;

  const CustomNavigationBar({
    super.key,
    required this.index,
    required this.onChange,
  });

  @override
  Widget build(BuildContext context) {
    return BottomAppBar(
      elevation: 0,
      padding: const EdgeInsets.only(left: 16, right: 16, top: 6, bottom: 6),
      child: SizedBox(
        height: 56,
        child: LayoutBuilder(
          builder: (context, constraints) {
            // Calculate optimal button width based on screen size
            final screenWidth = constraints.maxWidth;
            final buttonWidth = (screenWidth / 5).clamp(60.0, 80.0);

            return Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                SizedBox(
                  width: buttonWidth,
                  child: _navigationButton(
                    context: context,
                    icon: HugeIcons.strokeRoundedHome01,
                    onPress: () {
                      onChange!(0);
                    },
                    label: context.loc.home,
                    currentIndex: index,
                    index: 0,
                  ),
                ),
                SizedBox(
                  width: buttonWidth,
                  child: _navigationButton(
                    context: context,
                    icon: HugeIcons.strokeRoundedShoppingBag01,
                    onPress: () {
                      onChange!(1);
                    },
                    label: context.loc.store,
                    currentIndex: index,
                    index: 1,
                  ),
                ),
                SizedBox(
                  width: buttonWidth,
                  child: _navigationButton(
                    context: context,
                    icon: HugeIcons.strokeRoundedGlobe02,
                    onPress: () {
                      onChange!(2);
                    },
                    label: context.loc.websites,
                    currentIndex: index,
                    index: 2,
                  ),
                ),
                SizedBox(
                  width: buttonWidth,
                  child: _navigationButton(
                    context: context,
                    icon: HugeIcons.strokeRoundedTask01,
                    onPress: () {
                      onChange!(3);
                    },
                    label: context.loc.myOrders,
                    currentIndex: index,
                    index: 3,
                  ),
                ),
                SizedBox(
                  width: buttonWidth,
                  child: _navigationButton(
                    context: context,
                    icon: HugeIcons.strokeRoundedUser,
                    onPress: () {
                      onChange!(4);
                    },
                    label: context.loc.account,
                    currentIndex: index,
                    index: 4,
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _navigationButton({
    required IconData icon,
    required String label,
    required void Function()? onPress,
    required int currentIndex,
    required int index,
    required context,
  }) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);
    return MaterialButton(
      highlightColor: Colors.transparent,
      splashColor: Colors.transparent,
      onPressed: onPress,
      minWidth: 0, // Remove fixed minWidth to allow flexibility
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
      textColor: currentIndex == index
          ? AppColors.primaryColor
          : isDarkMode
              ? Colors.white60
              : Colors.black54,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 24.0,
          ),
          const SizedBox(height: 2),
          Flexible(
            child: Text(
              label,
              style: const TextStyle(fontSize: 10),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ],
      ),
    );
  }
}
