import 'package:flutter/material.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:provider/provider.dart';

class AppBottomNavigationBar extends StatelessWidget {
  final int index;
  final void Function(int)? onChange;

  const AppBottomNavigationBar({
    super.key,
    required this.index,
    required this.onChange,
  });

  @override
  Widget build(BuildContext context) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    return NavigationBarTheme(
      data: NavigationBarThemeData(
        backgroundColor:
            isDarkMode ? AppColors.appbarDarkColor : AppColors.appbarLightColor,
        indicatorColor: AppColors.primaryColor.withOpacity(0.3),
        labelTextStyle: WidgetStateProperty.all(
          TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.normal,
              color: isDarkMode ? Colors.white60 : Colors.black),
        ),
        iconTheme: WidgetStateProperty.all(
            IconThemeData(color: isDarkMode ? Colors.white60 : Colors.black)),
      ),
      child: NavigationBar(
        destinations: <Widget>[
          NavigationDestination(
              icon: const Icon(Icons.home_rounded), label: context.loc.home),
          NavigationDestination(
              icon: const Icon(Icons.link), label: context.loc.websites),
          NavigationDestination(
              icon: const Icon(Icons.add_box_outlined),
              label: context.loc.newOrder),
          NavigationDestination(
              icon: const Icon(Icons.store_outlined), label: context.loc.store),
          NavigationDestination(
              icon: const Icon(Icons.shopping_bag_outlined),
              label: context.loc.myOrders),
        ],
        selectedIndex: index,
        // unselectedItemColor:
        //     MediaQuery.of(context).platformBrightness == Brightness.dark
        //         ? Colors.white
        //         : Colors.grey.shade500,
        onDestinationSelected: onChange,
      ),
    );
  }
}
