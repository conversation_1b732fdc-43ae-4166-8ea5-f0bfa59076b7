import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:goldenprizma/domain/orders/bloc/order_approval_bloc.dart';
import 'package:goldenprizma/domain/orders/bloc/order_bloc.dart';
import 'package:goldenprizma/domain/orders/models/rejection_reason.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/helpers/custom_snackbars.dart';
import 'package:goldenprizma/helpers/extract_links.dart';
import 'package:goldenprizma/helpers/remove_links.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/pages/store/widgets/product_image_viewer.dart';
import 'package:goldenprizma/presentation/views/screens/order_detail_screen.dart';
import 'package:goldenprizma/presentation/views/screens/website_launcher.dart';
import 'package:ionicons/ionicons.dart';
import 'package:provider/provider.dart';

const Map<String, Color> statusColors = {
  "created": Color.fromARGB(255, 56, 191, 248),
  "item-rejected": Color.fromARGB(255, 210, 93, 93),
  "reject": Color.fromARGB(255, 210, 93, 93),
  "rejected": Color.fromARGB(255, 210, 93, 93),
  "canceled": Color.fromARGB(255, 210, 93, 93),
  "damaged-item": Color.fromARGB(255, 210, 93, 93),
  "wrong-item": Color.fromARGB(255, 210, 93, 93),
  "ignored": Color.fromARGB(255, 210, 93, 93),
  "returned": Color.fromARGB(255, 210, 93, 93),
  "refunded": Color.fromARGB(255, 8, 205, 103),
  "completed": Color.fromARGB(255, 8, 205, 103),
};

Color getStatusColor(String status) {
  return statusColors[status] ?? const Color.fromARGB(255, 56, 189, 248);
}

class OrderCard extends StatefulWidget {
  final String? size;
  final int quantity;
  final String price;
  final String image;
  final String status;
  final int id;
  final bool showAcceptReject;
  final bool hasStatusReason;
  final bool showPrice;
  final double shippingPrice;
  final List<RejectionReason> rejectionReasons;
  final bool canConfirmAgain;

  const OrderCard({
    super.key,
    required this.image,
    required this.status,
    required this.quantity,
    required this.price,
    required this.id,
    this.shippingPrice = 0,
    this.showAcceptReject = false,
    this.hasStatusReason = false,
    this.showPrice = false,
    this.size,
    this.rejectionReasons = const [],
    this.canConfirmAgain = false,
  });

  @override
  State<OrderCard> createState() => _OrderCardState();
}

class _OrderCardState extends State<OrderCard> {
  @override
  Widget build(BuildContext context) {
    const verticalSpace = SizedBox(height: 6);
    return BlocBuilder<OrderApprovalBloc, OrderApprovalState>(
      builder: (context, state) {
        return Container(
          height: 130,
          color: Theme.of(context).cardColor,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _networkImage(),
              const SizedBox(width: 16),
              _orderDetails(verticalSpace, context,
                  context.read<OrderApprovalBloc>(), context.read<OrderBloc>()),
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _detailButton(context, context.read<OrderBloc>()),
                  if (widget.hasStatusReason)
                    _showReasonButton(context, context.read<OrderBloc>()),
                ],
              )
            ],
          ),
        );
      },
    );
  }

  Widget _status() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: getStatusColor(widget.status.toLowerCase()),
          ),
          child: Text(
            widget.status.toUpperCase(),
            style: const TextStyle(
                color: Colors.white, fontSize: 11, letterSpacing: 0.5),
          ),
        ),
        if (widget.status.toLowerCase() == 'created') const SizedBox(width: 6),
        if (widget.status.toLowerCase() == 'created')
          Text(
            context.loc.orderProcessingHelper,
            style: const TextStyle(fontSize: 10, color: Colors.red),
            overflow: TextOverflow.fade,
          ),
        // InkWell(
        //   onTap: () {
        //     showCupertinoModalBottomSheet(
        //       expand: false,
        //       context: context,
        //       builder: (context) => Material(
        //         child: Container(
        //           padding: const EdgeInsets.all(20),
        //           constraints: BoxConstraints(
        //               maxHeight: MediaQuery.sizeOf(context).height - 200),
        //           child: Column(
        //             mainAxisSize: MainAxisSize.min,
        //             children: [
        //               ListView.separated(
        //                 padding: EdgeInsets.zero,
        //                 shrinkWrap: true,
        //                 separatorBuilder: (context, index) {
        //                   return const Divider();
        //                 },
        //                 itemCount: 1,
        //                 itemBuilder: (context, index) {
        //                   return Column(
        //                     mainAxisAlignment: MainAxisAlignment.start,
        //                     crossAxisAlignment: CrossAxisAlignment.start,
        //                     mainAxisSize: MainAxisSize.min,
        //                     children: [
        //                       const Text(
        //                           "Your order was rejected beacuse of price order was rejected beacuse of price"),
        //                       const SizedBox(height: 5),
        //                       Text(DateTime.now().toIso8601String())
        //                     ],
        //                   );
        //                 },
        //               )
        //             ],
        //           ),
        //         ),
        //       ),
        //     );
        //   },
        //   child: Padding(
        //     padding: const EdgeInsetsDirectional.only(start: 8),
        //     child: Text(
        //       "show".toUpperCase(),
        //       style: const TextStyle(color: Colors.red),
        //     ),
        //   ),
        // )
      ],
    );
  }

  Widget _detailButton(BuildContext context, OrderBloc orderBloc) {
    return OutlinedButton(
      style: OutlinedButton.styleFrom(
        backgroundColor: Provider.of<AppProvider>(context).isDarkMode(context)
            ? AppColors.darkBodyColor
            : Colors.grey[50],
        padding: EdgeInsets.zero,
        visualDensity: const VisualDensity(vertical: -3),
        side: const BorderSide(color: Colors.transparent),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      onPressed: () {
        Navigator.of(context)
            .push(OrderDetailScreen.routePage(widget.id, orderBloc: orderBloc));
      },
      child: Text(
        context.loc.details.toUpperCase(),
        style: const TextStyle(
            color: AppColors.primaryColor, fontSize: 11, letterSpacing: 0.1),
      ),
    );
  }

  Widget _showReasonButton(BuildContext context, OrderBloc orderBloc) {
    // question mark icon
    return OutlinedButton(
      style: OutlinedButton.styleFrom(
        padding: EdgeInsets.zero,
        visualDensity: const VisualDensity(vertical: -3),
        side: const BorderSide(color: Colors.transparent),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      onPressed: () {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(context.loc.reason),
            elevation: 1,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: widget.rejectionReasons.map(
                  (reason) {
                    List<String> links = extractLinks(reason.reason);
                    if (links.isNotEmpty) {
                      String reasonWithoutLinks = removeLinks(reason.reason);
                      List<Widget> children = List.of(
                        [
                          Text(reasonWithoutLinks.trim()),
                        ],
                      )..addAll(
                          links
                              .map(
                                (link) => InkWell(
                                  onTap: () {
                                    Navigator.of(context).push(
                                        MaterialPageRoute(builder: (context) {
                                      return WebsiteLauncher(
                                        url: link,
                                        websiteName: "Link",
                                      );
                                    }));
                                  },
                                  child: Text(
                                    link,
                                    style: const TextStyle(
                                      color: Colors.blue,
                                      decoration: TextDecoration.underline,
                                    ),
                                  ),
                                ),
                              )
                              .toList(),
                        );
                      return ListTile(
                        title: Text(reason.date,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  color: AppColors.primaryColor,
                                )),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: children,
                        ),
                      );
                    }

                    return ListTile(
                      title: Text(reason.date,
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: AppColors.primaryColor,
                                  )),
                      subtitle: Text(
                        reason.reason,
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    );
                  },
                ).toList(),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context, rootNavigator: true).pop();
                },
                child: Text(context.loc.close),
              )
            ],
          ),
        );
      },
      child: Icon(
        Ionicons.help_circle_outline,
        color: Theme.of(context).textTheme.bodySmall?.color,
        size: 22,
      ),
    );
  }

  Expanded _orderDetails(
    SizedBox verticalSpace,
    BuildContext context,
    OrderApprovalBloc approvalBloc,
    OrderBloc orderBloc,
  ) {
    return Expanded(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Table(
            columnWidths: const <int, TableColumnWidth>{
              0: FixedColumnWidth(48),
            },
            children: [
              _dataRow(context.loc.idLable, widget.id.toString(), context),
              _dataRow(context.loc.price, widget.price, context,
                  isPrice: true, showPrice: widget.showPrice),
              _dataRow(context.loc.qty, widget.quantity.toString(), context),
              _dataRow(context.loc.size, widget.size.toString(), context),
            ],
          ),
          widget.showAcceptReject
              ? _buildAceptOrRejectButton(
                  context,
                  widget.price,
                  widget.id,
                  approvalBloc,
                  orderBloc,
                )
              : _status(),
        ],
      ),
    );
  }

  Widget _networkImage() {
    // we will not load images
    // since they are not found in staging and local servers.
    // if (kDebugMode) {
    //   return const SizedBox(width: 130, height: 130, child: Icon(Icons.error));
    // }
    return GestureDetector(
      onTap: () {
        ProductImageViewer.show(
          context: context,
          imageUrl: widget.image,
          productName: '',
        );
      },
      child: Container(
        width: 130,
        height: 130,
        padding: const EdgeInsets.all(6),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(
            color: Theme.of(context).dividerColor,
            width: 1,
            style: BorderStyle.solid,
          ),
          borderRadius: BorderRadius.circular(18),
        ),
        child: CachedNetworkImage(
          imageUrl: widget.image,
          placeholder: (context, url) => const SizedBox(
            width: 72,
            height: 72,
            child: Center(
              child: CircularProgressIndicator.adaptive(
                valueColor:
                    AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
              ),
            ),
          ),
          errorWidget: (context, url, error) => const SizedBox(
            width: 130,
            height: 130,
            child: Icon(Icons.error),
          ),
        ),
      ),
    );
  }

  TableRow _dataRow(String label, String data, context,
      {bool isPrice = false, bool showPrice = true}) {
    return TableRow(
      children: [
        TableCell(
          verticalAlignment: TableCellVerticalAlignment.middle,
          child: SizedBox(
            height: 20,
            child: Text(
              label,
              style: TextStyle(
                  color: Theme.of(context).textTheme.bodySmall?.color,
                  fontSize: 14),
            ),
          ),
        ),
        TableCell(
          child: SizedBox(
            height: 20,
            child: Text(
              ': ${isPrice && !showPrice ? '-' : data}',
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAceptOrRejectButton(
    BuildContext context,
    price,
    int orderId,
    OrderApprovalBloc approvalBloc,
    OrderBloc orderBloc,
  ) {
    if (widget.canConfirmAgain) {
      return SizedBox(
        height: 26,
        child: OutlinedButton(
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
            backgroundColor: Colors.white,
            foregroundColor: Theme.of(context).primaryColor,
            visualDensity:
                const VisualDensity(vertical: VisualDensity.minimumDensity),
            side: BorderSide(color: Theme.of(context).primaryColor, width: 1),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          onPressed: () {
            final rejectionReason = widget.rejectionReasons.first;
            showDialog(
              context: context,
              builder: (context) => _accepOrRejectDialog(
                reason: rejectionReason.reason,
                context: context,
                orderId: orderId,
                approvalBloc: approvalBloc,
                orderBloc: orderBloc,
                canReply: rejectionReason.canReply,
              ),
            );
          },
          child: Text(
            AppLocalizations.of(context)!.checkOrder.toUpperCase(),
            style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontSize: 11,
                letterSpacing: 0.3,
                fontWeight: FontWeight.bold),
          ),
        ),
      );
    }

    return Row(
      children: [
        SizedBox(
          height: 24,
          width: 56,
          child: TextButton(
            style: TextButton.styleFrom(
              padding: EdgeInsets.zero,
              backgroundColor: Theme.of(context).primaryColor,
              visualDensity:
                  const VisualDensity(vertical: VisualDensity.minimumDensity),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => _confirmDialog(
                  isAccept: true,
                  totalMoney: price,
                  context: context,
                  orderId: orderId,
                  approvalBloc: approvalBloc,
                  orderBloc: orderBloc,
                ),
              );
            },
            child: Text(
              AppLocalizations.of(context)!.accept.toUpperCase(),
              style: const TextStyle(
                  color: Colors.white, fontSize: 11, letterSpacing: 0.5),
            ),
          ),
        ),
        const SizedBox(width: 8),
        SizedBox(
          height: 24,
          width: 56,
          child: TextButton(
            style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                side: const BorderSide(color: Colors.red, width: 1),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                )),
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => _confirmDialog(
                  isAccept: false,
                  totalMoney: price,
                  context: context,
                  orderId: orderId,
                  approvalBloc: approvalBloc,
                  orderBloc: orderBloc,
                ),
              );
            },
            child: Text(
              AppLocalizations.of(context)!.reject.toUpperCase(),
              style: const TextStyle(
                  color: Colors.red, fontSize: 11, letterSpacing: 0.5),
            ),
          ),
        ),
      ],
    );
  }

  _confirmDialog({
    bool isAccept = false,
    required String totalMoney,
    required BuildContext context,
    required int orderId,
    required OrderApprovalBloc approvalBloc,
    required OrderBloc orderBloc,
  }) {
    TextEditingController reasonTextController = TextEditingController();
    String actionText = isAccept
        ? AppLocalizations.of(context)!.accept
        : AppLocalizations.of(context)!.reject;

    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: approvalBloc),
        BlocProvider.value(value: orderBloc),
      ],
      child: BlocListener<OrderApprovalBloc, OrderApprovalState>(
        bloc: approvalBloc,
        listener: (context, state) {
          if (state.status == OrderApprovalStatus.success) {
            showSuccessSnackBar(
                context: context, message: state.successMessage);
            context
                .read<OrderBloc>()
                .add(OrderReload(query: context.read<OrderBloc>().state.query));
            Navigator.of(context, rootNavigator: true).pop();
          }

          if (state.status == OrderApprovalStatus.failure &&
              state.hasApiError) {
            Navigator.of(context, rootNavigator: true).pop();
            showErrorSnackBar(context: context, message: state.errorMessage);
          }
        },
        child: AlertDialog(
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          actionsPadding: const EdgeInsets.only(right: 16, bottom: 16),
          title: Text(isAccept
              ? AppLocalizations.of(context)!.acceptOrderDialogTitle
              : AppLocalizations.of(context)!.rejectOrderDialogTitle),
          content: Column(mainAxisSize: MainAxisSize.min, children: [
            Text(
              AppLocalizations.of(context)!
                  .orderDialogContent(actionText.toLowerCase(), totalMoney),
            ),
            const SizedBox(height: 24),
            // add a form field for rejection reason if isAccept is false
            if (!isAccept)
              DropdownButtonFormField<String>(
                items: [
                  context.loc.dontNeedIt,
                  context.loc.highShipping,
                  context.loc.highPrice,
                ]
                    .toList()
                    .map((reason) => DropdownMenuItem(
                          value: reason,
                          child: Text(reason),
                        ))
                    .toList(),
                onChanged: (value) {
                  reasonTextController.value = TextEditingValue(text: value!);
                },
                decoration: InputDecoration(
                  labelText: context.loc.rejectReason,
                  isDense: true,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            if (!isAccept) const SizedBox(height: 16),
            // show a textarea with height if Other is selected from above dropdown and isAccept is false
            if (!isAccept)
              TextFormField(
                maxLength: 500,
                maxLines: 3,
                controller: reasonTextController,
                decoration: InputDecoration(
                  labelText: "Other",
                  isDense: true,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
          ]),
          actions: [
            BlocBuilder<OrderApprovalBloc, OrderApprovalState>(
              builder: (context, state) {
                return MaterialButton(
                  elevation: 0,
                  padding: EdgeInsets.zero,
                  visualDensity: const VisualDensity(
                      vertical: VisualDensity.minimumDensity),
                  color: isAccept ? Theme.of(context).primaryColor : Colors.red,
                  onPressed: () {
                    if (isAccept) {
                      context
                          .read<OrderApprovalBloc>()
                          .add(AcceptOrder(orderId: orderId));
                    } else {
                      context.read<OrderApprovalBloc>().add(RejectOrder(
                            orderId: orderId,
                            reason: reasonTextController.text,
                          ));
                    }
                  },
                  child: state.status == OrderApprovalStatus.loading
                      ? const CircularProgressIndicator.adaptive()
                      : Text(
                          actionText,
                          style: const TextStyle(color: Colors.white),
                        ),
                );
              },
            ),
            BlocBuilder<OrderApprovalBloc, OrderApprovalState>(
              builder: (context, state) {
                return MaterialButton(
                  elevation: 0,
                  visualDensity: const VisualDensity(
                      vertical: VisualDensity.minimumDensity),
                  onPressed: state.status == OrderApprovalStatus.loading
                      ? null
                      : () {
                          Navigator.of(context, rootNavigator: true).pop();
                        },
                  child: Text(
                    AppLocalizations.of(context)!.notNow,
                  ),
                );
              },
            )
          ],
        ),
      ),
    );
  }

  _accepOrRejectDialog({
    required String reason,
    required BuildContext context,
    required int orderId,
    required OrderApprovalBloc approvalBloc,
    required OrderBloc orderBloc,
    bool canReply = false,
  }) {
    TextEditingController reasonTextController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: approvalBloc),
        BlocProvider.value(value: orderBloc),
      ],
      child: BlocListener<OrderApprovalBloc, OrderApprovalState>(
        bloc: approvalBloc,
        listener: (context, state) {
          if (state.status == OrderApprovalStatus.success) {
            showSuccessSnackBar(
                context: context, message: state.successMessage);
            context
                .read<OrderBloc>()
                .add(OrderReload(query: context.read<OrderBloc>().state.query));
            Navigator.of(context, rootNavigator: true).pop();
          }

          if (state.status == OrderApprovalStatus.failure &&
              state.hasApiError) {
            Navigator.of(context, rootNavigator: true).pop();
            showErrorSnackBar(context: context, message: state.errorMessage);
          }
        },
        child: AlertDialog(
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          actionsPadding: const EdgeInsets.only(right: 16, bottom: 16),
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Align(
                alignment: Alignment.centerLeft,
                child: Text(context.loc.checkOrder),
              ),
              Align(
                alignment: Alignment.centerRight,
                child: IconButton(
                  icon: const Icon(Icons.close),
                  padding: const EdgeInsets.all(0),
                  visualDensity:
                      const VisualDensity(vertical: -4, horizontal: -4),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ),
            ],
          ),
          content: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(reason),
                const SizedBox(height: 24),
                if (canReply)
                  TextFormField(
                    maxLength: 500,
                    maxLines: 3,
                    controller: reasonTextController,
                    validator: (value) {
                      if (canReply && (value == null || value.isEmpty)) {
                        return 'Please enter your comment';
                      }
                      return null;
                    },
                    decoration: InputDecoration(
                      labelText: "Comment",
                      isDense: true,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          actions: [
            BlocBuilder<OrderApprovalBloc, OrderApprovalState>(
              builder: (context, state) {
                return MaterialButton(
                  elevation: 0,
                  padding: EdgeInsets.zero,
                  visualDensity: const VisualDensity(
                      vertical: VisualDensity.minimumDensity),
                  color: Theme.of(context).primaryColor,
                  onPressed: () {
                    if (formKey.currentState!.validate()) {
                      context.read<OrderApprovalBloc>().add(AcceptOrder(
                            orderId: orderId,
                            reason: reasonTextController.text,
                          ));
                    }
                  },
                  child: state.status == OrderApprovalStatus.loading
                      ? const CircularProgressIndicator.adaptive()
                      : Text(
                          AppLocalizations.of(context)!.accept,
                          style: const TextStyle(color: Colors.white),
                        ),
                );
              },
            ),
            BlocBuilder<OrderApprovalBloc, OrderApprovalState>(
              builder: (context, state) {
                return MaterialButton(
                  elevation: 0,
                  visualDensity: const VisualDensity(
                      vertical: VisualDensity.minimumDensity),
                  onPressed: state.status == OrderApprovalStatus.loading
                      ? null
                      : () {
                          if (formKey.currentState!.validate()) {
                            context.read<OrderApprovalBloc>().add(RejectOrder(
                                  orderId: orderId,
                                  reason: reasonTextController.text,
                                ));
                          }
                        },
                  child: Text(
                    AppLocalizations.of(context)!.reject,
                  ),
                );
              },
            )
          ],
        ),
      ),
    );
  }
}

class ImageDialog extends StatelessWidget {
  const ImageDialog({super.key, required this.path});
  final String path;
  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      // elevation: 0,
      child: Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: Image.network(
                path,
                fit: BoxFit.fill,
              ),
            ),
            const SizedBox(height: 16),
            Center(
              child: RawMaterialButton(
                padding: const EdgeInsets.all(16.0),
                fillColor: AppColors.boxDarkColor,
                elevation: 0,
                shape: const CircleBorder(),
                child: const Icon(Ionicons.close),
                onPressed: () {
                  Navigator.of(context, rootNavigator: true).pop();
                },
              ),
            )
          ],
        ),
      ),
    );
  }
}
