# User Agent Manager

The `UserAgentManager` is a singleton class designed to help avoid website blocking by providing randomized, realistic mobile user agents for WebView instances.

## Features

### 1. Randomized User Agents
- Provides a pool of 20+ realistic mobile user agents
- Includes iOS Safari, Android Chrome, Samsung Internet, Firefox, and Edge
- All user agents are mobile-optimized for the app's use case

### 2. Website-Specific Optimization
- Automatically selects optimal user agents for specific websites:
  - **Shein**: Uses iOS Safari user agents (best compatibility)
  - **Amazon**: Uses Chrome user agents (best performance)
  - **AliExpress**: Uses Android Chrome user agents (best compatibility)
  - **Other sites**: Uses random user agents

### 3. Version Randomization
- Automatically randomizes browser version numbers to avoid fingerprinting
- Supports Chrome, Safari, iOS, and Android version randomization
- Keeps versions within realistic ranges

### 4. Session Management
- Maintains consistent user agent within a session
- Allows session reset for new browsing sessions
- Tracks current user agent for debugging

### 5. Anti-Detection Features
- Provides additional HTTP headers to avoid detection
- Includes methods to detect potentially blocked user agents
- Avoids common automation signatures

## Usage

### Basic Usage

```dart
import 'package:goldenprizma/helpers/user_agent_manager.dart';

// Get a random user agent
final userAgent = UserAgentManager().getRandomUserAgent();

// Set it on WebViewController
controller.setUserAgent(userAgent);
```

### Website-Specific User Agents

```dart
// Get optimized user agent for a specific website
final userAgent = UserAgentManager().getUserAgentForWebsite('https://m.shein.com/product');
controller.setUserAgent(userAgent);
```

### Randomized User Agents (Recommended)

```dart
// Get a randomized version of a website-specific user agent
final baseUserAgent = UserAgentManager().getUserAgentForWebsite(url);
final randomizedUserAgent = UserAgentManager().getRandomizedUserAgent(baseUserAgent);
controller.setUserAgent(randomizedUserAgent);
```

### Session Management

```dart
final manager = UserAgentManager();

// Get consistent user agent for the session
final sessionUserAgent = manager.getSessionUserAgent();

// Reset session (will generate new user agent on next call)
manager.resetSession();

// Generate new session user agent immediately
final newSessionUserAgent = manager.getNewSessionUserAgent();
```

### Anti-Detection Headers

```dart
final headers = UserAgentManager().getAntiDetectionHeaders();
// Use these headers with HTTP requests for better compatibility
```

## Implementation in WebViews

The user agent manager is automatically integrated into:

1. **PasteLinkBrowserScreen**: Uses website-specific randomized user agents
2. **WebsiteLauncher**: Uses website-specific randomized user agents

Both implementations:
- Select optimal user agents based on the target website
- Apply version randomization to avoid fingerprinting
- Log the selected user agent for debugging

## Supported User Agents

### iOS Safari
- iPhone iOS 17.1, 17.0, 16.6, 16.5
- iPad iOS 17.1, 16.6

### Android Chrome
- Android 14, 13, 12, 11 with latest Chrome versions
- Various device models (Samsung Galaxy, Pixel)

### iOS Chrome (CriOS)
- iPhone with Chrome on iOS 17.1, 16.6

### iOS Firefox (FxiOS)
- iPhone with Firefox on iOS 17.1, 16.6

### Android Samsung Internet
- Samsung devices with Samsung Internet browser

### Android Firefox
- Mobile Firefox on Android

### Android Edge
- Microsoft Edge on Android devices

## Best Practices

1. **Use website-specific user agents** when possible for better compatibility
2. **Apply randomization** to avoid fingerprinting detection
3. **Reset sessions** between different browsing contexts
4. **Monitor logs** to see which user agents are being used
5. **Test with different user agents** if a website blocks requests

## Debugging

Enable debug logging to see which user agents are selected:

```dart
debugPrint('Using randomized user agent for $url: $userAgent');
```

The user agent selection and randomization process is logged automatically in both WebView implementations.

## Testing

The user agent manager includes comprehensive tests covering:
- Random user agent generation
- Website-specific selection
- Version randomization
- Session management
- Anti-detection features
- Mobile-friendly validation

Run tests with:
```bash
flutter test test/helpers/user_agent_manager_test.dart
```
