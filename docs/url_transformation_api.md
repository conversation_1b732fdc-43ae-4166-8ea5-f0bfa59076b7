# URL Transformation API Documentation

## Overview
The URL Transformation feature allows dynamic redirection of e-commerce URLs to Turkish versions without requiring app updates. This is particularly useful for mobile/share links that don't include country codes.

## Backend API Endpoint

### GET `/api/v1/url-transformations`

Returns transformation rules that the mobile app will use to redirect URLs.

#### Response Format

```json
{
  "rules": [
    {
      "domain": "zara.com",
      "source_pattern": "*/share/*",
      "target_pattern": "/tr/tr",
      "decode_url": false,
      "priority": 10
    },
    {
      "domain": "zara.com",
      "source_pattern": "*/?go=https%3A//www.zara.com/share/*",
      "target_pattern": "/tr/tr/",
      "decode_url": true,
      "priority": 5
    }
  ],
  "version": 1,
  "last_updated": "2024-01-15T10:30:00Z"
}
```

#### Field Descriptions

- **domain**: Domain pattern to match (supports wildcards like `*.shein.com`)
- **source_pattern**: URL pattern to match (supports wildcards `*`)
- **target_pattern**: Target URL path to redirect to (e.g., `/tr/tr`, `/tr/en`)
- **decode_url**: Whether to decode URL-encoded parameters before processing
- **priority**: Rule priority (higher number = higher priority)
- **version**: Rules version number for caching
- **last_updated**: ISO timestamp of last update

## Example Transformations

### Zara Examples

**Input:** `https://www.zara.com/share/-p20110709.html`
**Output:** `https://www.zara.com/tr/tr/-p20110709.html`

**Input:** `https://www.zara.com/?go=https%3A//www.zara.com/share/-p20110709.html`
**Output:** `https://www.zara.com/tr/tr/-p20110709.html`

### Shein Examples

**Input:** `https://m.shein.com/share/product123.html`
**Output:** `https://m.shein.com/tr/product123.html`

## Pattern Matching

### Wildcards
- `*` matches any sequence of characters
- `*.domain.com` matches any subdomain of domain.com
- `/*/product/*` matches any path with "product" in the middle

### Priority System
When multiple rules match the same URL, the rule with the highest priority is used.

## Mobile App Implementation

### Caching Strategy
- Rules are cached locally for 24 hours
- App falls back to cached rules if network fails
- Empty rules returned if no cache available

### Error Handling
- Original URL used if transformation fails
- Graceful fallback ensures app continues working
- Debug logs for troubleshooting

### Integration Points
- Rules loaded on app startup
- Transformation applied before WebView loading
- Transformed URL used for order creation

## Backend Considerations

### Performance
- Endpoint should be fast (< 200ms response time)
- Consider CDN caching for global distribution
- Implement proper HTTP caching headers

### Versioning
- Increment version number when rules change
- Mobile app can check version for updates
- Backward compatibility maintained

### Monitoring
- Track transformation success rates
- Monitor rule effectiveness
- Log failed transformations for analysis

## Testing

### Test Cases
1. **Basic transformation**: Simple share link to Turkish version
2. **URL decoding**: Encoded URLs properly decoded
3. **Product ID extraction**: Product IDs preserved in transformation
4. **Priority handling**: Higher priority rules take precedence
5. **Fallback behavior**: Original URL used when transformation fails
6. **Cache behavior**: Cached rules used when network unavailable

### Example Test Data
```json
{
  "test_cases": [
    {
      "input": "https://www.zara.com/share/-p20110709.html",
      "expected": "https://www.zara.com/tr/tr/-p20110709.html",
      "rule_matched": "zara.com share pattern"
    },
    {
      "input": "https://unsupported-site.com/product/123",
      "expected": "https://unsupported-site.com/product/123",
      "rule_matched": "none (fallback to original)"
    }
  ]
}
```

## Security Considerations

- Validate all transformation rules on backend
- Prevent malicious redirects to external domains
- Sanitize URL patterns to prevent injection attacks
- Rate limit the API endpoint

## Future Enhancements

- A/B testing different transformation strategies
- Analytics on transformation effectiveness
- Dynamic rule updates based on website changes
- Support for more complex transformation logic
