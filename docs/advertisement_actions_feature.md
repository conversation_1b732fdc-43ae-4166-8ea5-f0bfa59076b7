# Advertisement Actions Feature

## Overview

This feature implements dynamic advertisement actions based on the `app_action` field in advertisement data. When users tap on advertisements in carousels or advertisement lists, the app will check the `app_action` field and decide what action to take.

## Supported Actions

### 1. External URL (`external_url`)
- **Purpose**: Opens external websites or URLs
- **Data**: Uses `app_action_data` field for the URL, falls back to `url` field
- **Behavior**: Opens the URL in WebsiteLauncher with URL transformation applied

**Example Data:**
```json
{
  "id": 58,
  "url": "https://google.com",
  "image": "https://golden.test/advertisements_image/KRPJMlAVM5.jpg",
  "app_action": "external_url",
  "app_action_data": "https://google.com"
}
```

### 2. Page (`page`)
- **Purpose**: Opens internal app pages or specific website pages
- **Data**: Uses the `url` field for navigation
- **Behavior**: Opens the URL in WebsiteLauncher with URL transformation applied

**Example Data:**
```json
{
  "id": 60,
  "url": "https://golden.test/pages/cerave",
  "image": "https://golden.test/advertisements_image/j11nFY0s3M.jpg",
  "app_action": "page",
  "app_action_data": "cerave"
}
```

### 3. Brand Store (`brand_store`)
- **Purpose**: Navigates to store tab with brand filter applied
- **Data**: Uses `app_action_data` for brand ID, `note` field for brand name
- **Behavior**: Sets brand filter in StoreFilterCubit and switches to store tab (preserves bottom navigation)

**Example Data:**
```json
{
  "id": 59,
  "url": "https://golden.test/store?brand=1",
  "image": "https://golden.test/advertisements_image/O462KLSpG5.jpg",
  "app_action": "brand_store",
  "app_action_data": "1",
  "note": "Kerastase"
}
```

## Implementation Details

### Core Service
- **File**: `lib/services/advertisement_action_service.dart`
- **Class**: `AdvertisementActionService`
- **Pattern**: Singleton service for handling advertisement actions

### Key Methods
- `handleAdvertisementTap()`: Main method for handling Advertisement objects
- `handleSlideTap()`: Method for handling Slide objects (home slides)
- `getActionDescription()`: Utility method for debugging/logging

### Updated Components
1. **Advertisement List View** (`lib/presentation/widgets/advertisement_list_view.dart`)
2. **Advertisement Card Grid** (`lib/presentation/widgets/advertisement_card_grid.dart`)
3. **Slide Widget** (`lib/presentation/widgets/slider_widget.dart`)
4. **Home Slide Widget** (`lib/presentation/widgets/home_slide_widget.dart`)

### Advertisement Model Enhancements
The `Advertisement` model already includes helper methods:
- `isExternalUrl`: Checks if action is external_url
- `isBrandStore`: Checks if action is brand_store
- `isPageAction`: Checks if action is page
- `brandId`: Extracts brand ID from app_action_data
- `pageSlug`: Gets page slug from app_action_data
- `externalUrl`: Gets external URL from app_action_data

## Navigation Behavior

### Brand Store Navigation
- **Problem**: Using `Navigator.push()` hides bottom navigation and app bar
- **Solution**: Navigate to HomeScreen with store tab selected (index 1)
- **Implementation**:
  1. Pop to root route if needed
  2. Push replacement route to HomeScreen with `initialPage: 1`
  3. Brand filter is already set before navigation
- **Result**: User sees store page with bottom navigation preserved

## Error Handling

### Fallback Behavior
- If an unknown action is encountered, falls back to opening the `url` field
- If brand filtering fails, falls back to opening the `url` field
- All navigation checks for context.mounted to prevent async context issues

### Context Safety
- All async operations check `context.mounted` before navigation
- Prevents "Don't use BuildContext across async gaps" warnings
- Ensures safe navigation even if widget is disposed

## Testing

### Unit Tests
- **File**: `test/services/advertisement_action_service_test.dart`
- **Coverage**: Tests all action types and helper methods
- **Status**: All tests passing ✅

### Test Cases
1. External URL action identification and data extraction
2. Brand store action identification and brand ID parsing
3. Page action identification and slug extraction
4. Action description generation
5. Unknown action fallback behavior

## Usage Examples

### Basic Advertisement Handling
```dart
// In any widget with advertisement tap handling
onTap: () {
  AdvertisementActionService().handleAdvertisementTap(context, advertisement);
}
```

### Home Slide Handling
```dart
// For home slides that don't have app actions yet
onTap: () {
  AdvertisementActionService().handleSlideTap(context, slide);
}
```

### Debugging
```dart
// Get action description for logging
final description = AdvertisementActionService().getActionDescription(advertisement);
debugPrint('Advertisement action: $description');
```

## Future Enhancements

### Potential New Actions
- `product_detail`: Navigate to specific product pages
- `category_store`: Navigate to store with category filter
- `deep_link`: Handle app deep links
- `custom_action`: Execute custom business logic

### Home Slide Enhancement
Currently, home slides use the simple `Slide` model without app action support. Future enhancement could:
1. Update the backend to return Advertisement objects for home slides
2. Add app_action fields to the Slide model
3. Implement full action support for home slides

## Migration Notes

### Backward Compatibility
- Existing advertisements without app_action field default to 'external_url'
- All existing functionality continues to work unchanged
- No breaking changes to existing API contracts

### Performance Impact
- Minimal performance impact as service is singleton
- URL transformation is applied consistently
- Brand filtering uses existing StoreFilterCubit infrastructure
